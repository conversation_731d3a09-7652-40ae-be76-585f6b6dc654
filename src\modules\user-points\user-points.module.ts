import { forwardRef, Module } from '@nestjs/common';
import { PointRedemptionModule } from '../point-redemption/point-redemption.module';
import { UserPointsProvider } from './providers/user-points.provider';
import { UserPointsRepository } from './repositories';
import { UserPointsController } from './rest';
import { UserPointsService } from './services';
import { HelperPointsRepository } from './services/helper.points.repository';

@Module({
  imports: [forwardRef(() => PointRedemptionModule)],
  controllers: [UserPointsController],
  providers: [
    UserPointsService,
    UserPointsRepository,
    HelperPointsRepository,
    UserPointsProvider,
  ],
  exports: [
    UserPointsService,
    UserPointsRepository,
    HelperPointsRepository,
    UserPointsProvider,
  ],
})
export class UserPointsModule {}
