import { ApiProperty } from "@nestjs/swagger";
import { Expose } from "class-transformer";
import { IsNotEmpty, IsString } from "class-validator";
import { ServiceSuccessResponse } from "src/helper/serviceResponse/service.response.interface";
import { CoachSubscriptionCancelStatus, CoachSubscriptionRefundStatus } from "../../common/entities/coach-program.entity";

class CoachProgramPriceResponseDto {

  @Expose()
  @ApiProperty({ required: true, type: Number, description: 'Actual price of the program' })
  actualPrice: number;

  @Expose()
  @ApiProperty({ required: true, type: Number, description: 'Discounted price of the program' })
  discountedPrice: number;

}

export class GetSubscriptionResponseDtoForCoach {
  @Expose()
  @ApiProperty({ required: true, type: String, description: 'Subscription ID' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @Expose()
  @ApiProperty({
    required: true,
    type: String,
    description: 'Corresponding user id',
  })
  userId: string;

  @Expose()
  @ApiProperty({
    required: true,
    type: String,
    description: 'Corresponding user name',
  })
  userName: string;

  @Expose()
  @ApiProperty({
    required: true,
    type: String,
    description: 'Corresponding user image url',
  })
  userImage?: string;

  @Expose()
  @ApiProperty({
    required: true,
    type: String,
    description: 'Corresponding program id',
  })
  programId: string;

  @Expose()
  @ApiProperty({
    required: true,
    type: String,
    description: 'Corresponding program name',
  })
  programName: string;

  @Expose()
  @ApiProperty({
    required: false,
    type: String,
    description: 'My Payment Term',
  })
  paymentTerm?: string;

  @Expose()
  @ApiProperty({
    required: true,
    type: CoachProgramPriceResponseDto,
    description: 'Corresponding program - one time price',
  })
  oneTimePrice?: CoachProgramPriceResponseDto;

  @Expose()
  @ApiProperty({
    required: true,
    type: CoachProgramPriceResponseDto,
    description: 'Corresponding program - one time price',
  })
  dailyPrice?: CoachProgramPriceResponseDto;

  @Expose()
  @ApiProperty({
    required: true,
    type: CoachProgramPriceResponseDto,
    description: 'Corresponding program - one time price',
  })
  weeklyPrice?: CoachProgramPriceResponseDto;

  @Expose()
  @ApiProperty({
    required: true,
    type: CoachProgramPriceResponseDto,
    description: 'Corresponding program - monthly price',
  })
  monthlyPrice?: CoachProgramPriceResponseDto;

  @Expose()
  @ApiProperty({
    required: true,
    type: Boolean,
    description: 'Whether a payment is needed or not',
  })
  isPaymentNeeded: boolean;

  @Expose()
  @ApiProperty({
    required: true,
    type: Boolean,
    description: 'Whether user have paid at least once',
  })
  isFirstPaymentDone: boolean;

  @Expose()
  @ApiProperty({
    required: false,
    enum: CoachSubscriptionCancelStatus,
    description: 'if subscription canceled then by whom',
  })
  cancelStatus?: string;

  @Expose()
  @ApiProperty({
    required: false,
    enum: CoachSubscriptionRefundStatus,
    description: "if refund requested - then what's the status",
  })
  refundStatus?: string;

  @Expose()
  @ApiProperty({
    required: true,
    type: Date,
    description: 'Corresponding subscription date',
  })
  subscriptionDate: Date;

  @Expose()
  @ApiProperty({
    required: true,
    type: Date,
    description: 'Represents when the next payment date is',
  })
  nextPaymentDate: Date;

  @Expose()
  @ApiProperty({
    required: true,
    type: Number,
    description: 'Corresponding user total paid so far for this subscription',
  })
  userTotalPaid: number;

  @Expose()
  @ApiProperty({
    required: true,
    type: Number,
    description: 'the amount that the coach already got',
  })
  coachAlreadyGot: number;

  @Expose()
  @ApiProperty({
    required: false,
    type: String,
    description: 'My rating id to this program',
  })
  programRatingId?: string;

  @Expose()
  @ApiProperty({
    required: false,
    type: String,
    description: 'My rating id to this coach profile',
  })
  coachRatingId?: string;

  @Expose()
  @ApiProperty({
    required: true,
    type: Boolean,
    description: 'Whether the subscriptionis completed or expired',
  })
  isCompleted: boolean;

}

export class GetSubscriptionSuccessResponseDtoForCoach implements ServiceSuccessResponse {

  @Expose()
  @ApiProperty({ required: true, type: GetSubscriptionResponseDtoForCoach, description: 'Single subscription details' })
  data: GetSubscriptionResponseDtoForCoach;

}


export class GetSubscribersSuccessResponseDtoForCoach implements ServiceSuccessResponse {
  @Expose()
  @ApiProperty({ required: true, type: [GetSubscriptionResponseDtoForCoach], description: 'List of subscription details' })
  data: GetSubscriptionResponseDtoForCoach[]; // Now it's an array!
}