import { Injectable } from '@nestjs/common';
import { randomUUID } from 'crypto';
import { VersionModel } from 'src/database/version/version.model';
import { UpdateVersionRequest, Version } from 'src/entity/version';

@Injectable()
export class VersionRepository {

  async getVersion(query: Record<string, any>): Promise<Version | null> {
    try {
      return await VersionModel.findOne(query)
        .select('-_id')
        .lean();
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async getLatestVersion(): Promise<Version | null> {
    try {
      return await VersionModel.findOne()
        .sort({ createdAt: -1 })
        .select('-_id')
        .lean();
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async createVersion(data: Version): Promise<Version | null> {
    try {
      data.id = randomUUID();
      const version = await VersionModel.create(data);
      return version?.toObject();
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async updateVersion(
    query: Record<string, any>,
    data: UpdateVersionRequest,
  ): Promise<Version | null> {
    try {
      return await VersionModel.findOneAndUpdate(
        query,
        { $set: data },
        { new: true },
      )
        .select('-_id')
        .lean();
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async deleteVersion(query: Record<string, any>): Promise<boolean> {
    try {
      const result = await VersionModel.deleteOne(query);
      return result.deletedCount > 0;
    } catch (error: any) {
      console.log(error.message);
      return false;
    }
  }
}
