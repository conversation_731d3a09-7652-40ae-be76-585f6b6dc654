import { Injectable } from "@nestjs/common";
import { CoachProgramEntity } from "../../common/entities/coach-program.entity";
import { CoachProgramRepositoryForInternal } from "../repositories/internal.repository";

@Injectable()
export class CoachProgramProviderForInternal {

  constructor(private readonly repository: CoachProgramRepositoryForInternal) { }

  async onPaymentSuccessful(programId: string, amount: number): Promise<CoachProgramEntity | null> {
    return this.repository.onPaymentSuccessful(programId, amount);
  }

  async onRefundSuccessful(programId: string, amount: number): Promise<CoachProgramEntity | null> {
    return this.repository.onRefundSuccessful(programId, amount);
  }
}