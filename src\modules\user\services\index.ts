import { HttpStatus, Injectable } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { authConfig } from 'config/auth';
import {
  AddUserNewAddressErrorMessages,
  AddUserNewAddressSuccessResponse,
  DeleteUserAddressErrorMessages,
  DeleteUserAddressSuccessResponse,
  FitBuddiesStatus,
  FitBuddiesType,
  GetUserInformationErrorMessages,
  GetUserInformationSuccessResponse,
  IDeleteUserAccountReqByAdmin,
  IIndexNameEnum,
  IOrderAddress,
  PackageFeatureEnum,
  ProfileVisibilityEnum,
  RelationStatusEnum,
  SendOtpErrorMessages,
  SendOtpRequest,
  SendOtpSuccessResponse,
  SpotProfile,
  UpdateUserAddressErrorMessages,
  UpdateUserAddressSuccessResponse,
  UpdateUserErrorMessages,
  UpdateUserLocationErrorMessages,
  UpdateUserLocationSuccessResponse,
  UpdateUserRequestBody,
  UpdateUserSuccessResponse,
  UserChangeEmailErrorMessages,
  UserChangeEmailSuccessMessages,
  UserChangePasswordErrorMessages,
  UserChangePasswordRequest,
  UserChangePasswordSuccessMessage,
  UserChangePasswordSuccessResponse,
  VerifyOtpErrorMessages,
  VerifyOtpRequest,
  VerifyOtpSuccessResponse,
} from 'models';
import { Otp } from 'src/entity/otp';
import { UserAddress } from 'src/entity/user';
import { Helper } from 'src/helper/helper.interface';
import { APIException } from 'src/internal/exception/api.exception';
import { ESearchDbService } from 'src/modules/global-search/services/elastic.db.service';
import { UserDailyActivityService } from 'src/modules/package/services/user.dailyActivity.service';
import { EasypostAddressService } from 'src/modules/shipping/services/easypost.address.service';
import { SpotProfileRepository } from 'src/modules/spot/repositories/spotProfile';
import { QueueInstance } from 'src/queue-system';
import {
  DeleteAccountQueueName,
  QueuePayloadType,
} from 'src/queue-system/predefined-data';
import { successResponse } from 'src/utils/response';
import { IServiceResponse } from 'src/utils/response/service.response.interface';
import { UserRepository } from '../repositories';

@Injectable()
export class UserService {
  constructor(
    private userRepo: UserRepository,
    private spotProfileRepo: SpotProfileRepository,
    private helper: Helper,
    private readonly eSearchDbService: ESearchDbService,
    private readonly easypostAddressService: EasypostAddressService,
    private readonly userDailyActivityService: UserDailyActivityService,
  ) {}

  async getUser(userId: string): Promise<GetUserInformationSuccessResponse> {
    const user = await this.userRepo.findUser({ id: userId });

    // console.log('user profile',user)

    if (!user)
      throw new APIException(
        GetUserInformationErrorMessages.USER_NOT_FOUND,
        'USER_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    return this.helper.serviceResponse.successResponse(
      await this.userRepo.userInfoExtension(user),
    );
  }

  private async getRelationStatus(
    userId: string,
    buddyId: string,
  ): Promise<RelationStatusEnum> {
    const buddy = await this.userRepo.findFitBuddy({
      $or: [
        {
          userId,
          fitBuddyId: buddyId,
        },
        {
          userId: buddyId,
          fitBuddyId: userId,
        },
      ],
    });

    if (buddy && buddy.status === FitBuddiesStatus.BLOCKED)
      throw new APIException(
        GetUserInformationErrorMessages.CAN_NOT_SEE_THE_USER_INFO,
        'CAN_NOT_SEE_THE_USER_INFO',
        HttpStatus.BAD_REQUEST,
      );

    let relationStatus = RelationStatusEnum.NOT_SPOTTED;
    if (buddy) {
      if (buddy.type === FitBuddiesType.FIT_BUDDY) {
        relationStatus = RelationStatusEnum.SPOTTED;
      } else if (buddy.type === FitBuddiesType.FOLLOWER) {
        if (buddy.fitBuddyId === userId) {
          relationStatus = RelationStatusEnum.REQUESTED;
        } else {
          relationStatus = RelationStatusEnum.SPOT_BACK;
        }
      }
    }
    return relationStatus;
  }

  async getUserInfo(
    myId: string,
    userId: string,
  ): Promise<GetUserInformationSuccessResponse> {
    const user = await this.userRepo.findUser(
      { id: userId },
      { addresses: 0, email: 0, location: 0, phone: 0 },
    );
    if (!user)
      throw new APIException(
        GetUserInformationErrorMessages.USER_NOT_FOUND,
        'USER_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );

    const preference = await this.userRepo.getUserPreference(userId);

    const profileVisibility =
      preference?.privacy?.profileVisibility || ProfileVisibilityEnum.PUBLIC;

    const relationStatus = await this.getRelationStatus(myId, userId);

    // checking subscription
    if (myId !== userId && relationStatus !== RelationStatusEnum.SPOTTED) {
      const handlePackage = await this.userDailyActivityService.verifyPackage(
        myId,
        PackageFeatureEnum.PROFILE_VIEW,
        userId,
      );
    }

    return this.helper.serviceResponse.successResponse({
      ...(await this.userRepo.userInfoExtension(user)),
      profileVisibility,
      relationStatus,
    });
  }

  async updateUser(
    userId: string,
    data: UpdateUserRequestBody,
  ): Promise<UpdateUserSuccessResponse> {
    let user = await this.userRepo.findUser({ id: userId });
    if (!user)
      throw new APIException(
        GetUserInformationErrorMessages.USER_NOT_FOUND,
        'USER_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );

    if (data.phone) {
      const { phone } = data;
      const doesPhoneExists = await this.userRepo.findUser({ phone });
      if (doesPhoneExists)
        throw new APIException(
          UpdateUserErrorMessages.PHONE_ALREADY_EXISTS,
          'PHONE_ALREADY_EXISTS',
          HttpStatus.BAD_REQUEST,
        );
    }

    user = Object.assign(user, data);

    const updatedUser = await this.userRepo.updateUser(userId, user);
    if (!updatedUser) {
      throw new APIException(
        UpdateUserErrorMessages.CAN_NOT_UPDATE_USER_INFORMATION,
        'CAN_NOT_UPDATE_USER_INFORMATION',
        HttpStatus.BAD_REQUEST,
      );
    } else {
      if (updatedUser) {
        //update user's maxBench and MaxSquat
        const spotProfile: Partial<SpotProfile> = {};
        if (data?.maxSquat) spotProfile.maxSquat = data?.maxSquat;
        if (data?.maxBench) spotProfile.maxBench = data?.maxBench;
        if (data?.weightType) spotProfile.weightType = data?.weightType;
        spotProfile.name = updatedUser.name;
        await this.userRepo.updateSpotProfileByUserId(userId, spotProfile);
      }
    }
    this.eSearchDbService.insertOneItemToES(IIndexNameEnum.USER, userId);
    return this.helper.serviceResponse.successResponse(
      await this.userRepo.userInfoExtension(updatedUser),
    );
  }

  async validateAddress(userId: string, address: IOrderAddress) {
    const isVerified = await this.easypostAddressService.validateAddress(
      address,
    );
    return successResponse(null, isVerified);
  }

  async addUserNewAddress(
    userId: string,
    address: UserAddress,
  ): Promise<AddUserNewAddressSuccessResponse> {
    const updatedUser = await this.userRepo.addUserNewAddress(userId, address);
    if (!updatedUser)
      throw new APIException(
        AddUserNewAddressErrorMessages.CAN_NOT_ADD_USER_NEW_ADDRESS,
        'CAN_NOT_ADD_USER_NEW_ADDRESS',
        HttpStatus.BAD_REQUEST,
      );

    if (
      address.isDefault &&
      updatedUser.addresses.length &&
      updatedUser.addresses.length > 1
    ) {
      let check = false;
      updatedUser.addresses.forEach((address) => {
        if (
          address.isDefault &&
          updatedUser.addresses[updatedUser.addresses.length - 1].id !==
            address.id
        ) {
          address.isDefault = false;
          check = true;
        }
      });
      check && (await this.userRepo.updateUser(userId, updatedUser));
    }
    return this.helper.serviceResponse.successResponse(updatedUser);
  }

  async updateUserAddress(
    userId: string,
    addressId: string,
    address: UserAddress,
  ): Promise<UpdateUserAddressSuccessResponse> {
    const updatedUser = await this.userRepo.updateUserAddress(
      userId,
      addressId,
      address,
    );
    if (!updatedUser)
      throw new APIException(
        UpdateUserAddressErrorMessages.CAN_NOT_UPDATE_USER_ADDRESS,
        'CAN_NOT_UPDATE_USER_ADDRESS',
        HttpStatus.BAD_REQUEST,
      );

    if (
      address.isDefault &&
      updatedUser.addresses.length &&
      updatedUser.addresses.length > 1
    ) {
      let check = false;
      updatedUser.addresses.forEach((address) => {
        if (address.isDefault && addressId !== address.id) {
          address.isDefault = false;
          check = true;
        }
      });
      check && (await this.userRepo.updateUser(userId, updatedUser));
    }

    return this.helper.serviceResponse.successResponse(updatedUser);
  }

  async updateUserLocation(
    userId: string,
    latitude: number,
    longitude: number,
  ): Promise<UpdateUserLocationSuccessResponse> {
    if (!latitude || !longitude) {
      throw new APIException(
        UpdateUserLocationErrorMessages.INVALID_LOCATION_POINTS,
        'INVALID_LOCATION_POINTS',
        HttpStatus.BAD_REQUEST,
      );
    }
    const updatedUser = await this.userRepo.updateUserLocation(
      userId,
      latitude,
      longitude,
    );

    if (updatedUser) {
      // update spot profile's location with user location change
      const profile = {
        location: {
          type: 'Point',
          coordinates: [longitude, latitude],
        },
      };
      await this.spotProfileRepo.updateSpotProfileByUserId(userId, profile);
    } else {
      throw new APIException(
        UpdateUserLocationErrorMessages.USER_LOCATION_UPDATED_FAILED,
        'USER_LOCATION_UPDATED_FAILED',
        HttpStatus.BAD_REQUEST,
      );
    }
    return this.helper.serviceResponse.successResponse(updatedUser);
  }

  async deleteUserAddress(
    userId: string,
    addressId: string,
  ): Promise<DeleteUserAddressSuccessResponse> {
    const user = await this.userRepo.findUser({ id: userId });
    if (!user)
      throw new APIException(
        GetUserInformationErrorMessages.USER_NOT_FOUND,
        'USER_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );

    const updatedUser = await this.userRepo.deleteUserAddress(
      userId,
      addressId,
    );
    if (!updatedUser)
      throw new APIException(
        DeleteUserAddressErrorMessages.CAN_NOT_DELETE_USER_ADDRESS,
        'CAN_NOT_DELETE_USER_ADDRESS',
        HttpStatus.BAD_REQUEST,
      );
    return this.helper.serviceResponse.successResponse(updatedUser);
  }

  async changePassword(
    userId: string,
    data: UserChangePasswordRequest,
  ): Promise<UserChangePasswordSuccessResponse> {
    try {
      const { currentPassword, newPassword } = data;
      const user = await this.userRepo.getUserPassword({
        id: userId,
      });
      if (!user)
        throw new APIException(
          UserChangePasswordErrorMessages.INVALID_USER,
          'INVALID_USER',
          HttpStatus.BAD_REQUEST,
        );

      const doesPasswordMatch = await bcrypt.compare(
        currentPassword,
        user.password,
      );
      if (!doesPasswordMatch)
        throw new APIException(
          UserChangePasswordErrorMessages.CURRENT_PASSWORD_IS_INCORRECT,
          'CURRENT_PASSWORD_IS_INCORRECT',
          HttpStatus.BAD_REQUEST,
        );

      // hash the new password
      user.password = await bcrypt.hash(newPassword, authConfig.salt);

      // update the user password
      const updatedUser = await this.userRepo.updateUser(user.id, user);
      if (!updatedUser)
        throw new APIException(
          UserChangePasswordErrorMessages.PASSWORD_CHANGED_FAILED,
          'PASSWORD_CHANGED_FAILED',
          HttpStatus.BAD_REQUEST,
        );
      return this.helper.serviceResponse.successResponse({
        message: UserChangePasswordSuccessMessage.PASSWORD_CHANGED_SUCCESSFUL,
      });
    } catch (error: any) {
      console.log(error.message);
      throw new APIException(
        UserChangePasswordErrorMessages.PASSWORD_CHANGED_FAILED,
        'PASSWORD_CHANGED_FAILED',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async changeEmailSendOTP(
    data: SendOtpRequest,
  ): Promise<SendOtpSuccessResponse> {
    const query = this.otpQuery(data);
    const doesUserExist = await this.userRepo.findUser(query);
    if (doesUserExist)
      throw new APIException(
        UserChangeEmailErrorMessages.EMAIL_ALREADY_EXISTS,
        'EMAIL_ALREADY_EXISTS',
        HttpStatus.FORBIDDEN,
      );
    return this.sendOTP(data);
  }

  async sendOTP(data: SendOtpRequest): Promise<SendOtpSuccessResponse> {
    const query = this.otpQuery(data);
    const otpExists = await this.userRepo.findOtp(query);
    const randomOtp = 100000 + Math.floor(Math.random() * 900000);

    if (otpExists) {
      const otpUpdated = await this.userRepo.updateOtp(query, {
        otp: randomOtp,
        otpExpireTime: Date.now() + authConfig.change_email.otp_expiration_time,
        isVerified: false,
      });
      if (!otpUpdated)
        throw new APIException(
          SendOtpErrorMessages.CAN_NOT_UPDATE_OTP,
          'CAN_NOT_UPDATE_OTP',
          HttpStatus.BAD_REQUEST,
        );
    } else {
      const otpSend = await this.userRepo.sendOtp({
        ...data,
        otp: randomOtp,
        otpExpireTime: Date.now() + authConfig.change_email.otp_expiration_time,
      });
      if (!otpSend)
        throw new APIException(
          SendOtpErrorMessages.CAN_NOT_SEND_OTP,
          'CAN_NOT_SEND_OTP',
          HttpStatus.BAD_REQUEST,
        );
    }

    // send otp in email
    if (data?.email) {
      const mailBody = this.otpEmailBody(data.email, randomOtp);
      this.helper.mailService.sendMail(
        data.email,
        `${authConfig.signup.otp_subject}`,
        // `${authConfig.signup.otp_body_prefix} ${randomOtp}`,
        mailBody,
      );
    }
    // send otp in phone
    if (data?.phone) {
      const otpSmsBody = this.otpSmsBody(randomOtp);
      this.helper.smsService.sendSms(otpSmsBody, data.phone);
    }
    return this.helper.serviceResponse.successResponse({
      message: `Your FITSOMNIA OTP is send to Email`,
    });
  }

  async changeEmailVerifyOTP(
    userId: string,
    data: VerifyOtpRequest,
  ): Promise<VerifyOtpSuccessResponse> {
    const query = this.otpQuery(data);
    const doesUserExist = await this.userRepo.findUser(query);
    if (doesUserExist)
      throw new APIException(
        UserChangeEmailErrorMessages.EMAIL_ALREADY_EXISTS,
        'EMAIL_ALREADY_EXISTS',
        HttpStatus.BAD_REQUEST,
      );

    const verifiedOTP = await this.verifyOtp(data);
    if (!verifiedOTP)
      throw new APIException(
        VerifyOtpErrorMessages.OTP_EXPIRED_OR_INVALID_OTP,
        'OTP_EXPIRED_OR_INVALID_OTP',
        HttpStatus.BAD_REQUEST,
      );

    const user = await this.userRepo.findUser({ id: userId });
    if (data?.email) {
      user.email = data.email;
    }
    if (data?.phone) {
      user.phone = data.phone;
    }
    const updatedEmail = await this.userRepo.updateUser(userId, user);
    if (!updatedEmail)
      throw new APIException(
        UserChangeEmailErrorMessages.CAN_NOT_UPDATE_USER_NEW_EMAIL,
        'CAN_NOT_UPDATE_USER_NEW_EMAIL',
        HttpStatus.BAD_REQUEST,
      );

    await this.userRepo.deleteOtp(query);
    return this.helper.serviceResponse.successResponse({
      message: UserChangeEmailSuccessMessages.CHANGE_EMAIL_SUCCESSFUL,
    });
  }

  async verifyOtp(data: VerifyOtpRequest): Promise<Otp | null> {
    return await this.userRepo.verifyOtp({
      ...data,
      otpExpireTime: { $gt: Date.now() },
    });
  }

  // admin purpose
  async syncDeletedAccounts(): Promise<IServiceResponse<string>> {
    const deletedIds = await this.userRepo.findDeletedUserIds();
    const len = deletedIds.length;
    for (let i = 0; i < len; i++) {
      this.eSearchDbService.deleteItem(IIndexNameEnum.USER, deletedIds[i].id);
    }
    return successResponse(null, 'Sync with elastic db has been completed');
  }

  async deleteAccountByAdmin(
    body: IDeleteUserAccountReqByAdmin,
  ): Promise<IServiceResponse<string>> {
    const userInfo = await this.userRepo.findUser({
      name: body.userName,
      email: body.email,
    });
    if (!userInfo || !userInfo?.id) {
      throw new APIException(
        GetUserInformationErrorMessages.USER_NOT_FOUND,
        'USER_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }
    this.deleteAccount(userInfo.id);
    return successResponse(
      null,
      `${body.userName}'s account will be deleted soon`,
    );
  }

  async deleteAccount(userId: string): Promise<IServiceResponse<string>> {
    const isDeleted = await this.userRepo.deleteAccount(userId);
    if (!isDeleted) {
      throw new APIException(
        'Error in deleting account',
        'CONFLICT',
        HttpStatus.CONFLICT,
      );
    }
    this.eSearchDbService.deleteItem(IIndexNameEnum.USER, userId);

    (await QueueInstance).sendPayload(
      DeleteAccountQueueName.DELETE_ACCOUNT_QUEUE,
      Buffer.from(
        JSON.stringify({
          payloadType: QueuePayloadType.DELETE_ACCOUNT_HANDLER,
          userId,
        }),
      ),
    );
    return successResponse(null, 'Your account will be deleted soon');
  }

  private otpEmailBody(receiverName: string, otp: number): string {
    return `<p>Dear ${receiverName?.split('@')[0].toUpperCase() || 'User'},</p>
        <p>Thank you for using our services! As an added layer of security, we have generated a One-Time Password (OTP) for you to access your fitsomnia account. Please find your OTP details below:</p>
        
        <p>OTP: ${otp}
        
        <p>Please ensure to keep this OTP confidential and do not share it with anyone. It is valid for a limited time.
        If you did not request this OTP or have any concerns about your account's security, please contact our support team <NAME_EMAIL>.</p>
        
        <p>Thank you for your trust and cooperation.</p>
        
        Best regards,<br>
        Support Team<br>
        Fitsomnia<br>`;
  }

  private otpSmsBody(otp: number): string {
    return `Your FITSOMNIA OTP is ${otp}`;
  }

  private otpQuery(
    data: SendOtpRequest | VerifyOtpRequest,
  ): SendOtpRequest | VerifyOtpRequest {
    if (data?.phone) {
      return { phone: data?.phone };
    } else if (data?.email) {
      return { email: data.email };
    } else {
      return;
    }
  }
}
