import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import {
  IUserChallengeDetails,
  IUserChallengeHistoryRes,
  IUserChallengeStatusEnum,
  IUserLeaderboard,
  IUserPointsHistory,
} from 'models';
import {
  ICreatedUserChallengeDetails,
  ICreateUserChallengeReq,
  ICreateUserChallengeRes,
} from 'models/user-challenge/userChallenge.interface';

export class CreateUserChallengeReqDto implements ICreateUserChallengeReq {
  @ApiProperty({ example: '', required: true })
  @IsNotEmpty()
  @IsString({ message: 'Required challenge id' })
  challengeId: string;
}

export class CreatedUserChallengeDetails
  implements ICreatedUserChallengeDetails
{
  @ApiProperty()
  name: string;
  @ApiProperty()
  duration: number;
  @ApiProperty()
  loop: number;
  @ApiProperty()
  points: number;
  @ApiProperty()
  preview: string;
}
export class CreateUserChallengeResDto implements ICreateUserChallengeRes {
  @ApiProperty()
  id: string;
  @ApiProperty()
  userId: string;
  @ApiProperty()
  challengeId: string;
  @ApiProperty()
  lastStatus: IUserChallengeStatusEnum;
  @ApiProperty()
  expireAt: Date;
  @ApiProperty({ type: CreatedUserChallengeDetails })
  challengeDetails: CreatedUserChallengeDetails;
}

export class UserChallengeDetails implements IUserChallengeDetails {
  @ApiProperty()
  name: string;
  @ApiProperty()
  description: string;
  @ApiProperty()
  mechanics: string;
  @ApiProperty()
  type: string;
  @ApiProperty()
  category: string[];
  @ApiProperty()
  forceType: string[];
  @ApiProperty()
  primaryMuscles: string[];
  @ApiProperty()
  secondaryMuscles: string[];
  @ApiProperty()
  equipments: string[];
  @ApiProperty()
  preview: string;
  @ApiProperty()
  difficulty: string;
  @ApiProperty()
  duration: number;
  @ApiProperty()
  loop: number;
  @ApiProperty()
  points: number;
}
export class UserChallengeHistoryDtoRes implements IUserChallengeHistoryRes {
  @ApiProperty()
  lastStatus: string;
  @ApiProperty()
  startDate: Date;
  @ApiProperty()
  expireAt: Date;
  @ApiProperty()
  id: string;
  @ApiProperty({ type: UserChallengeDetails })
  challengeDetails: UserChallengeDetails;
}

export class UploadVideoReqDto {
  @ApiProperty({ example: '', required: true })
  id: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ example: '', required: false })
  videoUrl?: string;
}

export class UserPointsHistoryResDto implements IUserPointsHistory {
  @ApiProperty()
  points: number;
  @ApiProperty()
  lastStatus: string;
  @ApiProperty()
  name: string;
}
export class IUserLeaderboardResDto implements IUserLeaderboard {
  @ApiProperty()
  totalPoints: number;
  @ApiProperty()
  userId: string;
  @ApiProperty()
  name: string;
  @ApiProperty()
  image: string;
}
