/**
 * Smart Scheduler Configuration for Reminder System
 * Provides dynamic scheduling with server load monitoring and peak/off-peak optimization
 */

export interface SmartSchedulerConfig {
  // Frequency control
  defaultInterval: number;
  minInterval: number;
  maxInterval: number;
  
  // Server load thresholds and corresponding intervals
  serverLoad: {
    high: {
      cpu: number;
      memory: number;
      interval: number;
    };
    medium: {
      cpu: number;
      memory: number;
      interval: number;
    };
    low: {
      cpu: number;
      memory: number;
      interval: number;
    };
  };
  
  // Peak hours configuration
  peakHours: {
    morning: {
      start: number;
      end: number;
    };
    evening: {
      start: number;
      end: number;
    };
    offPeak: {
      start: number;
      end: number;
      skipProbability: number;
    };
  };
  
  // Cache and monitoring intervals
  cacheRefreshInterval: number;
  loadMonitorInterval: number;
  fallbackCheckInterval: number;
  maxAllowedGap: number;
  
  // Empty check optimization
  emptyCheckThreshold: number;
  emptyCheckMultiplier: number;
}

export const SMART_SCHEDULER_CONFIG: SmartSchedulerConfig = {
  // Extended frequency range: 5 minutes to 1 hour
  defaultInterval: 15,
  minInterval: 5,   // Minimum 5 minutes during peak activity
  maxInterval: 60,  // Maximum 1 hour during low activity

  serverLoad: {
    high: {
      cpu: 80,
      memory: 85,
      interval: 60  // 60 minutes (1 hour) during high load
    },
    medium: {
      cpu: 60,
      memory: 70,
      interval: 20  // 20 minutes during medium load
    },
    low: {
      cpu: 30,
      memory: 50,
      interval: 5   // 5 minutes during low load (peak performance)
    }
  },

  peakHours: {
    morning: {
      start: 6,  // 6 AM
      end: 10    // 10 AM
    },
    evening: {
      start: 17, // 5 PM
      end: 22    // 10 PM
    },
    offPeak: {
      start: 0,  // 12:00 AM (midnight)
      end: 7.5,  // 7:30 AM
      skipProbability: 0.7 // 70% chance to skip during off-peak
    }
  },
  
  cacheRefreshInterval: 60,  // Refresh cache every hour
  loadMonitorInterval: 5,    // Monitor load every 5 minutes
  fallbackCheckInterval: 30, // Fallback safety check every 30 minutes
  maxAllowedGap: 75,         // Trigger fallback if smart scheduler hasn't run in 75 minutes (15min buffer over max 60min)
  
  emptyCheckThreshold: 4,    // After 4 consecutive empty checks
  emptyCheckMultiplier: 1.5  // Increase interval by 50%
};
