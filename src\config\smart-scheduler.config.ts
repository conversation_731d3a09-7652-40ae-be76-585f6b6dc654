export interface SmartSchedulerConfig {
  // Default intervals (in minutes)
  defaultInterval: number;
  minInterval: number;
  maxInterval: number;
  
  // Server load thresholds
  serverLoad: {
    high: {
      cpu: number;
      memory: number;
      interval: number;
    };
    medium: {
      cpu: number;
      memory: number;
      interval: number;
    };
    low: {
      cpu: number;
      memory: number;
      interval: number;
    };
  };
  
  // Peak hour configuration
  peakHours: {
    morning: {
      start: number; // 24-hour format
      end: number;
    };
    evening: {
      start: number;
      end: number;
    };
    offPeak: {
      start: number; // 23 = 11 PM
      end: number;   // 5 = 5 AM
      skipProbability: number; // 0.7 = 70% chance to skip
    };
  };
  
  // Cache and monitoring settings
  cacheRefreshInterval: number; // minutes
  loadMonitorInterval: number;  // minutes
  fallbackCheckInterval: number; // minutes
  maxAllowedGap: number; // minutes before fallback triggers
  
  // Empty check optimization
  emptyCheckThreshold: number; // consecutive empty checks before reducing frequency
  emptyCheckMultiplier: number; // multiply interval by this when too many empty checks
}

export const SMART_SCHEDULER_CONFIG: SmartSchedulerConfig = {
  // Start with 15 minutes instead of 5 for immediate optimization
  defaultInterval: 15,
  minInterval: 10,
  maxInterval: 45,
  
  serverLoad: {
    high: {
      cpu: 80,
      memory: 85,
      interval: 30
    },
    medium: {
      cpu: 60,
      memory: 70,
      interval: 20
    },
    low: {
      cpu: 30,
      memory: 50,
      interval: 10
    }
  },
  
  peakHours: {
    morning: {
      start: 6,  // 6 AM
      end: 10    // 10 AM
    },
    evening: {
      start: 17, // 5 PM
      end: 22    // 10 PM
    },
    offPeak: {
      start: 23, // 11 PM
      end: 5,    // 5 AM
      skipProbability: 0.7 // 70% chance to skip during off-peak
    }
  },
  
  cacheRefreshInterval: 60,  // Refresh cache every hour
  loadMonitorInterval: 5,    // Monitor load every 5 minutes
  fallbackCheckInterval: 30, // Fallback safety check every 30 minutes
  maxAllowedGap: 45,         // Trigger fallback if smart scheduler hasn't run in 45 minutes
  
  emptyCheckThreshold: 4,    // After 4 consecutive empty checks
  emptyCheckMultiplier: 1.5  // Increase interval by 50%
};
