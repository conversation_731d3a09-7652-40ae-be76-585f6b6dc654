import { randomUUID } from 'crypto';
import { IUserPointsSources } from 'models';
import { model, Schema } from 'mongoose';
import { UserPoints, UserPointsTransHistory } from 'src/entity/user-points';

const UserPointsHistorySchema = new Schema<UserPointsTransHistory>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
    },
    type: {
      type: String,
      enum: ['earned', 'spent'],
    },
    points: {
      type: Number,
      default: 0,
    },
    pointSourceType: {
      type: String,
      enum: IUserPointsSources,
      required: false,
    },
    pointSourceId: {
      type: String,
      required: false,
      default: null,
    },
    pointSourceName: {
      type: String,
      required: false,
      default: null,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const UserPointsSchema = new Schema<UserPoints>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
      unique: true,
    },
    points: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const UserPointsHistoryModel = model<UserPointsTransHistory>(
  'user-points-history',
  UserPointsHistorySchema,
);
const UserPointsModel = model<UserPoints>('user-points', UserPointsSchema);

export { UserPointsHistoryModel, UserPointsModel };


