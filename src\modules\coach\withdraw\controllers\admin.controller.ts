import { Body, Controller, Get, HttpStatus, Param, ParseUUI<PERSON><PERSON>e, <PERSON>, Query, UseGuards, } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags, } from '@nestjs/swagger';
import { OnlyAuthGuard, OnlyRoleGuard, Role, Roles, } from 'src/authentication/guards/auth-role.guard';
import { DEFAULT_PAGINATION_LIMIT } from '../../common/const/coach.pagination.const';
import { COACH_API_FOR_ADMIN } from '../../common/const/swagger.const';
import { WithdrawServiceForAdmin } from '../services/admin.service';
import { GetWithdrawInfoSuccessResponseDtoForAdmin } from './dtos/admin-get-withdraw-info.dto';
import { GetWithdrawInfosQueryDtoForAdmin, GetWithdrawInfosSuccessResponseDtoForAdmin } from './dtos/admin-get-withdraw-infos.dto';
import { UpdateWithdrawRequestDtoForAdmin } from './dtos/admin-update-withdraw-info.dto';
@ApiTags(COACH_API_FOR_ADMIN)
@ApiBearerAuth()
@Controller('admin/coach')
export class WithdrawControllerAdmin {
	constructor(private service: WithdrawServiceForAdmin) {}

  @Get('withdraw/:requestId')
  @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
  @Roles(Role.Admin)
  @ApiOperation({ summary: 'Get single withdraw request info' })
  @ApiParam({
    name: 'requestId',
    description: 'The withdraw request id whose data you want',
    type: String,
  })
  @ApiResponse({
    description: 'Returns a single withdraw request data',
    type: GetWithdrawInfoSuccessResponseDtoForAdmin,
    status: HttpStatus.OK,
  })
  async getSingleWithdrawInfo(@Param('requestId', ParseUUIDPipe) requestId: string) {
    return await this.service.getSingleWithdrawInfo(requestId);
  }

  @Get('withdraws')
  @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
  @Roles(Role.Admin)
  @ApiOperation({ summary: 'Get multiple withdraw request info' })
  @ApiResponse({
    description: 'Returns multiple withdraw request data',
    type: GetWithdrawInfosSuccessResponseDtoForAdmin,
    status: HttpStatus.OK,
  })
  async getMultipleWithdrawInfo(@Query() queryDto: GetWithdrawInfosQueryDtoForAdmin) {
    const offset = queryDto.offset || 0;
    const limit = queryDto.limit || DEFAULT_PAGINATION_LIMIT;
    return await this.service.getMultipleWithdrawInfo(offset, limit, queryDto.statusChoice);
  }

  @Patch('withdraw/:requestId/approve')
  @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
  @Roles(Role.Admin)
  @ApiOperation({ summary: 'Approve withdraw request' })
  @ApiParam({
    name: 'requestId',
    description: 'The requestId to identify the withdraw request',
    type: String,
  })
  @ApiBody({
    description: 'Approval details, including admin comments',
    type: UpdateWithdrawRequestDtoForAdmin,
  })
  @ApiResponse({
    description: 'Returns the updated withdraw request',
    type: GetWithdrawInfoSuccessResponseDtoForAdmin,
    status: HttpStatus.OK,
  })
  async approveWithdrawRequest(@Param('requestId', ParseUUIDPipe) requestId: string, @Body() updateDto: UpdateWithdrawRequestDtoForAdmin) {
    return await this.service.approveWithdrawRequest(requestId, updateDto);
  }

  @Patch('withdraw/:requestId/reject')
  @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
  @Roles(Role.Admin)
  @ApiOperation({ summary: 'Reject withdraw request' })
  @ApiParam({
    name: 'requestId',
    description: 'The requestId to identify the withdraw request',
    type: String,
  })
  @ApiBody({
    description: 'Reject details, including admin comments',
    type: UpdateWithdrawRequestDtoForAdmin,
  })
  @ApiResponse({
    description: 'Returns the updated withdraw request',
    type: GetWithdrawInfoSuccessResponseDtoForAdmin,
    status: HttpStatus.OK,
  })
  async rejectWithdrawRequest(
    @Param('requestId', ParseUUIDPipe) requestId: string,
    @Body() updateDto: UpdateWithdrawRequestDtoForAdmin,
  ) {
    return await this.service.rejectWithdrawRequest(requestId, updateDto);
  }
}
