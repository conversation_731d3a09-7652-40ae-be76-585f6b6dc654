import { Test, TestingModule } from '@nestjs/testing';
import { UserAuthService } from 'src/modules/user-auth/services';
import { TopicManagementService } from 'src/modules/reminder/services/topic-management.service';
import { UserRepository } from 'src/modules/user/repositories';
import { Helper } from 'src/helper/helper.interface';
import { JwtService } from '@nestjs/jwt';
import { ESearchDbService } from 'src/modules/global-search/services/elastic.db.service';
import { UserAuthRepository } from 'src/modules/user-auth/repositories';
import Redis from 'ioredis';
import { getRedisToken } from '@nestjs-modules/ioredis';

describe('UserAuthService - Topic Subscription Integration', () => {
  let userAuthService: UserAuthService;
  let topicManagementService: jest.Mocked<TopicManagementService>;
  let userRepository: jest.Mocked<UserRepository>;
  let userAuthRepository: jest.Mocked<UserAuthRepository>;

  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    fcmToken: 'fcm-token-123',
  };

  beforeEach(async () => {
    const mockTopicManagementService = {
      subscribeUserToReminders: jest.fn(),
      unsubscribeUserFromReminders: jest.fn(),
    };

    const mockUserRepository = {
      findUser: jest.fn(),
      updateUser: jest.fn(),
      verifyFcmtoken: jest.fn(),
    };

    const mockUserAuthRepository = {
      removeUserSessionyAndFcmToken: jest.fn(),
    };

    const mockHelper = {
      serviceResponse: {
        successResponse: jest.fn(),
      },
    };

    const mockJwtService = {
      sign: jest.fn(),
    };

    const mockESearchDbService = {};
    const mockRedis = {
      del: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserAuthService,
        {
          provide: TopicManagementService,
          useValue: mockTopicManagementService,
        },
        {
          provide: UserRepository,
          useValue: mockUserRepository,
        },
        {
          provide: UserAuthRepository,
          useValue: mockUserAuthRepository,
        },
        {
          provide: Helper,
          useValue: mockHelper,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: ESearchDbService,
          useValue: mockESearchDbService,
        },
        {
          provide: getRedisToken(),
          useValue: mockRedis,
        },
      ],
    }).compile();

    userAuthService = module.get<UserAuthService>(UserAuthService);
    topicManagementService = module.get(TopicManagementService);
    userRepository = module.get(UserRepository);
    userAuthRepository = module.get(UserAuthRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('verifyFcmToken', () => {
    it('should subscribe user to topics when FCM token is updated', async () => {
      userRepository.verifyFcmtoken.mockResolvedValue(true);
      topicManagementService.subscribeUserToReminders.mockResolvedValue(true);

      const result = await userAuthService.verifyFcmToken('user-123', 'new-fcm-token');

      expect(result).toBe('token updated successfully');
      expect(userRepository.verifyFcmtoken).toHaveBeenCalledWith('user-123', 'new-fcm-token');
      expect(topicManagementService.subscribeUserToReminders).toHaveBeenCalledWith('new-fcm-token');
    });

    it('should not subscribe when FCM token is empty', async () => {
      userRepository.verifyFcmtoken.mockResolvedValue(true);

      const result = await userAuthService.verifyFcmToken('user-123', '');

      expect(result).toBe('token updated successfully');
      expect(userRepository.verifyFcmtoken).toHaveBeenCalledWith('user-123', '');
      expect(topicManagementService.subscribeUserToReminders).not.toHaveBeenCalled();
    });

    it('should handle subscription failures gracefully', async () => {
      userRepository.verifyFcmtoken.mockResolvedValue(true);
      topicManagementService.subscribeUserToReminders.mockRejectedValue(
        new Error('Subscription failed')
      );

      const result = await userAuthService.verifyFcmToken('user-123', 'new-fcm-token');

      expect(result).toBe('token updated successfully');
      expect(topicManagementService.subscribeUserToReminders).toHaveBeenCalledWith('new-fcm-token');
    });
  });

  describe('logout', () => {
    it('should unsubscribe user from topics when logging out with FCM token', async () => {
      userRepository.findUser.mockResolvedValue(mockUser);
      userAuthRepository.removeUserSessionyAndFcmToken.mockResolvedValue(true);
      topicManagementService.unsubscribeUserFromReminders.mockResolvedValue(true);

      const result = await userAuthService.logout('user-123');

      expect(userRepository.findUser).toHaveBeenCalledWith({ id: 'user-123' });
      expect(userAuthRepository.removeUserSessionyAndFcmToken).toHaveBeenCalledWith('user-123');
      expect(topicManagementService.unsubscribeUserFromReminders).toHaveBeenCalledWith('fcm-token-123');
    });

    it('should not unsubscribe when user has no FCM token', async () => {
      const userWithoutToken = { ...mockUser, fcmToken: '' };
      userRepository.findUser.mockResolvedValue(userWithoutToken);
      userAuthRepository.removeUserSessionyAndFcmToken.mockResolvedValue(true);

      const result = await userAuthService.logout('user-123');

      expect(userRepository.findUser).toHaveBeenCalledWith({ id: 'user-123' });
      expect(userAuthRepository.removeUserSessionyAndFcmToken).toHaveBeenCalledWith('user-123');
      expect(topicManagementService.unsubscribeUserFromReminders).not.toHaveBeenCalled();
    });

    it('should not unsubscribe when user is not found', async () => {
      userRepository.findUser.mockResolvedValue(null);
      userAuthRepository.removeUserSessionyAndFcmToken.mockResolvedValue(true);

      const result = await userAuthService.logout('user-123');

      expect(userRepository.findUser).toHaveBeenCalledWith({ id: 'user-123' });
      expect(userAuthRepository.removeUserSessionyAndFcmToken).toHaveBeenCalledWith('user-123');
      expect(topicManagementService.unsubscribeUserFromReminders).not.toHaveBeenCalled();
    });

    it('should handle unsubscription failures gracefully', async () => {
      userRepository.findUser.mockResolvedValue(mockUser);
      userAuthRepository.removeUserSessionyAndFcmToken.mockResolvedValue(true);
      topicManagementService.unsubscribeUserFromReminders.mockRejectedValue(
        new Error('Unsubscription failed')
      );

      const result = await userAuthService.logout('user-123');

      expect(topicManagementService.unsubscribeUserFromReminders).toHaveBeenCalledWith('fcm-token-123');
      // Should still complete logout process despite unsubscription failure
    });

    it('should handle database errors during logout', async () => {
      userRepository.findUser.mockRejectedValue(new Error('Database error'));

      const result = await userAuthService.logout('user-123');

      expect(userRepository.findUser).toHaveBeenCalledWith({ id: 'user-123' });
      expect(topicManagementService.unsubscribeUserFromReminders).not.toHaveBeenCalled();
    });
  });

  describe('Topic Subscription Integration Scenarios', () => {
    it('should handle subscription during user authentication flow', async () => {
      // Simulate a successful subscription during login/signup
      topicManagementService.subscribeUserToReminders.mockResolvedValue(true);

      // Call the private method through verifyFcmToken (which calls subscribeUserToTopics)
      await userAuthService.verifyFcmToken('user-123', 'new-fcm-token');

      expect(topicManagementService.subscribeUserToReminders).toHaveBeenCalledWith('new-fcm-token');
    });

    it('should handle subscription failures without breaking authentication flow', async () => {
      // Simulate subscription failure
      topicManagementService.subscribeUserToReminders.mockResolvedValue(false);

      // Authentication should still succeed even if subscription fails
      const result = await userAuthService.verifyFcmToken('user-123', 'new-fcm-token');

      expect(result).toBe('token updated successfully');
      expect(topicManagementService.subscribeUserToReminders).toHaveBeenCalledWith('new-fcm-token');
    });

    it('should handle topic service unavailability gracefully', async () => {
      // Simulate topic service being unavailable
      topicManagementService.subscribeUserToReminders.mockRejectedValue(
        new Error('Topic service unavailable')
      );

      // Authentication should still work
      const result = await userAuthService.verifyFcmToken('user-123', 'new-fcm-token');

      expect(result).toBe('token updated successfully');
      expect(topicManagementService.subscribeUserToReminders).toHaveBeenCalledWith('new-fcm-token');
    });

    it('should handle rapid FCM token updates correctly', async () => {
      userRepository.verifyFcmtoken.mockResolvedValue(true);
      topicManagementService.subscribeUserToReminders.mockResolvedValue(true);

      // Simulate rapid token updates
      await userAuthService.verifyFcmToken('user-123', 'token-1');
      await userAuthService.verifyFcmToken('user-123', 'token-2');
      await userAuthService.verifyFcmToken('user-123', 'token-3');

      expect(topicManagementService.subscribeUserToReminders).toHaveBeenCalledTimes(3);
      expect(topicManagementService.subscribeUserToReminders).toHaveBeenNthCalledWith(1, 'token-1');
      expect(topicManagementService.subscribeUserToReminders).toHaveBeenNthCalledWith(2, 'token-2');
      expect(topicManagementService.subscribeUserToReminders).toHaveBeenNthCalledWith(3, 'token-3');
    });
  });
});
