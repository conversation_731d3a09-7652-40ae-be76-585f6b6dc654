import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { Helper } from 'src/helper/helper.interface';
import {
  deepCasting,
  shallowCasting,
} from 'src/internal/casting/object.casting';
import {
  throwConflictErrIf,
  throwNotFoundErr,
} from 'src/internal/exception/api.exception.ext';
import { AccountingProvider } from 'src/modules/accounting/account/providers/account.provider';
import { AccountingReference, AccountingReferenceType, AccountingTransactionType } from 'src/modules/accounting/common/entities/account.entity';
import {
  WITHDRAW_INFO_NOT_FOUND,
  WITHDRAW_IS_NOT_PENDING,
} from '../../common/const/coach.const';
import {
  CoachWithdrawEntity,
  CoachWithdrawPaymentStatus,
  CoachWithdrawReviewStatus,
} from '../../common/entities/coach-withdraw.entity';
import { calculateWithdrawSplit } from '../../common/utils/withdraw.utils';
import { IncomeProfileProviderForCoach } from '../../income-profile/providers/coach.provider';
import { VerifiedProfileProviderForAdmin } from '../../verified-profile/providers/admin.provider';
import {
  GetWithdrawInfoResponseDtoForAdmin,
  GetWithdrawInfoSuccessResponseDtoForAdmin,
} from '../controllers/dtos/admin-get-withdraw-info.dto';
import {
  GetWithdrawInfosResponseDtoForAdmin,
  GetWithdrawInfosSuccessResponseDtoForAdmin,
  WithdrawStatusChoicesForAdmin,
} from '../controllers/dtos/admin-get-withdraw-infos.dto';
import { UpdateWithdrawRequestDtoForAdmin } from '../controllers/dtos/admin-update-withdraw-info.dto';
import { CoachWithdrawProviderForInternal } from '../providers/internal.provider';
import { WithdrawRepositoryForAdmin } from '../repositories/admin.repository';

@Injectable()
export class WithdrawServiceForAdmin {
  constructor(
    private helper: Helper,
    private readonly repository: WithdrawRepositoryForAdmin,
    @Inject(forwardRef(() => VerifiedProfileProviderForAdmin))
    private readonly verifiedProfileProvider: VerifiedProfileProviderForAdmin,
    @Inject(forwardRef(() => IncomeProfileProviderForCoach))
    private readonly coachIncomeInfoProvider: IncomeProfileProviderForCoach,
    @Inject(forwardRef(() => AccountingProvider))
    private readonly accountProvider: AccountingProvider,
    @Inject(forwardRef(() => CoachWithdrawProviderForInternal))
    private readonly coachWithdrawProvider: CoachWithdrawProviderForInternal,
  ) {}

  async getSingleWithdrawInfo(
    requestId: string,
  ): Promise<GetWithdrawInfoSuccessResponseDtoForAdmin> {
    const withdrawInfo = await this.repository.getWithdrawInfo(requestId);
    throwNotFoundErr(
      !withdrawInfo,
      'Withdraw request not found',
      WITHDRAW_INFO_NOT_FOUND,
    );

    const coachProfile = await this.verifiedProfileProvider.getSingleCoachProfile(withdrawInfo.coachId);

    const { fitsomniaShare, coachShare } = calculateWithdrawSplit(withdrawInfo.moneyWithdraw);

    const responseDto = deepCasting(
      GetWithdrawInfoResponseDtoForAdmin,
      withdrawInfo,
    );

    responseDto.coachName = coachProfile.legalName;
    responseDto.coachShare = coachShare;
    responseDto.fitsomniaShare = fitsomniaShare;

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getMultipleWithdrawInfo(
    offset: number,
    limit: number,
    statusChoice: string,
  ): Promise<GetWithdrawInfosSuccessResponseDtoForAdmin> {
    let withdrawInfos: CoachWithdrawEntity[];

    if (statusChoice == WithdrawStatusChoicesForAdmin.PENDING) {
      withdrawInfos = await this.repository.getWithdrawInfosByReviewStatus(
        offset,
        limit,
        CoachWithdrawReviewStatus.PENDING,
      );
    } else if (statusChoice == WithdrawStatusChoicesForAdmin.APPROVED) {
      withdrawInfos = await this.repository.getWithdrawInfosByReviewStatus(
        offset,
        limit,
        CoachWithdrawReviewStatus.APPROVED,
      );
    } else if (statusChoice == WithdrawStatusChoicesForAdmin.REJECTED) {
      withdrawInfos = await this.repository.getWithdrawInfosByReviewStatus(
        offset,
        limit,
        CoachWithdrawReviewStatus.REJECTED,
      );
    } else {
      withdrawInfos = await this.repository.getWithdrawInfos(offset, limit);
    }

    const responseDtos = withdrawInfos.map((withdrawInfo) =>
      deepCasting(GetWithdrawInfosResponseDtoForAdmin, withdrawInfo),
    );

    const coachProfiles = await Promise.all(
    responseDtos.map((responseDto) =>
      this.verifiedProfileProvider.getSingleCoachProfile(responseDto.coachId),
    )
    );
     
    responseDtos.forEach((responseDto, index) => {
    responseDto.coachName = coachProfiles[index].legalName;
    });
    
    return this.helper.serviceResponse.successResponse(responseDtos);
  }

  async approveWithdrawRequest(requestId: string, updateDto: UpdateWithdrawRequestDtoForAdmin): Promise<GetWithdrawInfoSuccessResponseDtoForAdmin> {
    const oldWithdrawInfo = await this.repository.getWithdrawInfo(requestId);
    throwNotFoundErr(
      !oldWithdrawInfo,
      'withdraw request not found',
      WITHDRAW_INFO_NOT_FOUND,
    );

    const isPending = oldWithdrawInfo.reviewStatus == CoachWithdrawReviewStatus.PENDING;
    throwConflictErrIf(
      !isPending,
      'withdraw request is not pending',
      WITHDRAW_IS_NOT_PENDING,
    );
 
    const updatedWithdrawInfo = {
      ...oldWithdrawInfo,
      ...updateDto,
      bkashTrxId: updateDto.trxId,
      reviewStatus: CoachWithdrawReviewStatus.APPROVED,
      paymentStatus: CoachWithdrawPaymentStatus.Triggered
    };
    // check for withdraw business logic
    const { fitsomniaShare, coachShare } = calculateWithdrawSplit(oldWithdrawInfo.moneyWithdraw);

    const newWithdrawInfo = await this.repository.updateWithdrawInfo(
      requestId,
      updatedWithdrawInfo,
    );
    
    const responseDto = deepCasting(
      GetWithdrawInfoResponseDtoForAdmin,
      newWithdrawInfo,
    );

    await this.coachWithdrawProvider.onWithdrawSuccess(requestId);

    await this.accountProvider.addAccount(
      AccountingReference.COACH,
      AccountingReferenceType.WITHDRAW,
      newWithdrawInfo.id,
      AccountingTransactionType.DEBIT,
      coachShare
    );
    
    await this.accountProvider.addAccount(
      AccountingReference.COACH,
      AccountingReferenceType.PLATFORMFREE,
      newWithdrawInfo.id,
      AccountingTransactionType.CREDIT,
      fitsomniaShare
    );

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async rejectWithdrawRequest(
    requestId: string,
    updateDto: UpdateWithdrawRequestDtoForAdmin,
  ): Promise<GetWithdrawInfoSuccessResponseDtoForAdmin> {
    const oldWithdrawInfo = await this.repository.getWithdrawInfo(requestId);
    throwNotFoundErr(
      !oldWithdrawInfo,
      'Withdraw request not found',
      WITHDRAW_INFO_NOT_FOUND,
    );

    const isPending = oldWithdrawInfo.reviewStatus == CoachWithdrawReviewStatus.PENDING;
    throwConflictErrIf(
      !isPending,
      'Withdraw request is not pending',
      WITHDRAW_IS_NOT_PENDING,
    );

    const updatedWithdrawInfo = shallowCasting(CoachWithdrawEntity, updateDto);
    updatedWithdrawInfo.reviewStatus = CoachWithdrawReviewStatus.REJECTED;

    const newWithdrawInfo = await this.repository.updateWithdrawInfo(
      requestId,
      updatedWithdrawInfo,
    );

    // Refund the amount
    await this.coachIncomeInfoProvider.restoreWithdrawableAmount(
      oldWithdrawInfo.coachId,
      oldWithdrawInfo.moneyWithdraw,
    );
    const responseDto = deepCasting(
      GetWithdrawInfoResponseDtoForAdmin,
      newWithdrawInfo,
    );

    return this.helper.serviceResponse.successResponse(responseDto);
  }
}
