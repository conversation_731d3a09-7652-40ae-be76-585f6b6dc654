import { Modu<PERSON> } from '@nestjs/common';
import { NotificationController } from './rest';
import { NotificationService } from './services';
import { NotificationRepository } from './repositories';
import { NotificationHelperService } from './services/helper.notification.service';
import { NotificationStatsService } from './services/notification-stats.service';

@Module({
  controllers: [NotificationController],
  providers: [NotificationService, NotificationHelperService, NotificationStatsService, NotificationRepository],
  exports: [NotificationHelperService, NotificationStatsService],
})
export class NotificationModule {}
