import { Injectable } from "@nestjs/common";
import { Cron, CronExpression } from "@nestjs/schedule";
import { IncomeProfileProviderForCoach } from "../../income-profile/providers/coach.provider";
import { CoachPaymentProviderForInternal } from "../../payment/providers/internal.provider";

@Injectable()
export class AvailablePaymentHandlerJob {

  constructor(
    private readonly coachPayoutProvider: CoachPaymentProviderForInternal,
    private readonly coachIncomeProvider: IncomeProfileProviderForCoach
  ) { }

  
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)

  async copyUnprocessedPaymentsIntoAvailablePayments() {
  console.log('cron job for unprocessed coach payment')
  const now = new Date();
  const startOfToday = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));
    

  const plans = await this.coachPayoutProvider.getPayoutPlansByCompletionStatus(false)

  for (const plan of plans) {
    const subscriptionDate = new Date(plan.subscriptionDate);
    const daysSinceSubscription = Math.floor(
      (startOfToday.getTime() - subscriptionDate.getTime()) / (1000 * 60 * 60 * 24)
    );
   
    console.log('daysSinceSubscription',daysSinceSubscription)

    if (daysSinceSubscription < 7) continue; // Wait until day 8

    const payoutsToMake = [];

    // On day 8, pay for day 1 to 8 (bulk catchup)
    // const payoutStart = plan.lastPaidDay + 1;
    const payoutStart = plan.lastPaidDay + 1;
    const payoutEnd = Math.min(daysSinceSubscription + 1, 30); // +1 to include today
    
    // console.log('coach id', plan.coachId)
    // console.log('pay out start', payoutStart)
    // console.log('pay out end', payoutEnd)

    for (let day = payoutStart; day <= payoutEnd; day++) {
      const payoutEntry = plan.payoutSchedule.find(p => p.dayNumber === day && !p.isPaid);
      // console.log('payout entry', payoutEntry)
      if (payoutEntry) {
        payoutEntry.isPaid = true;
        payoutEntry.paidAt = startOfToday;
        payoutsToMake.push(payoutEntry.amount);
        plan.lastPaidDay = day;
      }
    }

    if (payoutsToMake.length === 0) continue;

    const totalToPay = payoutsToMake.reduce((sum, amt) => sum + amt, 0);

    // console.log('total to pay', totalToPay)

    const updatePayoutData= await this.coachPayoutProvider.updatePlanAfterPayout(plan.id, {
      payoutSchedule: plan.payoutSchedule,
      lastPaidDay: plan.lastPaidDay,
      daysPaid: plan.lastPaidDay,
    });

    // console.log('Updated plan:', updatePayoutData);

    // If last day paid, mark complete
    if (plan.lastPaidDay >= 30) {
      plan.isCompleted = true;
        await this.coachPayoutProvider.setPayoutPlanCompletedStatusById(plan.id, plan.isCompleted )
    }

    const coachIncome = await this.coachIncomeProvider.updateIncomeProfile(plan.coachId, totalToPay)
    
    // console.log('coach income',coachIncome)

 

  }
  }

}