import { Injectable } from '@nestjs/common';
import { sharedConfig } from 'config/shared';
import {
  ICreateBaseChallengeRes,
  ICreateUserChallenge,
  ICreateUserChallengeRes,
  IUserChallengeHistoryRes,
  IUserChallengeStatusEnum,
  IUserLeaderboard,
  IUserPointsHistory,
} from 'models';
import { BaseChallengeModel } from 'src/database/baseChallenge/baseChallenge.model';
import { UserChallengeModel } from 'src/database/userChallenge/userChallenge.model';
import { UserChallenge } from 'src/entity/userChallenge';
import { HelperChallengeRepository } from './helper.challenge.repository';

@Injectable()
export class UserChallengeRepository {
  constructor(
    private readonly helperChallengeRepository: HelperChallengeRepository,
  ) {}
  async createUserChallenge(
    body: ICreateUserChallenge,
  ): Promise<ICreateUserChallengeRes | null> {
    try {
      const userChallenge = (await UserChallengeModel.create(body)).toObject();
      delete userChallenge._id;
      delete userChallenge.videoUrl;
      return userChallenge;
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async findOne(
    query: Record<string, any>,
  ): Promise<ICreateUserChallengeRes | null> {
    try {
      return await UserChallengeModel.findOne(query).select('-_id');
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async getUserChallengeHistory(
    userId: string,
    filterBy: IUserChallengeStatusEnum,
    offset: number,
    limit: number,
  ): Promise<IUserChallengeHistoryRes[] | null> {
    offset = Number(offset);
    limit =
      Number(limit) > sharedConfig.defaultMaxLImit
        ? sharedConfig.defaultLimit
        : Number(limit);
    try {
      const query: any[] =
        this.helperChallengeRepository.userChallengeHistoryAggQuery(
          userId,
          filterBy,
          offset,
          limit,
        );
      return await UserChallengeModel.aggregate(query);
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async getAcceptedChallengeIds(
    query: Record<string, any>,
  ): Promise<string[] | null> {
    try {
      const acceptedList = await UserChallengeModel.find(query).select(
        '-_id challengeId',
      );
      return acceptedList.map((e) => e.challengeId);
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async getUserMonthlyChallenges(
    query: Record<string, any>,
    offset: number,
    limit: number,
  ): Promise<ICreateBaseChallengeRes[] | null> {
    try {
      return await BaseChallengeModel.find(query)
        .sort({ startDate: 1 })
        .skip(offset)
        .limit(limit)
        .select('-_id -isActive -totalPick -startDate')
        .lean();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async uploadVideo(
    userId: string,
    id: string,
    videoUrl?: string,
  ): Promise<boolean> {
    try {
      // Create an update object that can handle optional videoUrl
      const updateFields: any = {
        lastStatus: IUserChallengeStatusEnum.PENDING,
      };

      // Only add videoUrl to the update if it's provided
      if (videoUrl !== undefined && videoUrl !== null) {
        updateFields.videoUrl = videoUrl;
      }

      const updated = await UserChallengeModel.findOneAndUpdate(
        {
          userId,
          id,
          expireAt: { $gt: Date.now() },
          lastStatus: IUserChallengeStatusEnum.IN_PROGRESS,
        },
        {
          $set: updateFields,
        },
        {
          new: true,
        },
      );
      return updated ? true : false;
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async getUserTotoalPoint(userId: string): Promise<number | null> {
    try {
      const query =
        this.helperChallengeRepository.totalPointsOfChallengesAggQuery(userId);
      const point = await UserChallengeModel.aggregate(query);
      return point.length ? point[0]?.totalPoints : 0;
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }
  async getUserPointsHistory(
    userId: string,
    offset: number,
    limit: number,
  ): Promise<IUserPointsHistory[] | null> {
    try {
      const query = this.helperChallengeRepository.pointsHistoryAggQuery(
        userId,
        offset,
        limit,
      );

      const pointHistory = await UserChallengeModel.aggregate(query);
      return pointHistory;
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async leaderBoard(
    offset: number,
    limit: number,
  ): Promise<IUserLeaderboard[] | null> {
    try {
      const query: any[] = this.helperChallengeRepository.leaderBoardAggQuery(
        offset,
        limit,
      );

      const leaderBoard = await UserChallengeModel.aggregate(query);
      return leaderBoard;
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async findPendingChallenges(): Promise<UserChallenge[]> {
    try {
      const results = await UserChallengeModel.find({
        lastStatus: IUserChallengeStatusEnum.PENDING,
      }).lean();

      return results;
    } catch (error) {
      console.log(error.message);
      return [];
    }
  }
}
