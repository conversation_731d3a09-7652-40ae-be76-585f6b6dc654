import { InjectRedis } from '@nestjs-modules/ioredis';
import { HttpStatus, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { authConfig } from 'config/auth';
import { randomUUID } from 'crypto';
import Redis from 'ioredis';
import {
  CreateUserErrorMessages,
  CreateUserRequest,
  CreateUserSuccessResponse,
  IIndexNameEnum,
  SendOtpErrorMessages,
  SendOtpForSignupRequest,
  SendOtpRequest,
  SendOtpSuccessResponse,
  SignInErrorMessages,
  UserForgotPasswordErrorMessages,
  UserForgotPasswordRequest,
  UserForgotPasswordSuccessMessages,
  UserForgotPasswordSuccessResponse,
  VerifyOtpErrorMessages,
  VerifyOtpRequest,
  VerifyOtpSuccessMessages,
  VerifyOtpSuccessResponse,
} from 'models';
import { UserJwtPayload } from 'src/entity/user-auth';
import { Helper } from 'src/helper/helper.interface';
import { APIException } from 'src/internal/exception/api.exception';
import { ESearchDbService } from 'src/modules/global-search/services/elastic.db.service';
import { UserRepository } from 'src/modules/user/repositories';
import { UserAuthRepository } from '../repositories';
@Injectable()
export class UserAuthServiceV2 {
  constructor(
    @InjectRedis() private readonly redis: Redis,
    private userRepo: UserRepository,
    private helper: Helper,
    private jwtService: JwtService,
    private readonly eSearchDbService: ESearchDbService,
    private readonly userAuthRepository: UserAuthRepository,
  ) {}

  async signup(data: CreateUserRequest): Promise<CreateUserSuccessResponse> {
    const query = this.otpQuery(data);
    const doesUserExist = await this.userRepo.findUser(query);
    if (doesUserExist) {
      throw new APIException(
        CreateUserErrorMessages.USER_ALREADY_EXITS,
        'USER_ALREADY_EXITS',
        HttpStatus.BAD_REQUEST,
      );
    }

    const tmpUser = await this.userRepo.findOtpForSignup({
      ...data,
      otpExpireTime: { $gt: Date.now() },
    });
    if (!tmpUser) {
      throw new APIException(
        CreateUserErrorMessages.OTP_EXPIRED_OR_INVALID_OTP,
        'OTP_EXPIRED_OR_INVALID_OTP',
        HttpStatus.BAD_REQUEST,
      );
    }

    const user: any = tmpUser;
    user.email = tmpUser?.email && tmpUser?.email.toLowerCase();
    user.password = await bcrypt.hash(user.password, authConfig.salt);

    const registeredUser = await this.userRepo.createUser(user);
    if (!registeredUser)
      throw new APIException(
        CreateUserErrorMessages.CAN_NOT_CREATE_USER,
        'CAN_NOT_CREATE_USER',
        HttpStatus.BAD_REQUEST,
      );

    const payload: UserJwtPayload = {
      id: registeredUser.id,
      email: registeredUser?.email,
      phone: registeredUser?.phone,
      logInTime: Date.now(),
      role: 'user',
    };
    this.eSearchDbService.insertOneItemToES(
      IIndexNameEnum.USER,
      registeredUser.id,
    );
    const token = await this.generateToken(payload);
    return this.helper.serviceResponse.successResponse({ token });
  }

  async signupSendOTP(
    data: SendOtpForSignupRequest,
  ): Promise<SendOtpSuccessResponse> {
    // Validate that at least one valid contact method is provided
    if (
      (!data.email || data.email.trim() === '') &&
      (!data.phone || data.phone.trim() === '')
    ) {
      throw new APIException(
        'Valid email or phone number is required',
        'INVALID_CONTACT_METHOD',
        HttpStatus.BAD_REQUEST,
      );
    }

    const query = this.otpQuery(data);
    const doesUserExist = await this.userRepo.findUser(query);

    if (doesUserExist?.email && doesUserExist?.email === data?.email) {
      throw new APIException(
        SendOtpErrorMessages.USER_EMAIL_ALREADY_EXITS,
        'USER_ALREADY_EXITS',
        HttpStatus.BAD_REQUEST,
      );
    } else if (doesUserExist?.phone && doesUserExist?.phone === data?.phone) {
      throw new APIException(
        SendOtpErrorMessages.USER_PHONE_ALREADY_EXITS,
        'USER_ALREADY_EXITS',
        HttpStatus.BAD_REQUEST,
      );
    }
    return await this.sendOtpForSignUp(data);
  }

  async forgotPasswordSendOTP(
    data: SendOtpRequest,
  ): Promise<SendOtpSuccessResponse> {
    const query = this.otpQuery(data);
    const doesUserExist = await this.userRepo.findUser(query);
    if (!doesUserExist)
      throw new APIException(
        UserForgotPasswordErrorMessages.CAN_NOT_GET_USER,
        'CAN_NOT_GET_USER',
        HttpStatus.BAD_REQUEST,
      );
    return this.sendOtpForForgotPassword(data);
  }

  async forgotPasswordVerifyOTP(
    data: VerifyOtpRequest,
  ): Promise<VerifyOtpSuccessResponse> {
    const query = this.otpQuery(data);
    const doesUserExist = await this.userRepo.findUser(query);
    if (!doesUserExist)
      throw new APIException(
        UserForgotPasswordErrorMessages.CAN_NOT_GET_USER,
        'CAN_NOT_GET_USER',
        HttpStatus.BAD_REQUEST,
      );

    const verifyOtp = await this.userRepo.verifyOtp({
      ...data,
      otpExpireTime: { $gt: Date.now() },
    });

    if (!verifyOtp)
      throw new APIException(
        VerifyOtpErrorMessages.OTP_EXPIRED_OR_INVALID_OTP,
        'OTP_EXPIRED_OR_INVALID_OTP',
        HttpStatus.BAD_REQUEST,
      );

    return this.helper.serviceResponse.successResponse({
      message: VerifyOtpSuccessMessages.OTP_VERIFIED_SUCCESSFUL,
    });
  }

  async forgotPassword(
    data: UserForgotPasswordRequest,
  ): Promise<UserForgotPasswordSuccessResponse> {
    const query = this.otpQuery(data);
    const user = await this.userRepo.getUserPassword(query);
    if (!user) {
      throw new APIException(
        UserForgotPasswordErrorMessages.CAN_NOT_GET_USER,
        'CAN_NOT_GET_USER',
        HttpStatus.BAD_REQUEST,
      );
    }

    const otpVerified = await this.userRepo.findOtp({
      isVerified: true,
      ...query,
    });
    if (
      !otpVerified ||
      otpVerified.otpVerifiedAt +
        authConfig.forgot_password.set_password_expiration_time <
        Date.now()
    ) {
      throw new APIException(
        CreateUserErrorMessages.TIME_LIMIT_EXCEED_OR_UNVERIFIED_USER,
        'TIME_LIMIT_EXCEED_OR_UNVERIFIED_USER',
        HttpStatus.BAD_REQUEST,
      );
    }

    // change the new password
    user.password = await bcrypt.hash(data.password, authConfig.salt);

    const updatedPassword = await this.userRepo.updateUser(user.id, user);
    if (!updatedPassword) {
      throw new APIException(
        CreateUserErrorMessages.TIME_LIMIT_EXCEED_OR_UNVERIFIED_USER,
        'TIME_LIMIT_EXCEED_OR_UNVERIFIED_USER',
        HttpStatus.BAD_REQUEST,
      );
    }

    await this.userRepo.deleteOtp(query);
    return this.helper.serviceResponse.successResponse({
      message: UserForgotPasswordSuccessMessages.FORGOT_PASSWORD_SUCCESSFUL,
    });
  }

  private async sendOtpForSignUp(
    data: SendOtpForSignupRequest,
  ): Promise<SendOtpSuccessResponse> {
    const query = this.otpQuery(data);

    const otpExists = await this.userRepo.findOtpForSignup(query);
    const randomOtp = 100000 + Math.floor(Math.random() * 900000);

    // Check if the OTP is already in the database.
    if (otpExists) {
      const otpUpdated = await this.userRepo.updateOtpForSignup(query, {
        otp: randomOtp,
        otpExpireTime: Date.now() + authConfig.signup.otp_expiration_time,
        isVerified: false,
        ...data,
      });

      if (!otpUpdated)
        throw new APIException(
          SendOtpErrorMessages.CAN_NOT_UPDATE_OTP,
          'CAN_NOT_UPDATE_OTP',
          HttpStatus.BAD_REQUEST,
        );
    } else {
      const otpSend = await this.userRepo.sendOtpForSignup({
        ...data,
        otp: randomOtp,
        otpExpireTime: Date.now() + authConfig.signup.otp_expiration_time,
      });

      if (!otpSend)
        throw new APIException(
          SendOtpErrorMessages.CAN_NOT_SEND_OTP,
          'CAN_NOT_SEND_OTP',
          HttpStatus.BAD_REQUEST,
        );
    }

    // send otp in email
    if (data?.email) {
      const mailBody = this.otpEmailBody(data.email, randomOtp);
      this.helper.mailService.sendMail(
        data.email,
        `${authConfig.signup.otp_subject}`,
        // `${authConfig.signup.otp_body_prefix} ${randomOtp}`,
        mailBody,
      );
    }
    // send otp in phone
    if (data?.phone) {
      const otpSmsBody = this.otpSmsBody(randomOtp);
      this.helper.smsService.sendSms(otpSmsBody, data.phone);
    }

    return this.helper.serviceResponse.successResponse({
      message: `Your FITSOMNIA OTP is sent to Email`,
    });
  }

  private async sendOtpForForgotPassword(
    data: SendOtpRequest,
  ): Promise<SendOtpSuccessResponse> {
    const query = this.otpQuery(data);
    const otpExists = await this.userRepo.findOtp(query);
    const randomOtp = 100000 + Math.floor(Math.random() * 900000);

    if (otpExists) {
      const otpUpdated = await this.userRepo.updateOtp(query, {
        otp: randomOtp,
        otpExpireTime:
          Date.now() + authConfig.forgot_password.otp_expiration_time,
        isVerified: false,
      });
      if (!otpUpdated)
        throw new APIException(
          SendOtpErrorMessages.CAN_NOT_UPDATE_OTP,
          'CAN_NOT_UPDATE_OTP',
          HttpStatus.BAD_REQUEST,
        );
    } else {
      const otpSend = await this.userRepo.sendOtp({
        ...data,
        otp: randomOtp,
        otpExpireTime:
          Date.now() + authConfig.forgot_password.otp_expiration_time,
      });
      if (!otpSend)
        throw new APIException(
          SendOtpErrorMessages.CAN_NOT_SEND_OTP,
          'CAN_NOT_SEND_OTP',
          HttpStatus.BAD_REQUEST,
        );
    }

    // send otp in email
    if (data?.email) {
      const mailBody = this.otpEmailBody(data.email, randomOtp);
      this.helper.mailService.sendMail(
        data.email,
        `${authConfig.forgot_password.otp_subject}`,
        // `${authConfig.forgot_password.otp_body_prefix} ${randomOtp}`,
        mailBody,
      );
    }

    // send otp in phone
    if (data?.phone) {
      const otpSmsBody = this.otpSmsBody(randomOtp);
      this.helper.smsService.sendSms(otpSmsBody, data.phone);
    }

    return this.helper.serviceResponse.successResponse({
      message: `Your FITSOMNIA OTP is sent to Email`,
    });
  }

  private async generateToken(
    payload: Partial<UserJwtPayload>,
  ): Promise<string> {
    let session = await this.userAuthRepository.getUserSession(payload.id);
    if (session) {
      session = await this.userAuthRepository.updateUserSession(
        {
          sessionId: session.sessionId,
        },
        {
          expireAt: new Date(Date.now() + authConfig.session_expire_time),
          sessionId: randomUUID(),
        },
      );
    } else {
      session = await this.userAuthRepository.createUserSession(payload.id);
    }
    if (session?.sessionId) {
      await this.redis.set(
        `USER_SESSION_${payload.id}`,
        JSON.stringify(session),
      );
      return this.jwtService.sign({ ...payload, sessionId: session.sessionId });
    } else {
      throw new APIException(
        SignInErrorMessages.CAN_NOT_CREATE_SESSION,
        'CAN_NOT_CREATE_SESSION',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  private otpEmailBody(receiverName: string, otp: number): string {
    return `<p>Dear ${receiverName.split('@')[0].toUpperCase()},</p>
        <p>Thank you for using our services! As an added layer of security, we have generated a One-Time Password (OTP) for you to access your fitsomnia account. Please find your OTP details below:</p>
        
        <p>OTP: ${otp}
        
        <p>Please ensure to keep this OTP confidential and do not share it with anyone. It is valid for a limited time.
        If you did not request this OTP or have any concerns about your account's security, please contact our support team <NAME_EMAIL>.</p>
        
        <p>Thank you for your trust and cooperation.</p>
        
        Best regards,<br>
        Support Team<br>
        Fitsomnia<br>`;
  }

  private otpSmsBody(otp: number): string {
    return `Your FITSOMNIA OTP is ${otp}`;
  }

  private otpQuery(
    data: SendOtpRequest | VerifyOtpRequest,
  ): SendOtpRequest | VerifyOtpRequest {
    if (data?.phone) {
      return { phone: data?.phone };
    } else if (data?.email) {
      return { email: data.email };
    } else {
      return;
    }
  }
}
