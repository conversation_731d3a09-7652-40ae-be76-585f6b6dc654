import { Module } from '@nestjs/common';
import { ChatModule } from '../chat/chat.module';
import { ClubRepository } from '../club/repositories';
import { ClubRepositoryHelper } from '../club/repositories/club.repository.helper';
import { PackageModule } from '../package/package.module';

import { TaskModule } from '../task/task.module';
import { UserRepository } from '../user/repositories';
import { SpotRepository } from './repositories/spot';
import { SpotProfileRepository } from './repositories/spotProfile';
import { AdminSpotController } from './rest/admin.spot.controller';
import { SpotController } from './rest/spot';
import { SpotProfileController } from './rest/spotProfile';
import { SpotService } from './services/spot';
import { SpotProfileService } from './services/spotProfile';
import { SpotProfileAddressHelperService } from './services/spotProfile.helper';

@Module({
  imports: [PackageModule, ChatModule, TaskModule],

  controllers: [SpotController, SpotProfileController, AdminSpotController],
  providers: [
    SpotService,
    SpotProfileService,
    SpotRepository,
    SpotProfileRepository,
    UserRepository,
    ClubRepository,
    ClubRepositoryHelper,
    SpotProfileAddressHelperService,
    // RedisDBService ,
  ],
})
export class SpotModule {}
