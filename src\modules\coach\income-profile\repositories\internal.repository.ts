import { Injectable } from "@nestjs/common";
import { CoachIncomeProfileEntityModel } from "../../common/db/coach-profile.model";
import { CoachIncomeProfileEntity } from "../../common/entities/coach-profile.entity";

@Injectable()
export class IncomeProfileRepositoryForInternal {
  async updateIncomeProfileToCurrentAccountBalance(coachId: string,currentAccountBalance:number): Promise<CoachIncomeProfileEntity | null> {
    const updatedProfileDoc = await CoachIncomeProfileEntityModel.findOneAndUpdate(
      { coachId }, // find by coachId
      { $inc: { currentAccountBalance: currentAccountBalance } },// set currentAccountBalance to the new value
      { new: true } // return the updated document
    ).exec();
    return updatedProfileDoc?.toObject();
  }

  async updateAfterRefund(coachId: string, refundAmount: number): Promise<CoachIncomeProfileEntity | null> {
  const amount = parseFloat(refundAmount.toFixed(2));
  const updatedProfileDoc = await CoachIncomeProfileEntityModel.findOneAndUpdate(
    { coachId }, // find by coachId
    { $inc: { currentAccountBalance: -amount } }, // decrease instead of increment
    { new: true } // return the updated document
  ).exec();
  return updatedProfileDoc?.toObject();
  }

 }