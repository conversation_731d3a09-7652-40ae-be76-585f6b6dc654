export class CoachWithdrawEntity {
  id: string;
  userId: string;
  coachId: string;
  mobileNumber: string;
  moneyWithdraw: number;
  bkashTrxId?: string;
  reviewStatus: CoachWithdrawReviewStatus;
  paymentStatus:CoachWithdrawPaymentStatus
  reviewComment?: string;
}

export class CoachWithdrawMobileNumberEntity {
  id: string;
  coachId: string;
  mobileNumber: string;
}

export enum CoachWithdrawReviewStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

export enum CoachWithdrawPaymentStatus {
  Triggered = 'triggered',
  Successfull = 'successfull',
  Failed = 'failed',
}
