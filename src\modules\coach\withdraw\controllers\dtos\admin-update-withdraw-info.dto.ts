import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class UpdateWithdrawRequestDtoForAdmin {
  @ApiProperty({
    required: true,
    description: 'Reason about the review decision',
  })
  @IsString()
  @IsNotEmpty()
  reviewComment: string;

  @ApiProperty({
    required: true,
    type: String,
    description: 'Payment trxId from Bkash',
  })
  @IsString()
  @IsNotEmpty()
  trxId: string;
}
