#!/bin/bash

# Topic Messaging Test Suite Runner
# This script runs all tests related to the topic messaging functionality

echo "🧪 Running Topic Messaging Test Suite..."
echo "========================================"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to run a test file and capture results
run_test() {
    local test_file=$1
    local test_name=$2
    
    echo -e "\n${YELLOW}📋 Running: $test_name${NC}"
    echo "----------------------------------------"
    
    if npm run test:unit -- --testPathPattern="$test_file" --verbose; then
        echo -e "${GREEN}✅ $test_name - PASSED${NC}"
        return 0
    else
        echo -e "${RED}❌ $test_name - FAILED${NC}"
        return 1
    fi
}

# Initialize counters
total_tests=0
passed_tests=0
failed_tests=0

# Test files to run
declare -A tests=(
    ["topic-management.service.unit-spec.ts"]="Topic Management Service Tests"
    ["fcm-topic-messaging.unit-spec.ts"]="FCM Topic Messaging Tests"
    ["daily-reminder-queue.unit-spec.ts"]="Daily Reminder Queue Tests"
    ["user-auth-topic-integration.unit-spec.ts"]="User Auth Topic Integration Tests"
)

echo -e "\n🚀 Starting test execution..."

# Run each test file
for test_file in "${!tests[@]}"; do
    test_name="${tests[$test_file]}"
    total_tests=$((total_tests + 1))
    
    if run_test "$test_file" "$test_name"; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
done

# Print summary
echo -e "\n📊 Test Summary"
echo "==============="
echo -e "Total Tests: $total_tests"
echo -e "${GREEN}Passed: $passed_tests${NC}"
echo -e "${RED}Failed: $failed_tests${NC}"

if [ $failed_tests -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed! Topic messaging implementation is working correctly.${NC}"
    exit 0
else
    echo -e "\n${RED}⚠️  Some tests failed. Please review the output above.${NC}"
    exit 1
fi
