import { Injectable } from "@nestjs/common";
import { CoachIncomeProfileEntityModel } from "../../common/db/coach-profile.model";
import { CoachIncomeProfileEntity } from "../../common/entities/coach-profile.entity";

@Injectable()
export class IncomeProfileRepositoryForCoach {

  async getIncomeProfile(coachId: string): Promise<CoachIncomeProfileEntity | null> {
    const profileDoc = await CoachIncomeProfileEntityModel.findOne({ coachId: coachId }).exec();
    return profileDoc?.toObject();
  }

  async updateIncomeProfile(coachId: string, withdrawableAmount: number): Promise<CoachIncomeProfileEntity | null> {
    const amount = parseFloat(withdrawableAmount.toFixed(2));
    const updatedProfileDoc = await CoachIncomeProfileEntityModel.findOneAndUpdate(
    { coachId }, // find by coachId
    { $inc: { withdrawableAmount: amount } }, // increment instead of replace
    { new: true } // return the updated document
  ).exec();
    return updatedProfileDoc?.toObject();
  }

  async restoreWithdraw(coachId: string, amount: number) {
  return await CoachIncomeProfileEntityModel.findOneAndUpdate(
    { coachId },
    {
      $inc: {
        withdrawableAmount: amount,
        totalWithdrawn: -amount, // if you track this as well
      },
    },
    { new: true }
  ).exec();
}

}