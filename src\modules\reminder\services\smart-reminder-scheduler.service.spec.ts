import { Test, TestingModule } from '@nestjs/testing';
import { SchedulerRegistry } from '@nestjs/schedule';
import { SmartReminderSchedulerService } from './smart-reminder-scheduler.service';
import { ReminderService } from './reminder.service';
import { ReminderRepository } from '../repositories/reminder.repository';

describe('SmartReminderSchedulerService', () => {
  let service: SmartReminderSchedulerService;
  let reminderService: jest.Mocked<ReminderService>;
  let reminderRepository: jest.Mocked<ReminderRepository>;
  let schedulerRegistry: jest.Mocked<SchedulerRegistry>;

  beforeEach(async () => {
    const mockReminderService = {
      sendReminderNotifications: jest.fn(),
      scheduleAllActiveReminders: jest.fn(),
    };

    const mockReminderRepository = {
      findAllReminders: jest.fn(),
    };

    const mockSchedulerRegistry = {
      addCronJob: jest.fn(),
      deleteCronJob: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SmartReminderSchedulerService,
        {
          provide: ReminderService,
          useValue: mockReminderService,
        },
        {
          provide: ReminderRepository,
          useValue: mockReminderRepository,
        },
        {
          provide: SchedulerRegistry,
          useValue: mockSchedulerRegistry,
        },
      ],
    }).compile();

    service = module.get<SmartReminderSchedulerService>(SmartReminderSchedulerService);
    reminderService = module.get(ReminderService);
    reminderRepository = module.get(ReminderRepository);
    schedulerRegistry = module.get(SchedulerRegistry);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('buildReminderCache', () => {
    it('should build cache from active reminders', async () => {
      const mockReminders = [
        { time: '09:00', isActive: true },
        { time: '14:30', isActive: true },
        { time: '18:00', isActive: false }, // Should be filtered out
        { time: '21:00', isActive: true },
      ];

      reminderRepository.findAllReminders.mockResolvedValue(mockReminders as any);

      // Access private method for testing
      await (service as any).buildReminderCache();

      const status = service.getSchedulerStatus();
      expect(status.cacheSize).toBe(3); // Only active reminders
    });
  });

  describe('shouldCheckReminders', () => {
    it('should return true for exact time match', async () => {
      const mockReminders = [
        { time: '09:00', isActive: true },
        { time: '14:30', isActive: true },
      ];

      reminderRepository.findAllReminders.mockResolvedValue(mockReminders as any);
      await (service as any).buildReminderCache();

      const shouldCheck = (service as any).shouldCheckReminders('09:00');
      expect(shouldCheck).toBe(true);
    });

    it('should return false for time with no reminders', async () => {
      const mockReminders = [
        { time: '09:00', isActive: true },
        { time: '14:30', isActive: true },
      ];

      reminderRepository.findAllReminders.mockResolvedValue(mockReminders as any);
      await (service as any).buildReminderCache();

      const shouldCheck = (service as any).shouldCheckReminders('12:00');
      expect(shouldCheck).toBe(false);
    });

    it('should return true for nearby time slots (±2 minutes)', async () => {
      const mockReminders = [
        { time: '09:00', isActive: true },
      ];

      reminderRepository.findAllReminders.mockResolvedValue(mockReminders as any);
      await (service as any).buildReminderCache();

      // Should find reminder at 09:00 when checking 09:02
      const shouldCheck = (service as any).shouldCheckReminders('09:02');
      expect(shouldCheck).toBe(true);
    });
  });

  describe('getPeakHourAdjustment', () => {
    it('should identify morning peak hours', () => {
      const morningPeakTime = new Date();
      morningPeakTime.setHours(8, 0, 0, 0); // 8 AM

      const adjustment = (service as any).getPeakHourAdjustment(morningPeakTime);
      expect(adjustment.skip).toBe(false);
      expect(adjustment.frequencyMultiplier).toBe(1.5);
    });

    it('should identify evening peak hours', () => {
      const eveningPeakTime = new Date();
      eveningPeakTime.setHours(19, 0, 0, 0); // 7 PM

      const adjustment = (service as any).getPeakHourAdjustment(eveningPeakTime);
      expect(adjustment.skip).toBe(false);
      expect(adjustment.frequencyMultiplier).toBe(1.5);
    });

    it('should identify off-peak hours', () => {
      const offPeakTime = new Date();
      offPeakTime.setHours(2, 0, 0, 0); // 2 AM

      const adjustment = (service as any).getPeakHourAdjustment(offPeakTime);
      expect(adjustment.frequencyMultiplier).toBe(0.3);
      // Skip is probabilistic, so we just check it's a boolean
      expect(typeof adjustment.skip).toBe('boolean');
    });

    it('should identify normal hours', () => {
      const normalTime = new Date();
      normalTime.setHours(13, 0, 0, 0); // 1 PM

      const adjustment = (service as any).getPeakHourAdjustment(normalTime);
      expect(adjustment.skip).toBe(false);
      expect(adjustment.frequencyMultiplier).toBe(1.0);
    });
  });

  describe('isServerOverloaded', () => {
    it('should detect high CPU usage', () => {
      (service as any).serverLoad = {
        cpuUsage: 85,
        memoryUsage: 50,
        lastUpdated: new Date()
      };

      const isOverloaded = (service as any).isServerOverloaded();
      expect(isOverloaded).toBe(true);
    });

    it('should detect high memory usage', () => {
      (service as any).serverLoad = {
        cpuUsage: 50,
        memoryUsage: 90,
        lastUpdated: new Date()
      };

      const isOverloaded = (service as any).isServerOverloaded();
      expect(isOverloaded).toBe(true);
    });

    it('should not detect overload with normal usage', () => {
      (service as any).serverLoad = {
        cpuUsage: 40,
        memoryUsage: 60,
        lastUpdated: new Date()
      };

      const isOverloaded = (service as any).isServerOverloaded();
      expect(isOverloaded).toBe(false);
    });
  });

  describe('getSchedulerStatus', () => {
    it('should return current scheduler status', () => {
      const status = service.getSchedulerStatus();
      
      expect(status).toHaveProperty('currentSchedule');
      expect(status).toHaveProperty('serverLoad');
      expect(status).toHaveProperty('cacheSize');
      expect(status).toHaveProperty('consecutiveEmptyChecks');
      expect(status).toHaveProperty('lastCheck');
      
      expect(status.currentSchedule.interval).toBe(15); // Default interval
      expect(status.currentSchedule.reason).toBe('Initial optimized schedule');
    });
  });

  describe('refreshReminderCache', () => {
    it('should refresh the reminder cache', async () => {
      const mockReminders = [
        { time: '10:00', isActive: true },
        { time: '15:00', isActive: true },
      ];

      reminderRepository.findAllReminders.mockResolvedValue(mockReminders as any);

      await service.refreshReminderCache();

      const status = service.getSchedulerStatus();
      expect(status.cacheSize).toBe(2);
    });
  });
});
