import { Test, TestingModule } from '@nestjs/testing';
import { SchedulerRegistry } from '@nestjs/schedule';
import { SmartReminderSchedulerService } from './smart-reminder-scheduler.service';
import { ReminderService } from './reminder.service';
import { SMART_SCHEDULER_CONFIG } from '../../../config/smart-scheduler.config';

// Mock Redis
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    status: 'ready',
    on: jest.fn(),
    quit: jest.fn(),
  }));
});

describe('SmartReminderSchedulerService', () => {
  let service: SmartReminderSchedulerService;
  let reminderService: jest.Mocked<ReminderService>;
  let schedulerRegistry: jest.Mocked<SchedulerRegistry>;

  const mockReminderService = {
    getAllReminders: jest.fn(),
    scheduleAllActiveReminders: jest.fn(),
    sendReminderNotifications: jest.fn(),
  };

  const mockSchedulerRegistry = {
    addCronJob: jest.fn(),
    deleteCronJob: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SmartReminderSchedulerService,
        {
          provide: ReminderService,
          useValue: mockReminderService,
        },
        {
          provide: SchedulerRegistry,
          useValue: mockSchedulerRegistry,
        },
      ],
    }).compile();

    service = module.get<SmartReminderSchedulerService>(SmartReminderSchedulerService);
    reminderService = module.get(ReminderService);
    schedulerRegistry = module.get(SchedulerRegistry);

    // Reset all mocks
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should initialize with default configuration', () => {
      const metrics = service.getMetrics();
      expect(metrics.currentInterval).toBe(SMART_SCHEDULER_CONFIG.defaultInterval);
      expect(metrics.consecutiveEmptyChecks).toBe(0);
      expect(metrics.totalRuns).toBe(0);
    });
  });

  describe('cache management', () => {
    it('should refresh reminder cache', async () => {
      const mockReminders = [
        { id: '1', title: 'Test 1', time: '09:00', isActive: true },
        { id: '2', title: 'Test 2', time: '14:00', isActive: true },
        { id: '3', title: 'Test 3', time: '18:00', isActive: false },
      ];

      reminderService.getAllReminders.mockResolvedValue(mockReminders as any);

      await service.forceCacheRefresh();

      expect(reminderService.getAllReminders).toHaveBeenCalled();
    });

    it('should handle cache refresh errors gracefully', async () => {
      reminderService.getAllReminders.mockRejectedValue(new Error('Database error'));

      await expect(service.forceCacheRefresh()).resolves.not.toThrow();
    });
  });

  describe('server metrics', () => {
    it('should calculate server metrics', async () => {
      // Access private method for testing
      const getServerMetrics = (service as any).getServerMetrics.bind(service);
      const metrics = await getServerMetrics();

      expect(metrics).toHaveProperty('cpuUsage');
      expect(metrics).toHaveProperty('memoryUsage');
      expect(metrics).toHaveProperty('timestamp');
      expect(typeof metrics.cpuUsage).toBe('number');
      expect(typeof metrics.memoryUsage).toBe('number');
    });
  });

  describe('interval calculation', () => {
    it('should return high interval for high server load', () => {
      const getIntervalFromServerLoad = (service as any).getIntervalFromServerLoad.bind(service);
      const highLoadMetrics = {
        cpuUsage: 85,
        memoryUsage: 90,
        timestamp: Date.now(),
      };

      const interval = getIntervalFromServerLoad(highLoadMetrics);
      expect(interval).toBe(SMART_SCHEDULER_CONFIG.serverLoad.high.interval);
    });

    it('should return medium interval for medium server load', () => {
      const getIntervalFromServerLoad = (service as any).getIntervalFromServerLoad.bind(service);
      const mediumLoadMetrics = {
        cpuUsage: 65,
        memoryUsage: 75,
        timestamp: Date.now(),
      };

      const interval = getIntervalFromServerLoad(mediumLoadMetrics);
      expect(interval).toBe(SMART_SCHEDULER_CONFIG.serverLoad.medium.interval);
    });

    it('should return low interval for low server load', () => {
      const getIntervalFromServerLoad = (service as any).getIntervalFromServerLoad.bind(service);
      const lowLoadMetrics = {
        cpuUsage: 25,
        memoryUsage: 40,
        timestamp: Date.now(),
      };

      const interval = getIntervalFromServerLoad(lowLoadMetrics);
      expect(interval).toBe(SMART_SCHEDULER_CONFIG.serverLoad.low.interval);
    });
  });

  describe('peak hour adjustments', () => {
    it('should return consistent frequency for 24/7 operation', () => {
      const getPeakHourAdjustment = (service as any).getPeakHourAdjustment.bind(service);

      // Test 2:00 AM (should be consistent now)
      const nightTime = new Date();
      nightTime.setHours(2, 0, 0, 0);

      const adjustment = getPeakHourAdjustment(nightTime);
      expect(adjustment.frequencyMultiplier).toBe(1.0);
      expect(adjustment.skip).toBe(false);
    });

    it('should return consistent frequency for morning hours', () => {
      const getPeakHourAdjustment = (service as any).getPeakHourAdjustment.bind(service);

      // Test 8:00 AM (should be consistent now)
      const morningTime = new Date();
      morningTime.setHours(8, 0, 0, 0);

      const adjustment = getPeakHourAdjustment(morningTime);
      expect(adjustment.frequencyMultiplier).toBe(1.0);
      expect(adjustment.skip).toBe(false);
    });

    it('should return consistent frequency for evening hours', () => {
      const getPeakHourAdjustment = (service as any).getPeakHourAdjustment.bind(service);

      // Test 7:00 PM (should be consistent now)
      const eveningTime = new Date();
      eveningTime.setHours(19, 0, 0, 0);

      const adjustment = getPeakHourAdjustment(eveningTime);
      expect(adjustment.frequencyMultiplier).toBe(1.0);
      expect(adjustment.skip).toBe(false);
    });

    it('should return consistent frequency for all hours (24/7 operation)', () => {
      const getPeakHourAdjustment = (service as any).getPeakHourAdjustment.bind(service);

      // Test multiple hours throughout the day
      const testHours = [0, 6, 12, 18, 23];

      testHours.forEach(hour => {
        const testTime = new Date();
        testTime.setHours(hour, 0, 0, 0);

        const adjustment = getPeakHourAdjustment(testTime);
        expect(adjustment.frequencyMultiplier).toBe(1.0);
        expect(adjustment.skip).toBe(false);
      });
    });
  });

  describe('metrics tracking', () => {
    it('should track scheduler metrics correctly', () => {
      const initialMetrics = service.getMetrics();
      
      expect(initialMetrics).toHaveProperty('lastRun');
      expect(initialMetrics).toHaveProperty('consecutiveEmptyChecks');
      expect(initialMetrics).toHaveProperty('currentInterval');
      expect(initialMetrics).toHaveProperty('totalRuns');
      expect(initialMetrics).toHaveProperty('successfulRuns');
      expect(initialMetrics).toHaveProperty('failedRuns');
    });

    it('should return a copy of metrics to prevent external modification', () => {
      const metrics1 = service.getMetrics();
      const metrics2 = service.getMetrics();
      
      expect(metrics1).not.toBe(metrics2); // Different object references
      expect(metrics1).toEqual(metrics2); // Same content
    });
  });

  describe('error handling', () => {
    it('should handle reminder service errors gracefully', async () => {
      reminderService.sendReminderNotifications.mockRejectedValue(new Error('Service error'));
      
      const checkAndSendReminders = (service as any).checkAndSendReminders.bind(service);
      const result = await checkAndSendReminders();
      
      expect(result).toBe(false);
    });

    it('should handle scheduler registry errors gracefully', async () => {
      schedulerRegistry.deleteCronJob.mockImplementation(() => {
        throw new Error('Registry error');
      });
      
      // Should not throw when trying to delete non-existent job
      const scheduleNextRun = (service as any).scheduleNextRun.bind(service);
      await expect(scheduleNextRun()).resolves.not.toThrow();
    });
  });
});
