import { model, Schema } from 'mongoose';
import { randomUUID } from 'crypto';
import { User, UserAddress } from 'src/entity/user';
import { TagType } from 'models';

const UserAddressSchema = new Schema<UserAddress>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      index: true,
    },
    firstName: {
      type: String,
      required: true,
    },
    lastName: {
      type: String,
      required: true,
    },
    addressLine1: {
      type: String,
      required: true,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    addressLine2: String,
    company: String,
    city: {
      type: String,
      required: true,
    },
    state: String,
    country: String,
    postCode: String,
    phone: {
      type: String,
      required: true,
    },
    tag: {
      type: String,
      enum: [TagType.HOME, TagType.OFFICE, TagType.OTHERS],
      default: TagType.HOME,
    },
  },
  {
    _id: false,
    timestamps: false,
    versionKey: false,
  },
);

const UserSchema = new Schema<User>(
  {
    id: {
      type: String,
      unique: true,
      default: () => randomUUID(),
    },
    name: {
      type: String,
      default: '',
      index: 'text',
    },
    phone: {
      type: String,
      index: true,
    },
    email: {
      type: String,
      default: '',
      index: true,
    },
    password: {
      type: String,
      required: true,
      index: true,
    },
    addresses: [UserAddressSchema],
    fcmToken: {
      type: String,
      default: null,
    },
    title: {
      type: String,
      default: null,
    },
    organization: {
      type: String,
      default: null,
    },
    workoutType: {
      type: String,
      default: null,
    },
    bodyType: {
      type: String,
      default: null,
    },
    dateOfBirth: {
      type: Date,
      default: null,
    },
    gender: {
      type: String,
      default: null,
    },
    maxBench: {
      type: Number,
      default: 0,
    },
    maxSquat: {
      type: Number,
      default: 0,
    },
    weightType: {
      type: String,
      enum: ['lb', 'kg'],
      default: 'lb',
    },
    location: {
      type: {
        type: String,
        enum: ['Point'],
      },
      coordinates: [Number],
    },
    image: {
      profile: {
        type: String,
        default: null,
      },
    },
    isOnline: {
      type: Boolean,
      default: false,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    timezone: {
      type: String,
      default: 'UTC',
      validate: {
        validator: function(v: string) {
          // Basic timezone validation - accepts common timezone formats
          return /^[A-Za-z_\/\-\+0-9:]+$/.test(v);
        },
        message: 'Invalid timezone format'
      }
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

UserSchema.index({ location: '2dsphere' });
const UserModel = model<User>('User', UserSchema);
export { UserModel };
