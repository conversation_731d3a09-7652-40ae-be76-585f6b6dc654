import { DailyReminderSenderQueue } from 'src/queue-system/worker/sender/reminder/daily-reminder';
import { FcmService } from 'src/helper/fcmService';
import { FCMSenderPayload } from 'src/queue-system/types';

// Mock the FcmService
jest.mock('src/helper/fcmService');

describe('DailyReminderSenderQueue', () => {
  let mockFcmService: jest.Mocked<FcmService>;

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    // Create a fresh mock instance
    mockFcmService = new FcmService() as jest.Mocked<FcmService>;

    // Mock the constructor to return our mock instance
    (FcmService as jest.MockedClass<typeof FcmService>).mockImplementation(
      () => mockFcmService
    );
  });

  describe('sender - Topic Broadcasting', () => {
    const mockTopicPayload: FCMSenderPayload = {
      title: 'Daily Workout Reminder',
      body: 'Time for your daily workout! Stay consistent with your fitness goals.',
      task: 'topic-sender',
      topic: 'all-reminders',
      documentId: 'reminder-123',
      data: { type: 'reminder', id: 'reminder-123', category: 'workout' },
      isHighPriority: true,
      token: '', // Required field
      payloadType: 'reminder', // Required field
    };

    it('should successfully send topic broadcast with valid payload', async () => {
      mockFcmService.sendToTopic.mockResolvedValue('msg-123');

      await DailyReminderSenderQueue.sender(mockTopicPayload);

      expect(mockFcmService.sendToTopic).toHaveBeenCalledWith({
        title: 'Daily Workout Reminder',
        body: 'Time for your daily workout! Stay consistent with your fitness goals.',
        documentId: 'reminder-123',
        data: { type: 'reminder', id: 'reminder-123', category: 'workout' },
        topic: 'all-reminders',
        isHighPriority: true,
        ttl: 3600,
      });
    });

    it('should handle missing topic gracefully', async () => {
      const invalidPayload = { ...mockTopicPayload, topic: undefined };
      
      await DailyReminderSenderQueue.sender(invalidPayload);

      expect(mockFcmService.sendToTopic).not.toHaveBeenCalled();
    });

    it('should handle missing title gracefully', async () => {
      const invalidPayload = { ...mockTopicPayload, title: undefined };
      
      await DailyReminderSenderQueue.sender(invalidPayload);

      expect(mockFcmService.sendToTopic).not.toHaveBeenCalled();
    });

    it('should handle missing body gracefully', async () => {
      const invalidPayload = { ...mockTopicPayload, body: undefined };
      
      await DailyReminderSenderQueue.sender(invalidPayload);

      expect(mockFcmService.sendToTopic).not.toHaveBeenCalled();
    });

    it('should handle FCM service errors gracefully', async () => {
      mockFcmService.sendToTopic.mockRejectedValue(new Error('FCM service error'));

      // Should not throw an error
      await expect(DailyReminderSenderQueue.sender(mockTopicPayload)).resolves.not.toThrow();

      expect(mockFcmService.sendToTopic).toHaveBeenCalledWith({
        title: 'Daily Workout Reminder',
        body: 'Time for your daily workout! Stay consistent with your fitness goals.',
        documentId: 'reminder-123',
        data: { type: 'reminder', id: 'reminder-123', category: 'workout' },
        topic: 'all-reminders',
        isHighPriority: true,
        ttl: 3600,
      });
    });

    it('should set default isHighPriority to true when not specified', async () => {
      const payloadWithoutPriority = { ...mockTopicPayload, isHighPriority: undefined };
      mockFcmServiceHelper.sendToTopic.mockResolvedValue(true);

      await DailyReminderSenderQueue.sender(payloadWithoutPriority);

      expect(mockFcmServiceHelper.sendToTopic).toHaveBeenCalledWith(
        expect.objectContaining({
          isHighPriority: true,
        })
      );
    });
  });

  describe('sender - Individual Token Messaging', () => {
    const mockIndividualPayload: FCMSenderPayload = {
      title: 'Personal Reminder',
      body: 'Your personalized workout reminder',
      token: 'user-fcm-token-123',
      documentId: 'reminder-456',
      data: { type: 'personal', userId: 'user-123' },
      isHighPriority: false,
      payloadType: 'individual',
      task: 'daily-reminder',
    };

    it('should successfully send individual notification', async () => {
      mockFcmService.sendToIndividual.mockResolvedValue(true);

      await DailyReminderSenderQueue.sender(mockIndividualPayload);

      expect(mockFcmService.sendToIndividual).toHaveBeenCalledWith({
        token: 'user-fcm-token-123',
        title: 'Personal Reminder',
        body: 'Your personalized workout reminder',
        documentId: 'reminder-456',
        data: { type: 'personal', userId: 'user-123' },
        isHighPriority: false,
        ttl: 3600,
      });
    });

    it('should handle individual notification failures gracefully', async () => {
      mockFcmService.sendToIndividual.mockResolvedValue(false);

      await expect(DailyReminderSenderQueue.sender(mockIndividualPayload)).resolves.not.toThrow();

      expect(mockFcmService.sendToIndividual).toHaveBeenCalled();
    });

    it('should handle FCM service errors for individual notifications', async () => {
      mockFcmService.sendToIndividual.mockRejectedValue(new Error('Token invalid'));

      await expect(DailyReminderSenderQueue.sender(mockIndividualPayload)).resolves.not.toThrow();

      expect(mockFcmService.sendToIndividual).toHaveBeenCalled();
    });
  });

  describe('sender - Batch Token Messaging', () => {
    const mockBatchPayload: FCMSenderPayload = {
      title: 'Group Reminder',
      body: 'Time for your group workout session!',
      token: 'token1', // Required field
      tokens: ['token1', 'token2', 'token3'],
      documentId: 'reminder-789',
      data: { type: 'group', sessionId: 'session-456' },
      isHighPriority: true,
      payloadType: 'batch',
      task: 'daily-reminder',
    };

    it('should successfully send batch notifications', async () => {
      mockFcmService.sendToMany.mockResolvedValue(true);

      await DailyReminderSenderQueue.sender(mockBatchPayload);

      expect(mockFcmService.sendToMany).toHaveBeenCalledWith(
        ['token1', 'token2', 'token3'],
        'Group Reminder',
        'Time for your group workout session!',
        'reminder-789',
        { type: 'group', sessionId: 'session-456' },
        true,
        3600
      );
    });

    it('should handle empty tokens array gracefully', async () => {
      const emptyTokensPayload = { ...mockBatchPayload, tokens: [] };

      await DailyReminderSenderQueue.sender(emptyTokensPayload);

      expect(mockFcmService.sendToMany).not.toHaveBeenCalled();
    });

    it('should handle batch notification failures gracefully', async () => {
      mockFcmService.sendToMany.mockResolvedValue(false);

      await expect(DailyReminderSenderQueue.sender(mockBatchPayload)).resolves.not.toThrow();

      expect(mockFcmService.sendToMany).toHaveBeenCalled();
    });
  });

  describe('sender - Invalid Payload Handling', () => {
    it('should handle completely invalid payload gracefully', async () => {
      const invalidPayload = {
        title: 'Test',
        // Missing required fields
      } as FCMSenderPayload;

      await expect(DailyReminderSenderQueue.sender(invalidPayload)).resolves.not.toThrow();

      expect(mockFcmService.sendToTopic).not.toHaveBeenCalled();
      expect(mockFcmService.sendToIndividual).not.toHaveBeenCalled();
      expect(mockFcmService.sendToMany).not.toHaveBeenCalled();
    });

    it('should handle payload with missing body gracefully', async () => {
      const payloadWithoutBody = {
        title: 'Test Title',
        task: 'topic-sender',
        topic: 'test-topic',
        // Missing body
      } as FCMSenderPayload;

      await DailyReminderSenderQueue.sender(payloadWithoutBody);

      expect(mockFcmService.sendToTopic).not.toHaveBeenCalled();
    });
  });

  describe('sender - Error Handling and Logging', () => {
    it('should handle FCM quota exceeded errors', async () => {
      const quotaError = new Error('Quota exceeded');
      quotaError.name = 'messaging/quota-exceeded';
      mockFcmService.sendToTopic.mockRejectedValue(quotaError);

      const mockTopicPayload: FCMSenderPayload = {
        title: 'Test',
        body: 'Test body',
        task: 'topic-sender',
        topic: 'test-topic',
        token: 'dummy-token', // Required field
        isHighPriority: false,
        payloadType: 'topic',
        documentId: 'reminder-123',
      };

      await expect(DailyReminderSenderQueue.sender(mockTopicPayload)).resolves.not.toThrow();
    });

    it('should handle invalid token errors', async () => {
      const tokenError = new Error('Invalid registration token');
      tokenError.name = 'messaging/registration-token-not-registered';
      mockFcmService.sendToIndividual.mockRejectedValue(tokenError);

      const mockIndividualPayload: FCMSenderPayload = {
        title: 'Test',
        body: 'Test body',
        token: 'invalid-token',
        isHighPriority: false,
        payloadType: 'individual',
        task: 'daily-reminder',
        documentId: 'reminder-123',
      };

      await expect(DailyReminderSenderQueue.sender(mockIndividualPayload)).resolves.not.toThrow();
    });

    it('should handle unexpected errors gracefully', async () => {
      const unexpectedError = new Error('Unexpected FCM error');
      mockFcmService.sendToTopic.mockRejectedValue(unexpectedError);

      const mockTopicPayload: FCMSenderPayload = {
        title: 'Test',
        body: 'Test body',
        task: 'topic-sender',
        topic: 'test-topic',
        token: 'dummy-token', // Required field
        isHighPriority: false,
        payloadType: 'topic',
        documentId: 'reminder-123',
      };

      await expect(DailyReminderSenderQueue.sender(mockTopicPayload)).resolves.not.toThrow();
    });
  });
});
