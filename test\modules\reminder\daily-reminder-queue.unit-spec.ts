import { DailyReminderSenderQueue } from 'src/queue-system/worker/sender/reminder/daily-reminder';
import { FcmServiceHelper } from 'src/helper/fcmService';
import { FCMSenderPayload } from 'src/queue-system/types/fcm.sender.payload';

// Mock the FcmServiceHelper
jest.mock('src/helper/fcmService');

describe('DailyReminderSenderQueue', () => {
  let mockFcmServiceHelper: jest.Mocked<FcmServiceHelper>;

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    
    // Create a fresh mock instance
    mockFcmServiceHelper = new FcmServiceHelper() as jest.Mocked<FcmServiceHelper>;
    
    // Mock the constructor to return our mock instance
    (FcmServiceHelper as jest.MockedClass<typeof FcmServiceHelper>).mockImplementation(
      () => mockFcmServiceHelper
    );
  });

  describe('sender - Topic Broadcasting', () => {
    const mockTopicPayload: FCMSenderPayload = {
      title: 'Daily Workout Reminder',
      body: 'Time for your daily workout! Stay consistent with your fitness goals.',
      task: 'topic-sender',
      topic: 'all-reminders',
      documentId: 'reminder-123',
      data: { type: 'reminder', id: 'reminder-123', category: 'workout' },
      isHighPriority: true,
    };

    it('should successfully send topic broadcast with valid payload', async () => {
      mockFcmServiceHelper.sendToTopic.mockResolvedValue(true);

      await DailyReminderSenderQueue.sender(mockTopicPayload);

      expect(mockFcmServiceHelper.sendToTopic).toHaveBeenCalledWith({
        title: 'Daily Workout Reminder',
        body: 'Time for your daily workout! Stay consistent with your fitness goals.',
        documentId: 'reminder-123',
        data: { type: 'reminder', id: 'reminder-123', category: 'workout' },
        topic: 'all-reminders',
        isHighPriority: true,
        ttl: 3600,
      });
    });

    it('should handle missing topic gracefully', async () => {
      const invalidPayload = { ...mockTopicPayload, topic: undefined };
      
      await DailyReminderSenderQueue.sender(invalidPayload);

      expect(mockFcmServiceHelper.sendToTopic).not.toHaveBeenCalled();
    });

    it('should handle missing title gracefully', async () => {
      const invalidPayload = { ...mockTopicPayload, title: undefined };
      
      await DailyReminderSenderQueue.sender(invalidPayload);

      expect(mockFcmServiceHelper.sendToTopic).not.toHaveBeenCalled();
    });

    it('should handle missing body gracefully', async () => {
      const invalidPayload = { ...mockTopicPayload, body: undefined };
      
      await DailyReminderSenderQueue.sender(invalidPayload);

      expect(mockFcmServiceHelper.sendToTopic).not.toHaveBeenCalled();
    });

    it('should handle FCM service errors gracefully', async () => {
      mockFcmServiceHelper.sendToTopic.mockRejectedValue(new Error('FCM service error'));

      // Should not throw an error
      await expect(DailyReminderSenderQueue.sender(mockTopicPayload)).resolves.not.toThrow();

      expect(mockFcmServiceHelper.sendToTopic).toHaveBeenCalledWith({
        title: 'Daily Workout Reminder',
        body: 'Time for your daily workout! Stay consistent with your fitness goals.',
        documentId: 'reminder-123',
        data: { type: 'reminder', id: 'reminder-123', category: 'workout' },
        topic: 'all-reminders',
        isHighPriority: true,
        ttl: 3600,
      });
    });

    it('should set default isHighPriority to true when not specified', async () => {
      const payloadWithoutPriority = { ...mockTopicPayload, isHighPriority: undefined };
      mockFcmServiceHelper.sendToTopic.mockResolvedValue(true);

      await DailyReminderSenderQueue.sender(payloadWithoutPriority);

      expect(mockFcmServiceHelper.sendToTopic).toHaveBeenCalledWith(
        expect.objectContaining({
          isHighPriority: true,
        })
      );
    });
  });

  describe('sender - Individual Token Messaging', () => {
    const mockIndividualPayload: FCMSenderPayload = {
      title: 'Personal Reminder',
      body: 'Your personalized workout reminder',
      token: 'user-fcm-token-123',
      documentId: 'reminder-456',
      data: { type: 'personal', userId: 'user-123' },
      isHighPriority: false,
    };

    it('should successfully send individual notification', async () => {
      mockFcmServiceHelper.sendToIndividual.mockResolvedValue(true);

      await DailyReminderSenderQueue.sender(mockIndividualPayload);

      expect(mockFcmServiceHelper.sendToIndividual).toHaveBeenCalledWith({
        token: 'user-fcm-token-123',
        title: 'Personal Reminder',
        body: 'Your personalized workout reminder',
        documentId: 'reminder-456',
        data: { type: 'personal', userId: 'user-123' },
        isHighPriority: false,
        ttl: 3600,
      });
    });

    it('should handle individual notification failures gracefully', async () => {
      mockFcmServiceHelper.sendToIndividual.mockResolvedValue(false);

      await expect(DailyReminderSenderQueue.sender(mockIndividualPayload)).resolves.not.toThrow();

      expect(mockFcmServiceHelper.sendToIndividual).toHaveBeenCalled();
    });

    it('should handle FCM service errors for individual notifications', async () => {
      mockFcmServiceHelper.sendToIndividual.mockRejectedValue(new Error('Token invalid'));

      await expect(DailyReminderSenderQueue.sender(mockIndividualPayload)).resolves.not.toThrow();

      expect(mockFcmServiceHelper.sendToIndividual).toHaveBeenCalled();
    });
  });

  describe('sender - Batch Token Messaging', () => {
    const mockBatchPayload: FCMSenderPayload = {
      title: 'Group Reminder',
      body: 'Time for your group workout session!',
      tokens: ['token1', 'token2', 'token3'],
      documentId: 'reminder-789',
      data: { type: 'group', sessionId: 'session-456' },
      isHighPriority: true,
    };

    it('should successfully send batch notifications', async () => {
      mockFcmServiceHelper.sendToMany.mockResolvedValue(true);

      await DailyReminderSenderQueue.sender(mockBatchPayload);

      expect(mockFcmServiceHelper.sendToMany).toHaveBeenCalledWith(
        ['token1', 'token2', 'token3'],
        'Group Reminder',
        'Time for your group workout session!',
        'reminder-789',
        { type: 'group', sessionId: 'session-456' },
        true,
        3600
      );
    });

    it('should handle empty tokens array gracefully', async () => {
      const emptyTokensPayload = { ...mockBatchPayload, tokens: [] };

      await DailyReminderSenderQueue.sender(emptyTokensPayload);

      expect(mockFcmServiceHelper.sendToMany).not.toHaveBeenCalled();
    });

    it('should handle batch notification failures gracefully', async () => {
      mockFcmServiceHelper.sendToMany.mockResolvedValue(false);

      await expect(DailyReminderSenderQueue.sender(mockBatchPayload)).resolves.not.toThrow();

      expect(mockFcmServiceHelper.sendToMany).toHaveBeenCalled();
    });
  });

  describe('sender - Invalid Payload Handling', () => {
    it('should handle completely invalid payload gracefully', async () => {
      const invalidPayload = {
        title: 'Test',
        // Missing required fields
      } as FCMSenderPayload;

      await expect(DailyReminderSenderQueue.sender(invalidPayload)).resolves.not.toThrow();

      expect(mockFcmServiceHelper.sendToTopic).not.toHaveBeenCalled();
      expect(mockFcmServiceHelper.sendToIndividual).not.toHaveBeenCalled();
      expect(mockFcmServiceHelper.sendToMany).not.toHaveBeenCalled();
    });

    it('should handle payload with missing body gracefully', async () => {
      const payloadWithoutBody = {
        title: 'Test Title',
        task: 'topic-sender',
        topic: 'test-topic',
        // Missing body
      } as FCMSenderPayload;

      await DailyReminderSenderQueue.sender(payloadWithoutBody);

      expect(mockFcmServiceHelper.sendToTopic).not.toHaveBeenCalled();
    });
  });

  describe('sender - Error Handling and Logging', () => {
    it('should handle FCM quota exceeded errors', async () => {
      const quotaError = new Error('Quota exceeded');
      quotaError.name = 'messaging/quota-exceeded';
      mockFcmServiceHelper.sendToTopic.mockRejectedValue(quotaError);

      const mockTopicPayload: FCMSenderPayload = {
        title: 'Test',
        body: 'Test body',
        task: 'topic-sender',
        topic: 'test-topic',
      };

      await expect(DailyReminderSenderQueue.sender(mockTopicPayload)).resolves.not.toThrow();
    });

    it('should handle invalid token errors', async () => {
      const tokenError = new Error('Invalid registration token');
      tokenError.name = 'messaging/registration-token-not-registered';
      mockFcmServiceHelper.sendToIndividual.mockRejectedValue(tokenError);

      const mockIndividualPayload: FCMSenderPayload = {
        title: 'Test',
        body: 'Test body',
        token: 'invalid-token',
      };

      await expect(DailyReminderSenderQueue.sender(mockIndividualPayload)).resolves.not.toThrow();
    });

    it('should handle unexpected errors gracefully', async () => {
      const unexpectedError = new Error('Unexpected FCM error');
      mockFcmServiceHelper.sendToTopic.mockRejectedValue(unexpectedError);

      const mockTopicPayload: FCMSenderPayload = {
        title: 'Test',
        body: 'Test body',
        task: 'topic-sender',
        topic: 'test-topic',
      };

      await expect(DailyReminderSenderQueue.sender(mockTopicPayload)).resolves.not.toThrow();
    });
  });
});
