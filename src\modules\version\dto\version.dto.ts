import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';
import { Version } from 'src/entity/version';

// Create Version DTOs
export class CreateVersionRequestDto {
  @ApiProperty({
    description: 'Version string',
    example: '1.0.0',
  })
  @IsString()
  @IsNotEmpty()
  version: string;

  @ApiProperty({
    description: 'Whether to force update',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  forceUpdate?: boolean;
}

export class CreateVersionSuccessResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Version data',
    type: Version,
  })
  data: Version;
}

// Get Version DTOs
export class GetVersionParamDto {
  @ApiProperty({
    description: 'Version ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  versionId: string;
}

export class GetVersionSuccessResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Version data',
    type: Version,
  })
  data: Version;
}

export class GetVersionsSuccessResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Versions data',
    type: [Version],
  })
  data: Version[];
}

// Update Version DTOs
export class UpdateVersionParamDto {
  @ApiProperty({
    description: 'Version ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  versionId: string;
}

export class UpdateVersionRequestDto {
  @ApiProperty({
    description: 'Version string',
    example: '1.0.1',
  })
  @IsString()
  @IsOptional()
  version?: string;

  @ApiProperty({
    description: 'Whether to force update',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  forceUpdate?: boolean;
}

export class UpdateVersionSuccessResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Updated version data',
    type: Version,
  })
  data: Version;
}

// Delete Version DTOs
export class DeleteVersionParamDto {
  @ApiProperty({
    description: 'Version ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  versionId: string;
}

export class DeleteVersionSuccessResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Success message',
    example: 'Version deleted successfully',
  })
  data: { message: string };
}
