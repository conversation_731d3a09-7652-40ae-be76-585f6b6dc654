import { Injectable } from '@nestjs/common';
import { QueuePayloadType } from './predefined-data';
import {
  SpotRequestGeneratorQueue,
  SpotRequestAcceptGeneratorQueue,
  CommonNotificationGeneratorQueue,
} from './worker/generator';
import { DailyReminderGeneratorQueue } from './worker/generator/reminder/daily-reminder';
import {
  ApprovePostQueue,
  ApproveReelsQueue,
  UserAccountDeleteQueue,
} from './worker/others';
import { SendEmailQueue } from './worker/others/scheduler/services/sendEmail';
import {
  SpotRequestSenderQueue,
  SpotRequestAcceptSenderQueue,
} from './worker/sender';
import { CommonNotificationSenderQueue } from './worker/sender/common';
import { DailyReminderSenderQueue } from './worker/sender/reminder/daily-reminder';

/**
 * Notification handler by payloadType
 */
@Injectable()
export class QueueConsumeHandler {
  static async handle(payload: any) {
    const { payloadType, task } = payload;

    console.log("🔄 Queue handler processing payload type:", payloadType, "task:", task);
    // console.log("payload",payload)

    const queues = [
      QueuePayloadType.SPOT_REQUEST_GENERATOR,
      QueuePayloadType.SPOT_REQUEST_SENDER,
      QueuePayloadType.SPOT_REQUEST_ACCEPT_GENERATOR,
      QueuePayloadType.SPOT_REQUEST_ACCEPT_SENDER,
      QueuePayloadType.POST_APPROVE,
      QueuePayloadType.REELS_APPROVE,
      QueuePayloadType.DELETE_ACCOUNT_HANDLER,
      QueuePayloadType.SEND_SCHEDULED_EMAIL,
      QueuePayloadType.DAILY_REMINDER_GENERATOR,
      QueuePayloadType.DAILY_REMINDER_SENDER,
      QueuePayloadType.REMINDER_TOPIC_SENDER,
    ];

    if (!queues.includes(payloadType)) {
      if (task == 'generator')
        CommonNotificationGeneratorQueue.generator(payload);
      else CommonNotificationSenderQueue.sender(payload);
    } else {
      switch (payloadType as QueuePayloadType) {
        case QueuePayloadType.SPOT_REQUEST_GENERATOR:
          SpotRequestGeneratorQueue.generator(payload);
          break;
        case QueuePayloadType.SPOT_REQUEST_SENDER:
          SpotRequestSenderQueue.sender(payload);
          break;
        case QueuePayloadType.SPOT_REQUEST_ACCEPT_GENERATOR:
          SpotRequestAcceptGeneratorQueue.generator(payload);
          break;
        case QueuePayloadType.SPOT_REQUEST_ACCEPT_SENDER:
          SpotRequestAcceptSenderQueue.sender(payload);
          break;
        case QueuePayloadType.POST_APPROVE:
          ApprovePostQueue.approvePost(payload);
          break;
        case QueuePayloadType.REELS_APPROVE:
          ApproveReelsQueue.approveReels(payload);
          break;
        case QueuePayloadType.DELETE_ACCOUNT_HANDLER:
          UserAccountDeleteQueue.triggerDeleteAccount(payload);
          break;
        case QueuePayloadType.SEND_SCHEDULED_EMAIL:
          SendEmailQueue.triggerSendEmail(payload);
          break;
        case QueuePayloadType.DAILY_REMINDER_GENERATOR:
          DailyReminderGeneratorQueue.generator(payload);
          break;
        case QueuePayloadType.DAILY_REMINDER_SENDER:
          DailyReminderSenderQueue.sender(payload);
          break;
        case QueuePayloadType.REMINDER_TOPIC_SENDER:
          DailyReminderSenderQueue.sender(payload);
          break;
        default:
          break;
      }
    }
  }
}
