import { Injectable } from "@nestjs/common";
import { CoachProgramEntityModel } from "../../common/db/coach-program.model";
import { CoachProgramEntity } from "../../common/entities/coach-program.entity";

@Injectable()
export class CoachProgramRepositoryForInternal {

  /**
   * When a payment is successful we will only increment the totalEarnings of the program.
   * We will not increase the totalSubscription of the program count, cause we already 
   * increment the subscription count when the user will first make the subscribe call.
   * @param programId the corresponding program id for which the payment is being make
   * @param amount the amount that is being paid
   * @returns whether the operation is successful or not
   */
  async onPaymentSuccessful(programId: string, amount: number): Promise<CoachProgramEntity | null> {
    return await CoachProgramEntityModel.updateOne(
      { id: programId },
      { $inc: { totalEarnings: amount } },
      { new: true },
    ).lean();
  }

  async onRefundSuccessful(programId: string, amount: number): Promise<CoachProgramEntity | null> {
    return await CoachProgramEntityModel.updateOne(
      { id: programId },
      { $inc: { totalEarnings: -amount } },
      { new: true },
    ).lean();
  }
}