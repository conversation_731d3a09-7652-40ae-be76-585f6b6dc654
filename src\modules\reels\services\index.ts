import { HttpStatus, Injectable } from '@nestjs/common';
import { redisCacheConfig } from 'config/cache';
import { sharedConfig } from 'config/shared';
import {
  CreateReelsCommentRequest, CreateReelsCommentResponse, CreateReelsRequest, CreateReelsResponse, FitBuddiesStatus, FitBuddiesType,
  GetReelsCommentsResponse,
  GetReelsInfoResponse,
  GetReelsListResponse,
  GetReelsListWithUserInfo,
  GetReelsMediaResponse,
  GetReelsReactionQuery,
  GetReelsTimelineListResponse,
  GetSingleReelsResponse,
  NotificationCategory,
  NotificationModule,
  NotificationType,
  ProfileVisibilityEnum,
  ReactionReelsQuery,
  ReelsErrorEnum,
  ReelsMedia,
  ReelsReactionResponse,
  ShareReelsRequest,
  ShareReelsSuccessResponse,
  UpdateReelsBody, UpdateReelsResponse,
} from 'models';
import { DeleteReelsErrorMessages, DeleteReelsSuccessMessages, DeleteReelsSuccessResponse } from 'models/reels/deleteReels';
import { RedisCacheHelper } from 'src/cache/helper';
import { getMyFeedReels } from 'src/cache/reels';
import { Reels, ReelsPrivacy, ReelsType } from 'src/entity/reels';
import { Helper } from 'src/helper/helper.interface';
import { APIException } from 'src/internal/exception/api.exception';
import { NotificationHelperService } from 'src/modules/notification/services/helper.notification.service';
import { QueueInstance } from 'src/queue-system';
import { QueueName, QueuePayloadType } from 'src/queue-system/predefined-data';
import { getImageVersionKey } from 'src/utils/response';
import { ReelsRepository } from '../repository';


@Injectable()
export class ReelsService {

    constructor(
        private readonly reelsRepository:ReelsRepository,
        private helper: Helper,
        private notificationHelperService: NotificationHelperService,
  
    ){}

    async createReels(
        userId: string,
        body: CreateReelsRequest,
      ): Promise<CreateReelsResponse> {
    
         
        // console.log(body)

        let {  videos } = body;
        if (!videos) {
          throw new APIException(
            ReelsErrorEnum.PROVIDE_NECESSARY_CONTENT,
            'PROVIDE_NECESSARY_CONTENT',
            HttpStatus.CONFLICT,
          );
        }
        const newReels = await this.reelsRepository.createReels({
          ...body,
          userId,
        });
        if (!newReels)
          throw new APIException(
          ReelsErrorEnum.ERROR_IN_CREATING_NEW_REELS,
            'ERROR_IN_CREATING_NEW_REELS',
            HttpStatus.CONFLICT,
          );
    
        // Send to the post-approval queue
        try {
          const payload = {
            payloadType: QueuePayloadType.REELS_APPROVE,
            ...newReels,
          };
    
          (await QueueInstance).sendPayload(
            QueueName.REELS_APPROVE_QUEUE,
            Buffer.from(JSON.stringify(payload)),
          );
        } catch (error: any) {
          console.log(error.message);


        }

     
        
    
        return this.helper.serviceResponse.successResponse({
          ...(await this.mappedGeneratePresignedUrl({
            ...newReels,
            userInfo: {
              name: null,
              image: {
                profile: null,
              },
            },
          })),
          sharedReels: null,
          productInfo: null,
          weight: 0,
          liked: false,
          reactionType: null,
          totalLikes: 0,
          totalComments: 0,
          totalShares: 0,
          comments: [],
        });


        // return this.helper.serviceResponse.successResponse(newReels);

        
    }


    async mappedGeneratePresignedUrl(
      reels: Reels & Partial<GetReelsListWithUserInfo>,
    ): Promise<(Reels & Partial<GetReelsListWithUserInfo>) | null> {
      try {

      
       
  
        reels.videos = reels.videos?.length
          ? await Promise.all(
              reels.videos.map(async (video: ReelsMedia) => {

                // video.key = video?.url;

              
                // video = getImageVersionKey<ReelsMedia>(video, video?.url, [
                //   'thumbnail',
                // ]);
                // video.url =
                //   await this.helper.azureBlobStorageService.generatePreSignedUrl(
                //     video.url,
                //     'original/reels',
                //   );
                // video.thumbnail =
                //   await this.helper.azureBlobStorageService.generatePreSignedUrl(
                //     video?.thumbnail,
                //     'thumbnail/videos/original/reels',
                //   );

                //   console.log(video)
                video.key = video?.url;

              
                video = getImageVersionKey<ReelsMedia>(video, video?.url, [
                  'thumbnail',
                ]);

           
                video.url =
                  await this.helper.azureBlobStorageService.generatePreSignedUrl(
                    video.url,
                    'original/reels',
                  );

                  

               
                video.thumbnail =
                  await this.helper.azureBlobStorageService.generatePreSignedUrl(
                    video?.thumbnail,
                    'thumbnail/videos/original/reels',
                  );

                
                return video;
              }),
            )
          : [];
  
        // post.userInfo.image.profile = post.userInfo?.image?.profile
        //   ? await this.helper.azureBlobStorageService.generatePreSignedUrl(
        //       post.userInfo.image?.profile,
        //       'user',
        //     )
        //   : null;
        return reels;
      } catch (error) {
        console.log(error.message);
        return null;
      }
    }

    async deleteReels(
        reelsId: string,
        userId: string,
      ): Promise<DeleteReelsSuccessResponse> {
        const reels = await this.reelsRepository.getReels({
          id: reelsId,
          userId,
        });
       
        if (!reels)
          throw new APIException(
            DeleteReelsErrorMessages.NO_REELS_EXIST,
            'NO_REELS_EXIST',
            HttpStatus.BAD_REQUEST,
          );


    
        const deleteReels = await this.reelsRepository.deleteReels(reelsId, userId);
        if (!deleteReels) {
          throw new APIException(
            DeleteReelsErrorMessages.CAN_NOT_DELETE_REELS,
            'CAN_NOT_DELETE_POST',
            HttpStatus.BAD_REQUEST,
          );
        }
    
        // delete from cache
        await RedisCacheHelper.deleteData(
          `${redisCacheConfig.reels_channel}/${reelsId}`,
        );
    
        return this.helper.serviceResponse.successResponse({
          message: DeleteReelsSuccessMessages.REELS_DELETED_SUCCESSFULLY,
        });
      }

      async updateReels(
        userId: string,
        data: UpdateReelsBody,
        reelsId: string,
      ): Promise<UpdateReelsResponse> {
        const updatedReels = await this.reelsRepository.updateReels(
          { id: reelsId, userId },
          data,
        );
    
        if (!updatedReels)
          throw new APIException(
            ReelsErrorEnum.REELS_CAN_NOT_BE_UPDATED,
            'POST_CAN_NOT_BE_UPDATED',
            HttpStatus.BAD_REQUEST,
          );


        // Add into next phase

        await this.updateRedisCache(updatedReels);
        return this.helper.serviceResponse.successResponse(
          await this.mappedGeneratePresignedUrl(updatedReels),
        );
        // return this.helper.serviceResponse.successResponse(
        //   updatedReels
        // );
      }


      async getReelsList(
        userId: string,
        refresh: any,
      ): Promise<GetReelsListResponse> {
        refresh = refresh == 'true' || refresh == true ? true : false;
        const postList = await getMyFeedReels(userId, refresh);
        return this.helper.serviceResponse.successResponse(
          postList
            ? await this.setNewsFeedReelsAndProductsPresignedUrl(postList)
            : [],
        );
      }


      async setNewsFeedReelsAndProductsPresignedUrl(postList: any[]): Promise<any> {
        try {
          return await Promise.all(
            (postList || []).map(async (post: any) => {
              // if (post.type === 'ADVERTISING_PRODUCT' && post?.productInfo) {
              //   post.productInfo =
              //     await this.mappedAdvertisingProductsPhotosPresignedUrl(
              //       post.productInfo,
              //     );
              // } else {
              post = await this.mappedGeneratePresignedUrl(post);
    
              post &&
                post.type === ReelsType.SHARE &&
                post?.sharedPost &&
                (post.sharedPost = await this.mappedGeneratePresignedUrl(
                  post.sharedPost,
                ));
              // }
              return post;
            }),
          );
        } catch (error) {
          console.log(error.message);
          return null;
        }
      }

   
      async getReels(myId: string, reelsId: string): Promise<GetSingleReelsResponse> {
        const reels = await this.reelsRepository.getReelsAggregate({
          id: reelsId,
        });
        if (!reels)
          throw new APIException(
            ReelsErrorEnum.REELS_NOT_FOUND,
            'REELS_NOT_FOUND',
            HttpStatus.BAD_REQUEST,
          );

        //    return this.helper.serviceResponse.successResponse(
        //   reels
        // );
    
        const [comments, likedReels, buddy] = await Promise.all([
          await this.reelsRepository.getReelsComments({ reelsId: reels.id }, 0, 2),
      
          await this.reelsRepository.checkReelsReaction({
            reelId: reels.id,
            userId: myId
           
          }),
          myId !== reels.userId &&
            (await this.reelsRepository.findFitBuddy({
              $or: [
                {
                  userId: myId,
                  fitBuddyId: reels.userId,
                },
                {
                  userId: reels.userId,
                  fitBuddyId: myId,
                },
              ],
            })),
        ]);
    
        if (
          myId !== reels.userId &&
          ((reels.privacy === ReelsPrivacy.FRIENDS &&
            (!buddy || buddy?.type === FitBuddiesType.FOLLOWER)) ||
            (buddy && buddy.status === FitBuddiesStatus.BLOCKED))
        )
          throw new APIException(
            ReelsErrorEnum.CAN_NOT_SEE_REELS,
            'CAN_NOT_SEE_REELS',
            HttpStatus.BAD_REQUEST,
          );
    
        (reels.comments as any) = comments;
        // (reels as any).liked = likedPost ? true : false;
        (reels as any).reactionType = likedReels ? likedReels.type : null;
        return this.helper.serviceResponse.successResponse(
          await this.mappedGeneratePresignedUrl(reels),
        );
      }


      async createReelsComment(
        userId: string,
        data: CreateReelsCommentRequest,
        ownerId: string,
        reelsId: string,
      ): Promise<CreateReelsCommentResponse> {
        const [reels, owner, buddy, ownInfo] = await Promise.all([
          await this.reelsRepository.getReels({
            id: reelsId,
          }),
          await this.reelsRepository.getUserInfo({
            id: ownerId,
          }),
          await this.reelsRepository.findFitBuddy({
            userId: ownerId,
            fitBuddyId: userId,
            status: FitBuddiesStatus.ACTIVE,
          }),
          await this.reelsRepository.getUserInfo({
            id: userId,
          }),
        ]);
    
        if (
          !reels ||
          !owner ||
          (ownerId !== userId && reels.privacy === ReelsPrivacy.PRIVATE) ||
          (ownerId !== userId &&
            buddy &&
            reels.privacy === ReelsPrivacy.FRIENDS &&
            buddy.type === FitBuddiesType.FOLLOWER)
        )
          throw new APIException(
            ReelsErrorEnum.CAN_NOT_DO_THAT,
            'CAN_NOT_DO_THAT',
            HttpStatus.BAD_REQUEST,
          );
    
        const comment = await this.reelsRepository.createReelsComment(
          userId,
          data,
          reelsId,
          owner.id,
          buddy,
        );
    
        // ownInfo.image.profile =
        //   ownInfo.image.profile &&
        //   (await this.helper.azureBlobStorageService.generatePreSignedUrl(
        //     ownInfo.image.profile,
        //     'user',
        //   ));
    
        comment.userInfo = {
          id: ownInfo.id,
          name: ownInfo.name,
          image: ownInfo.image,
        };

        const newReels=  await this.reelsRepository.getReels({
          id: reelsId,
        });

        
        const newReelsComment ={
            ...comment,
            totalComments: newReels?.totalComments,
        }
          
        // Notification Payload
        const payload = {
          recipient: owner,
          createdBy: ownInfo,
          title: 'Your reels got a comment!',
          content: `${ownInfo?.name} commented on your reels`,
          module: NotificationModule.REELS,
          type: NotificationType.REELS_COMMENT,
          category: NotificationCategory.REELS_COMMENT,
          documentId: reelsId,
        };
    
        await this.notificationHelperService.sendNotifications(payload);
    
        return this.helper.serviceResponse.successResponse(newReelsComment);
      }


      async getReelsComments(
        userId: string,
        reelsId: string,
        ownerId: string,
        offset?: number,
        limit?: number,
      ): Promise<GetReelsCommentsResponse> {

       

        const [reels, owner, buddy] = await Promise.all([
          await this.reelsRepository.getReels({
            id: reelsId,
          }),
          await this.reelsRepository.getUserInfo({
            id: ownerId,
          }),
          await this.reelsRepository.findFitBuddy({
            userId: ownerId,
            fitBuddyId: userId,
            status: FitBuddiesStatus.ACTIVE,
          }),
        ]);

        // console.log('reels',reels)
    
        // Whether the logged-in user leaves a comment on this post or not,
        // If the logged-in user does not leave a comment on it, then we should consider some conditions.
        // 1. post-privacy 2. buddy type
        if (
          !reels ||
          !owner ||
          (ownerId !== userId && reels.privacy === ReelsPrivacy.PRIVATE) ||
          (ownerId !== userId &&
            reels.privacy === ReelsPrivacy.FRIENDS &&
            buddy?.type === FitBuddiesType.FOLLOWER)
        )
          throw new APIException(
            ReelsErrorEnum.CAN_NOT_DO_THAT,
            'CAN_NOT_DO_THAT',
            HttpStatus.BAD_REQUEST,
          );
    
        const comments = await this.reelsRepository.getReelsComments(
          {
            reelsId,
          },
          offset,
          limit,
        );
        return this.helper.serviceResponse.successResponse(comments);
      }



      async shareReels(
        reelsId: string,
        ownerId: string,
        userId: string,
        body: ShareReelsRequest,
      ): Promise<ShareReelsSuccessResponse> {
        const [mainReels, owner, buddy] = await Promise.all([
          await this.reelsRepository.getReels({
            id: reelsId,
          }),
          await this.reelsRepository.getUserInfo({
            id: ownerId,
          }),
          await this.reelsRepository.findFitBuddy({
            userId: ownerId,
            fitBuddyId: userId,
            status: FitBuddiesStatus.ACTIVE,
          }),
        ]);

        // Whether the logged-in user shares this post or not,
        // If the logged-in user doesn't, then we should consider some conditions.
        // 1. post-privacy 2. buddy type
        if (
          !mainReels ||
          !owner ||
          (ownerId !== userId && mainReels.privacy === ReelsPrivacy.PRIVATE) ||
          (ownerId !== userId &&
            buddy &&
            mainReels.privacy === ReelsPrivacy.FRIENDS &&
            buddy.type === FitBuddiesType.FOLLOWER)
        )
          throw new APIException(
            ReelsErrorEnum.CAN_NOT_DO_THAT,
            'CAN_NOT_DO_THAT',
            HttpStatus.BAD_REQUEST,
          );
    
        const sharedReels = await this.reelsRepository.shareReels(
          mainReels.id,
          mainReels.userId,
          buddy?.type,
          {
            sharedReels: mainReels,
            userId,
            type: ReelsType.SHARE,
            ...body,
          },
        );

        // console.log('share reels',sharedReels)

        if (!sharedReels)
          throw new APIException(
            ReelsErrorEnum.ERROR_IN_SHARING_REELS,
            'ERROR_IN_SHARING_REELS',
            HttpStatus.CONFLICT,
          );
        


        try {
          const payload = {
            payloadType: QueuePayloadType.REELS_APPROVE,
            ...sharedReels,
          };


    
          (await QueueInstance).sendPayload(
            QueueName.REELS_APPROVE_QUEUE,
            Buffer.from(JSON.stringify(payload)),
          );
        } catch (error: any) {
          console.log(error.message);


        }
    
        return this.helper.serviceResponse.successResponse(sharedReels);
      }
    

      async addReelsReaction(
        userId: string,
        query: ReactionReelsQuery,
        reelsId: string,
      ): Promise<ReelsReactionResponse> {
        const { ownerId } = query;
    
    
        let setReactionValue= false;
        let setReactionType= ''
    
        if(query.isReacting){
          setReactionType = query.reactionType
          query.isReacting = String(query.isReacting) === 'true'
          setReactionValue= Boolean(query.isReacting)
        }
    
   
        const [reels, owner, doesLikedReels, buddy, loggedInUser] = await Promise.all(
          [
            await this.reelsRepository.getReels({
              id: reelsId,
            }),
            await this.reelsRepository.getUserInfo({
              id: ownerId,
            }),
            await this.reelsRepository.checkReelsReaction({
              userId,
              reelsId,
              
            }),
            await this.reelsRepository.findFitBuddy({
              userId: ownerId,
              fitBuddyId: userId,
              status: FitBuddiesStatus.ACTIVE,
            }),
            await this.reelsRepository.getUserInfo({
              id: userId,
            }),
          ],
        );

        // console.log(doesLikedReels)
    
        // Whether the logged-in user likes this post or not,
        // If the logged-in user doesn't, then we should think about some conditions.
        // 1. post-privacy 2. buddy type 3. already liked this post or not
        
        if (
          !reels ||
          !owner ||
          // (Boolean(query.liked) && doesLikedPost) ||
          // (!Boolean(query.liked) && !doesLikedPost) ||
         
          (ownerId !== userId && reels.privacy === ReelsPrivacy.PRIVATE) ||
          (ownerId !== userId &&
            buddy &&
            reels.privacy === ReelsPrivacy.FRIENDS &&
            buddy.type === FitBuddiesType.FOLLOWER)
        )
          throw new APIException(
            ReelsErrorEnum.CAN_NOT_DO_THAT,
            'CAN_NOT_DO_THAT',
            HttpStatus.BAD_REQUEST,
          );
    
          // console.log(query.liked)
    
          // if(doesLikedPost && query.liked === true){
          //   throw new APIException(
          //     PostErrorEnum.CAN_NOT_DO_THAT,
          //     'CAN_NOT_DO_THAT',
          //     HttpStatus.BAD_REQUEST,
          //   );
    
          // }
    
          // console.log(doesLikedPost)
    
    
          if (doesLikedReels && query.isReacting === true) {
            console.log('update the reaction')
            const updatedReaction = await this.reelsRepository.updateReelsReactionType(
              { userId, reelsId },  // Combine userId and postId into a single filter object
              setReactionType,     // The new reaction type to be updated
            );

            const newReels=  await this.reelsRepository.getReels({
              id: reelsId,
            });
            const newreaction ={
              ...updatedReaction,
            reactionType: updatedReaction?.type,
            totalReactions: newReels?.totalReactions,
            }
            return this.helper.serviceResponse.successResponse(newreaction);
          }
          
    
    
    
        const  addreactionReels = await this.reelsRepository.addreelsReaction(
          userId,
          reelsId,
          setReactionValue,
          setReactionType, 
          owner.id,
          buddy,
        );

        

        const {userId: reactionUserId , reelsId: reactionReelsId,type, createdAt, updatedAt}= addreactionReels


        const newReels=  await this.reelsRepository.getReels({
          id: reelsId,
        });

        
       const  reactionReels ={
          userId:reactionUserId,
          type,
          reelsId:reactionReelsId,
          createdAt,
          updatedAt,
          reactionType: type,
          totalReactions: newReels?.totalReactions,
    
        }
    
    
        if (Boolean(query.isReacting)) {
          // Notification Payload
          const payload = {
            recipient: owner,
            createdBy: loggedInUser,
            title: 'Your reels got reacted!',
            content: `${loggedInUser?.name} reacted your reels`,
            module: NotificationModule.REELS,
            type: NotificationType.REELS_REACTION,
            category: NotificationCategory.REELS_REACTION,
            documentId: reelsId,
          };
    
          await this.notificationHelperService.sendNotifications(payload);
        }
    
        return this.helper.serviceResponse.successResponse(reactionReels);
      }


      
  async getReelsReactionerInfo(
    reelsId: string,
    condition: GetReelsReactionQuery,
  ): Promise<GetReelsInfoResponse> {
    const post = await this.reelsRepository.getReels({
      id: reelsId,
    });

    if (!post) {
      throw new APIException(
        ReelsErrorEnum.REELS_NOT_FOUND,
        'REELS_NOT_FOUND',
        HttpStatus.BAD_REQUEST,
      );
    }

    const reactionerList = await this.reelsRepository.getReelsReactionerInfo(
      reelsId,
      condition?.offset,
      condition?.limit,
      condition?.reactionType,

      
    );
    return this.helper.serviceResponse.successResponse(reactionerList);
  }
    

  async updateRedisCache(reels: any): Promise<void> {
    try {
    
      const likedPost= await this.reelsRepository.checkReelsReaction({
        reelsId: reels.id,
        userId: reels.userId
       
      });
      const comments   =await this.reelsRepository.getReelsComments({ reelsId: reels.id }, 0, 2);
      const likersList = await this.reelsRepository.getReelsLikersInfo(
         reels.id ,
         0, 
         2
      );
      const likersListByType = await this.reelsRepository.getReelsMultiLikersInfo(
         reels.id ,
         0, 
         2
      );
     
     
      (reels.comments as any) = comments;
      // (reels as any).liked = likedPost ? true : false;
      (reels as any).reactionType = likedPost ? likedPost.type : null;
      reels.likersInfo = likersList;
      reels.totalReactionerInfo = likersList;
      reels.reactionTypeUserInfo = likersListByType;



      reels.userInfo = await this.reelsRepository.getUserInfo({
        id: reels.userId,
      });


      // update cache data(post)
      await RedisCacheHelper.setData<Reels>(
        `${redisCacheConfig.reels_channel}/${reels.id}`,
        reels,
      );
    } catch (error) {
      console.log(error.message);
    }
  }


  async getOwnReelstList(
    myId: string,
    offset?: number,
    limit?: number,
  ): Promise<GetReelsTimelineListResponse> {
    const reelsList = await this.reelsRepository.getOwnReelsList(
      myId,
      offset,
      limit,
    );
    return this.helper.serviceResponse.successResponse(
      reelsList
        ? await this.setLikeCommentsAndTimelinePostsPresignedUrl(reelsList, myId)
        : [],
    );
  }


  async getUserReelsList(
    myId: string,
    userId: string,
    offset?: number,
    limit?: number,
  ): Promise<GetReelsTimelineListResponse> {
    limit =
      !limit || Number(limit) > sharedConfig.defaultMaxLImit
        ? sharedConfig.defaultLimit
        : Number(limit);
    const [buddy, preference] = await Promise.all([
      await this.reelsRepository.findFitBuddy({
        userId: myId,
        fitBuddyId: userId,
      }),
      await this.reelsRepository.getUserPreference(userId),
    ]);

    if (!preference) {
      throw new APIException(
        ReelsErrorEnum.CAN_NOT_GET_USER_PREFERENCE,
        'CAN_NOT_GET_USER_PREFERENCE',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (buddy && buddy.status === FitBuddiesStatus.BLOCKED) {
      throw new APIException(
        ReelsErrorEnum.CAN_NOT_SEE_REELS,
        'CAN_NOT_SEE_REELS',
        HttpStatus.BAD_REQUEST,
      );
    }

    const buddiesIds = new Set<string>().add(buddy?.fitBuddyId || userId);
    const mergeObjArray = new Set<{ userId: string; privacy: string }>();

    if (
      preference?.privacy?.profileVisibility === ProfileVisibilityEnum.LOCKED &&
      buddy?.type !== FitBuddiesType.FIT_BUDDY
    ) {
      throw new APIException(
        ReelsErrorEnum.USER_PROFILE_IS_LOCKED,
        'USER_PROFILE_IS_LOCKED',
        HttpStatus.BAD_REQUEST,
      );
    }

    mergeObjArray.add({
      userId: buddy?.fitBuddyId || userId,
      privacy: ReelsPrivacy.PUBLIC,
    });
    if (buddy?.type === FitBuddiesType.FIT_BUDDY) {
      mergeObjArray.add({
        userId: buddy?.fitBuddyId || userId,
        privacy: ReelsPrivacy.FRIENDS,
      });
    }

    const postList = await this.reelsRepository.getUserReelsList(
      buddiesIds,
      mergeObjArray,
      offset,
      limit,
    );

    return this.helper.serviceResponse.successResponse(
      postList
        ? await this.setLikeCommentsAndTimelinePostsPresignedUrl(postList, myId)
        : [],
    );
  }


  async setLikeCommentsAndTimelinePostsPresignedUrl(
    reelsList: any[],
    userId: string,
  ): Promise<any> {
    try {
      return await Promise.all(
        (reelsList || []).map(async (reels: any) => {
          const comments = await this.reelsRepository.getReelsComments(
            { reelsId: reels.id },
            0,
            2,
          );
        
          const likedReels = await this.reelsRepository.checkReelsReaction({
            reelsId: reels.id,
            userId: userId,
            
          });

          
          (reels.comments as any) = comments;
          (reels as any).reactionType = likedReels ? likedReels.type : null;
          // (reels as any).liked = likedReels ? true : false;

          reels = await this.mappedGeneratePresignedUrl(reels);
          reels &&
            reels.type === ReelsType.SHARE &&
            reels?.sharedReels &&
            (reels.sharedReels = await this.mappedGeneratePresignedUrl(
              reels.sharedReels,
            ));
          return reels;
        }),
      );
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }


  async getReelsVideos(
    myId: string,
    userId: string,
    offset?: number,
    limit?: number,
  ): Promise<GetReelsMediaResponse> {
    const buddy =
      myId !== userId &&
      (await this.reelsRepository.findFitBuddy({
        userId: myId,
        fitBuddyId: userId,
      }));
    if (buddy && buddy.status === FitBuddiesStatus.BLOCKED)
      throw new APIException(
        ReelsErrorEnum.REELS_LIST_NOT_FOUND,
        'REELS_LIST_NOT_FOUND',
        HttpStatus.BAD_REQUEST,
      );

    const reels =
      myId !== userId
        ? await this.reelsRepository.getReelsMedia(userId, buddy)
        : await this.reelsRepository.getOwnReelsMedia(myId);

    let tmpVideosCount = 0;
    const videos = [];
    let loopBreak = false;

    // set the videos' pre-signed url.
    for (let index = 0, len = reels?.length; index < len; index++) {
      if (reels[index].videos?.length) {
        for (
          let videosIndex = 0, videosLength = reels[index].videos.length;
          videosIndex < videosLength;
          videosIndex++
        ) {
          if (
            reels[index].videos[videosIndex]?.url &&
            reels[index].videos[videosIndex]?.url !== ''
          ) {
            tmpVideosCount++;
            tmpVideosCount > (offset || 0) &&
              videos.push({
                reelsId: reels[index].id,
                url: reels[index].videos[videosIndex].url,
              });
          }

          // The number of videos has reached its limit.
          if (videos.length === (limit || Number.MAX_SAFE_INTEGER)) {
            loopBreak = true;
            break;
          }
        }
      }

      // console.log(videos)


      // The number of videos has reached its limit.
      if (loopBreak) break;
    }
    return this.helper.serviceResponse.successResponse(
      await Promise.all(
        videos.map(async (video: ReelsMedia) => {
          video = getImageVersionKey<ReelsMedia>(video, video?.url, [
            'thumbnail',
          ]);
          video.url =
            await this.helper.azureBlobStorageService.generatePreSignedUrl(
              video.url,
              'original/reels',
            );
          video.thumbnail =
            await this.helper.azureBlobStorageService.generatePreSignedUrl(
              video?.thumbnail,
              'thumbnail/videos/original/reels',
            );
          return video;
        }),
      ),
    );
  }





}
