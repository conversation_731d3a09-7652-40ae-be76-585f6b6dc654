import { Module } from '@nestjs/common';
import { SearchModule } from '../global-search/search.module';
import { PackageModule } from '../package/package.module';
import { ShippingModule } from '../shipping/shipping.module';
import { SpotProfileRepository } from '../spot/repositories/spotProfile';
import { UserInfoProvider } from './providers/user.provider';
import { UserRepository } from './repositories';
import { UserController } from './rest';
import { AdminUserController } from './rest/admin.user.controller';
import { UserService } from './services';

@Module({
  imports: [SearchModule, ShippingModule, PackageModule],
  controllers: [UserController, AdminUserController],
  providers: [UserService, UserRepository, SpotProfileRepository, UserInfoProvider],
  exports:[UserInfoProvider]
})
export class UserModule {}
