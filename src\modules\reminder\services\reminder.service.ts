import { Injectable } from '@nestjs/common';
import { ReminderRepository } from '../repositories/reminder.repository';
import { CreateReminderDto, UpdateReminderDto } from '../rest/dto/reminder.dto';
import { Reminder } from 'src/entity/reminder';
import { NotificationHelperService } from 'src/modules/notification/services/helper.notification.service';
import { NotificationStatsService } from '../../notification/services/notification-stats.service';
import { FcmService } from 'src/helper/fcmService';
import { QueueInstance } from 'src/queue-system';
import {
  NotificationQueueName,
} from 'src/queue-system/predefined-data';

@Injectable()
export class ReminderService {
  constructor(
    private readonly reminderRepository: ReminderRepository,
    private readonly notificationHelperService: NotificationHelperService,
    private readonly notificationStatsService: NotificationStatsService,
    private readonly fcmService: FcmService,
  ) {}

  async createReminder(
    adminId: string,
    createReminderDto: CreateReminderDto,
  ): Promise<Reminder> {
    const reminder = await this.reminderRepository.createReminder(adminId, createReminderDto);
    if (reminder.isActive) {
      await this.scheduleReminder(reminder);
    }
    return reminder;
  }

  async getAllReminders(): Promise<Reminder[]> {
    return this.reminderRepository.findAllReminders();
  }

  async getReminderById(id: string): Promise<Reminder> {
    return this.reminderRepository.findReminderById(id);
  }

  async updateReminder(
    id: string,
    updateReminderDto: UpdateReminderDto,
  ): Promise<Reminder> {
    const oldReminder = await this.reminderRepository.findReminderById(id);
    const reminder = await this.reminderRepository.updateReminder(id, updateReminderDto);
    console.log(`'${reminder.title}' is updated from ${oldReminder.time} to ${reminder.time}`);
    if (reminder.isActive) {
      await this.scheduleReminder(reminder);
    }
    return reminder;
  }

  async deleteReminder(id: string): Promise<boolean> {
    return this.reminderRepository.deleteReminder(id);
  }

  async sendReminderNotifications(): Promise<void> {
    try {
      // Get current time in HH:MM format
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now
        .getMinutes()
        .toString()
        .padStart(2, '0')}`;

      // Find all active reminders for the current time
      const activeReminders =
        await this.reminderRepository.findActiveRemindersForTime(currentTime);

      if (activeReminders.length === 0) {
        return;
      }

  

      // Start batch tracking
      const batchId = this.notificationStatsService.generateBatchId('legacy-reminder');
      this.notificationStatsService.startBatch(
        batchId,
        activeReminders.length,
        activeReminders.map(r => r.title).join(', '),
        'mixed'
      );

      // Group reminders by time proximity (within 5 minutes) for batching
      const timeWindowMs = 5 * 60 * 1000; // 5 minutes in milliseconds
      const groupedReminders: Reminder[][] = [];
      let currentGroup: Reminder[] = [];
      let lastTime: number | null = null;

      for (const reminder of activeReminders.sort((a, b) => {
        const timeA = this.parseTimeToMs(a.time);
        const timeB = this.parseTimeToMs(b.time);
        return timeA - timeB;
      })) {
        const reminderTimeMs = this.parseTimeToMs(reminder.time);
        if (lastTime === null || (reminderTimeMs - lastTime) <= timeWindowMs) {
          currentGroup.push(reminder);
        } else {
          groupedReminders.push(currentGroup);
          currentGroup = [reminder];
        }
        lastTime = reminderTimeMs;
      }
      if (currentGroup.length > 0) {
        groupedReminders.push(currentGroup);
      }

      // Send batched notifications for each group to a topic via queue
      const queueInstance = await QueueInstance;
      for (const group of groupedReminders) {
        try {
          let title = group[0].title;
          let body = group[0].message;
          const data = {
            reminderId: group[0].id,
            localTime: group[0].time,
            groupedIds: group.map(r => r.id).join(','),
          };

          if (group.length > 1) {
            title = "Multiple Reminders";
            body = group.map(r => `${r.title} at ${r.time}`).join('\n');
          }

          const payload = {
            title,
            body,
            documentId: group[0].id,
            data,
            topic: 'all-reminders',
            task: 'topic-sender',
            payloadType: 'REMINDER_TOPIC_SENDER',
            isHighPriority: true,
          };

          await queueInstance.sendPayload(
            NotificationQueueName.DAILY_REMINDER_SENDER_QUEUE,
            Buffer.from(JSON.stringify(payload)),
          );

          // Track successful send (note: count reflects number of reminders or groups broadcasted, not individual users, as topic sending doesn't track per-user delivery)
          group.forEach(() => this.notificationStatsService.incrementSent(batchId));
          // Log success message with reminder name(s) for developer clarity
          if (group.length === 1) {
            console.log(`Reminder sent successfully: ${group[0].title}`);
          } else {
            console.log(`Reminder sent successfully: Multiple Reminders (${group.map(r => r.title).join(', ')})`);
          }
        } catch (error) {
          // Track failed send for each reminder in the group
          group.forEach(() => this.notificationStatsService.incrementFailed(batchId));
          console.error(`Failed to enqueue reminder group to topic 'all-reminders':`, error.message);
        }
      }
    } catch (error) {
      console.error('Error sending reminder notifications:', error.message);
    }
  }

  // Helper method to parse HH:MM time string to milliseconds for comparison
  private parseTimeToMs(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return (hours * 60 * 60 * 1000) + (minutes * 60 * 1000);
  }



  /**
   * Schedule a single reminder to be sent at its specified time
   */
  async scheduleReminder(reminder: Reminder): Promise<void> {
    try {
      const now = new Date();
      const currentHours = now.getHours();
      const currentMinutes = now.getMinutes();
      const currentTimeMs = (currentHours * 60 * 60 * 1000) + (currentMinutes * 60 * 1000);
      const reminderTimeMs = this.parseTimeToMs(reminder.time);
      
      // Calculate delay until the reminder time today
      let delayMs = reminderTimeMs - currentTimeMs;
      if (delayMs <= 0) {
        // If the time has already passed for today, schedule for tomorrow
        delayMs += 24 * 60 * 60 * 1000; // Add 24 hours
      }
      
      // Create payload for the reminder
      const payload = {
        title: reminder.title,
        body: reminder.message,
        documentId: reminder.id,
        data: {
          reminderId: reminder.id,
          localTime: reminder.time,
        },
        topic: 'all-reminders',
        task: 'topic-sender',
        payloadType: 'REMINDER_TOPIC_SENDER',
        isHighPriority: true,
      };
      
      // Enqueue with delay (assuming QueueInstance supports delayed jobs)
      const queueInstance = await QueueInstance;
      await queueInstance.sendPayload(
        NotificationQueueName.DAILY_REMINDER_SENDER_QUEUE,
        Buffer.from(JSON.stringify(payload)),
        { delay: delayMs }
      );
      
      // Calculate the scheduled time for display in log
      // const currentTime = new Date();
      // const scheduledTime = new Date(currentTime.getTime() + delayMs);
      // const scheduledTimeStr = `${scheduledTime.getHours().toString().padStart(2, '0')}:${scheduledTime.getMinutes().toString().padStart(2, '0')}`;
      // console.log(`Scheduled reminder '${reminder.title}' for ${scheduledTimeStr}`);
    } catch (error) {
      console.error(`Failed to schedule reminder '${reminder.title}':`, error.message);
    }
  }

}
