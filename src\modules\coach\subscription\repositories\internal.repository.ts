import { Injectable } from "@nestjs/common";
import { CoachSubscriptionEntityModel } from "../../common/db/coach-program.model";
import { CoachSubscriptionPaymentEntity, CoachSubscriptionPaymentTerm } from "../../common/entities/coach-payment.entity";
import { CoachProgramDurationTerm, CoachSubscriptionEntity, SubscriptionStatus } from "../../common/entities/coach-program.entity";
import { TimeUtils } from "../utils/time.utils";

@Injectable()
export class SubscriptionRepositoryForInternal {

  /**
   * we will only select the isActive = true items cause these subscriptions are 
   * valid for being deactivated. If a subscription has already isActive = false,
   * then it is already deactivated - I mean it can't be expired right.
   * 
   * We also maintained the expiration date for each subscription. So, we can easily
   * check if the subscription is expired or not just by checking the current date
   * and expiration date.
   * 
   * @returns the number of subscriptions that were deactivated in this operation
   */
  async deactivateExpiredSubscriptions(): Promise<number> {
    const currentDate = new Date();

    const result = await CoachSubscriptionEntityModel.updateMany(
      { isActive: true, expirationDate: { $lt: currentDate } },
      { $set: { isActive: false } } // Deactivate them
    );

    return result.modifiedCount;
  }

  /**
   * We will select the isActive = true items cause only these subscriptions are valid
   * for being marked as payment due. If a subscription has already isActive = false,
   * then it is already deactivated - and why in actived subscriptions will be marked
   * as payment due, right?
   * 
   * Also, we will only select the isPaymentNeeded = false items cause only these
   * subscriptions are valid for being marked as payment due. If a subscription has
   * already isPaymentNeeded = true, then it is already marked as payment due.
   * 
   * We also maintained the next payment date for each subscription. So, we can easily
   * check if the payment term is expired or not just by checking the current date and
   * next payment date.
   * 
   * @returns the number of subscriptions that were marked as payment due in this operation
   */
  async markSubscriptionsAsPaymentDue(): Promise<number> {
    const currentDate = new Date();

    const result = await CoachSubscriptionEntityModel.updateMany(
      { isActive: true, isPaymentNeeded: false, nextPaymentDate: { $lt: currentDate } }, // Find subscriptions which need a new payment
      { $set: { isPaymentNeeded: true } } // Mark them as payment due
    );

    return result.modifiedCount;
  }

  /**
   * Since the payment is successful so we will make isPaymentNeeded = false, isFirstPaymentDone = true
   * and update the next payment date based on the payment term. We will also update the userTotalPaid
   * field in the subscription entity.
   * 
   * We can make isPaymentNeeded = false, user may pay his due payment, but either way the next very
   * day our cron job will update the isPaymentNeeded status based on the nextPaymentDate. So, marking
   * isPaymentNeeded = false is safe and required here.
   * 
   * We can make isFirstPaymentDone = true, you can think that this could be user's second payment, but
   * marking true doesn't change the concept right, I mean we are marking it true means user has already
   * made his first payment. So, marking true is safe and required here.
   * 
   * @param paymentEntity the payment details
   * @returns whether the operation was successful or not
   */
  async onPaymentSuccessful(paymentEntity: CoachSubscriptionPaymentEntity): Promise<CoachSubscriptionEntity | null> {
    let retValue: CoachSubscriptionEntity = null;
    const session = await CoachSubscriptionEntityModel.startSession();
    session.startTransaction();

    try {

      console.log(`Payment successful operation started for payment id: ${paymentEntity.id}`);

      const subscription = await CoachSubscriptionEntityModel.findOne({ id: paymentEntity.subscriptionId }).session(session);
      if (!subscription) {
        // subscription should be found here, if it is not then there is some serious issue
        // for now we will log the issue, but in future we should notify the issue tracker  
        console.log(`No subscription found for payment id: ${paymentEntity.subscriptionId}. This should be checked!`);
        throw new Error(`No subscription found for payment id: ${paymentEntity.subscriptionId}. This should be checked!`);
      }

      console.log(`Subscription found for payment id: ${paymentEntity.id}`);
      // console.log(`Subscription details: ${JSON.stringify(subscription)}`);

      const now = new Date();

      const diffInDays = TimeUtils.daysBetweenDates(subscription.subscriptionDate, now);

      // Adjust nextPaymentDate and expirationDate based on how many days delayed
      subscription.subscriptionDate = now;
      subscription.nextPaymentDate = TimeUtils.addDays(subscription.nextPaymentDate, diffInDays);
      subscription.expirationDate = TimeUtils.addDays(subscription.expirationDate, diffInDays);

      subscription.isPaymentNeeded = false;
      subscription.isFirstPaymentDone = true;
      subscription.userTotalPaid += paymentEntity.paymentAmount;
      subscription.isActive = true;
      subscription.status= SubscriptionStatus.SUCCESSFUL

      // console.log(`Updating subscription details: ${JSON.stringify(subscription)}`);

      switch (paymentEntity.paymentTerm) {
        case CoachSubscriptionPaymentTerm.OneTime:
          subscription.nextPaymentDate = subscription.expirationDate;
          break;
        case CoachSubscriptionPaymentTerm.Daily:
          subscription.nextPaymentDate = TimeUtils.calculateExpirationDate(subscription.nextPaymentDate, 1, CoachProgramDurationTerm.Days);
          break;
        case CoachSubscriptionPaymentTerm.Weekly:
          subscription.nextPaymentDate = TimeUtils.calculateExpirationDate(subscription.nextPaymentDate, 1, CoachProgramDurationTerm.Weeks);
          break;
        case CoachSubscriptionPaymentTerm.Monthly:
          subscription.nextPaymentDate = TimeUtils.calculateExpirationDate(subscription.nextPaymentDate, 1, CoachProgramDurationTerm.Months);
          break;
        default:
          throw new Error("Invalid payment term found while executing payment successful operation! this should be checked.");
      }

      // console.log(`Updated subscription details: ${JSON.stringify(subscription)}`);

      // Save changes
      await subscription.save({ session });
      await session.commitTransaction();
      retValue = await CoachSubscriptionEntityModel.findOne({ id: paymentEntity.subscriptionId }).session(session).lean().exec();
      console.log(retValue);
    } catch (error) {
      console.log(`Error occurred while executing payment successful operation for payment id: ${paymentEntity.id}`);
      console.log(`Error details: ${error}`);
      retValue = null;
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
    console.log(retValue);
    return retValue;
  }

}