import { forwardRef, Module } from "@nestjs/common";
import { AccountingModule } from "src/modules/accounting/accounting.rest.module";
import { BkashRestModule } from "src/modules/bkash/bkash.rest.module";
import { CoachIncomeProfileModule } from "../income-profile/coach-income-profile.module";
import { NotificationModule } from "../notification/notification.rest.module";
import { CoachPaymentModule } from "../payment/coach-payment.module";
import { CoachProgramModule } from "../program/coach-program.module";
import { CoachSubscriptionModule } from "../subscription/coach-subscription.module";
import { UserInfoModule } from "../user-info/user-info.module";
import { RefundControllerForAdmin } from "./controllers/admin.controller";
import { RefundControllerForUser } from "./controllers/user.controller";
import { CoachRefundProviderForInternal } from "./providers/internal.provider";
import { RefundRepositoryForAdmin } from "./repositories/admin.repository";
import { RefundRepositoryForUser } from "./repositories/user.repository";
import { RefundServiceForAdmin } from "./services/admin.service";
import { RefundServiceForUser } from "./services/user.service";

@Module({
  controllers: [
    RefundControllerForAdmin,
    RefundControllerForUser,
  ],
  providers: [
    RefundServiceForAdmin,
    RefundRepositoryForAdmin,
    RefundServiceForUser,
    RefundRepositoryForUser,
    CoachRefundProviderForInternal,
  ],
  imports: [
    forwardRef(() => CoachSubscriptionModule),
    forwardRef(() => UserInfoModule),
    forwardRef(()=> NotificationModule),
    forwardRef(() => CoachPaymentModule),
    forwardRef(() => BkashRestModule),
    forwardRef(() => AccountingModule),
    forwardRef(() => CoachIncomeProfileModule),
    forwardRef(() => CoachProgramModule),
  ],
  exports: [
    CoachRefundProviderForInternal
  ]
})
export class CoachRefundModule { }