import { HttpStatus, Injectable } from '@nestjs/common';
import { sharedConfig } from 'config/shared';
import {
  ChatErrorEnum,
  ICreateGroupChat,
  ICreateGroupChatReq,
  IDeleteMessageRes,
  IGroupChatRes,
  IGroupConversationListRes,
  IGroupCreateChatRes
} from 'models';
import { Helper } from 'src/helper/helper.interface';
import { APIException } from 'src/internal/exception/api.exception';
import { successResponse } from 'src/utils/response';
import { IServiceSuccessResponse } from 'src/utils/response/service.response.interface';
import { SocketHelperService } from '../realtime/socket.helper';
import { GroupChatRepository } from '../repositories';
import { ChatHelperService } from './chat.helper.service';

@Injectable()
export class GroupChatService {
  constructor(
    private readonly groupChatRepository: GroupChatRepository,
    private readonly chatHelperService: ChatHelperService,
    private readonly helperSerice: Helper,
    private readonly wsHelperService: SocketHelperService,
  ) {}

  async create(
    senderId: string,
    groupId: string,
    body: ICreateGroupChatReq,
  ): Promise<IGroupCreateChatRes> {
    const isAllowedToSend = await this.chatHelperService.isUserExistInChatGroup(
      senderId,
      groupId,
    );

    if (!isAllowedToSend) {
      return this.chatHelperService.response(
        false,
        'You are not allowed to send meassage',
      );
    }

    const payload: ICreateGroupChat = {
      senderId,
      groupId,
      ...body,
    };
    const newChat = await this.groupChatRepository.create(payload);
    if (!newChat) {
      return this.chatHelperService.response(
        false,
        'Something went wrong in create',
      );
    }

    const addToConversation =
      await this.chatHelperService.addToConversationHistory(
        payload.id,
        senderId,
        groupId,
        true,
      );
    if (!addToConversation) {
      return this.chatHelperService.response(
        false,
        'Something went wrong in manage conversation',
      );
    }
    //for debugging
    //this.wsHelperService.handleNotification(ChatType.GROUP_CHAT,senderId,null,body,groupId);
    return this.chatHelperService.response(true, 'Send successfully');
  }

  async getMessageHistoryByUser(
    senderId: string,
    groupId: string,
    offset = sharedConfig.oneToOneChatDefaultSkip,
    limit = sharedConfig.oneToOneChatDefaultLimit,
  ): Promise<IServiceSuccessResponse<IGroupChatRes[]>> {
    const isAllowedToFetch =
      await this.chatHelperService.isUserExistInChatGroup(senderId, groupId);
    if (!isAllowedToFetch) {
      throw new APIException(
        ChatErrorEnum.INVALID_REQUEST,
        'INVALID_REQUEST',
        HttpStatus.BAD_REQUEST,
      );
    }

    const chatHistory = await this.groupChatRepository.getGroupMessageHistory(
      groupId,
      offset,
      limit,
    );
    if (!chatHistory) {
      return successResponse(null, []);
    }
    const mapped = await Promise.all(
      chatHistory.map(async (e) => {
        if (e.type === 'image') {
          e.content =
            await this.helperSerice.azureBlobStorageService.generatePreSignedUrl(
              e.content,
              'original/chat',
            );
        }
        return e;
      }),
    );
    return successResponse(null, mapped);
  }

  async deleteMessage(
    senderId: string,
    messageId: string,
  ): Promise<IServiceSuccessResponse<IDeleteMessageRes>> {
    const query = {
      senderId,
      id: messageId,
    };
    const deletedMessage = await this.groupChatRepository.deleteSingleMessage(
      query,
    );

    if (!deletedMessage) {
      throw new APIException(
        ChatErrorEnum.MESSAGE_CAN_NOT_BE_DELETED,
        'MESSAGE_CAN_NOT_BE_DELETED',
        HttpStatus.BAD_REQUEST,
      );
    }
    return successResponse(null, {
      message: 'Message has been deleted successfully',
    });
  }

  async conversationList(
    userId: string,
    offset = sharedConfig.oneToOneChatDefaultSkip,
    limit = sharedConfig.oneToOneChatDefaultLimit,
  ): Promise<IServiceSuccessResponse<IGroupConversationListRes[]>> {
    if (limit < 1 || limit > sharedConfig.oneToOneChatMaxLimit) {
      limit = sharedConfig.oneToOneChatDefaultLimit;
    }
    let list = await this.groupChatRepository.getConversationList(
      userId,
      offset,
      limit,
    );
    if (!list) {
      return successResponse(null, []);
    }
    return successResponse(null, list);
  }

  async getUserFcmToken(userId: string): Promise<string> {
    return await this.groupChatRepository.getUserFcmToken(userId);
  }
}
