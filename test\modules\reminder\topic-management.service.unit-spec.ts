import { Test, TestingModule } from '@nestjs/testing';
import { TopicManagementService } from 'src/modules/reminder/services/topic-management.service';
import { NotificationHelperService } from 'src/modules/notification/services/helper.notification.service';
import { FcmService } from 'src/helper/fcmService';

describe('TopicManagementService', () => {
  let service: TopicManagementService;
  let notificationHelperService: jest.Mocked<NotificationHelperService>;
  let fcmService: jest.Mocked<FcmService>;

  const mockUsers = [
    { id: 'user1', fcmToken: 'token1', email: '<EMAIL>', name: 'User 1', dateOfBirth: new Date(), maxSquat: 100, weightType: 'kg', maxBench: 80, gender: 'male', timezone: 'UTC', isOnline: false, isDeleted: false, newsFeedStats: { userId: 'user1', buddyListOffset: 0, postDateOffset: 0, followEePostListOffset: 0, followErPostListOffset: 0, followEePostListOffsetDate: new Date(), followErPostListOffsetDate: new Date() }, totalBuddies: 0, totalFollowers: 0, _id: 'user1' },
    { id: 'user2', fcmToken: 'token2', email: '<EMAIL>', name: 'User 2', dateOfBirth: new Date(), maxSquat: 120, weightType: 'kg', maxBench: 90, gender: 'female', timezone: 'UTC', isOnline: false, isDeleted: false, newsFeedStats: { userId: 'user2', buddyListOffset: 0, postDateOffset: 0, followEePostListOffset: 0, followErPostListOffset: 0, followEePostListOffsetDate: new Date(), followErPostListOffsetDate: new Date() }, totalBuddies: 0, totalFollowers: 0, _id: 'user2' },
    { id: 'user3', fcmToken: '', email: '<EMAIL>', name: 'User 3', dateOfBirth: new Date(), maxSquat: 110, weightType: 'lb', maxBench: 85, gender: 'male', timezone: 'UTC', isOnline: false, isDeleted: false, newsFeedStats: { userId: 'user3', buddyListOffset: 0, postDateOffset: 0, followEePostListOffset: 0, followErPostListOffset: 0, followEePostListOffsetDate: new Date(), followErPostListOffsetDate: new Date() }, totalBuddies: 0, totalFollowers: 0, _id: 'user3' }, // No FCM token
    { id: 'user4', fcmToken: 'token4', email: '<EMAIL>', name: 'User 4', dateOfBirth: new Date(), maxSquat: 130, weightType: 'kg', maxBench: 95, gender: 'female', timezone: 'UTC', isOnline: false, isDeleted: false, newsFeedStats: { userId: 'user4', buddyListOffset: 0, postDateOffset: 0, followEePostListOffset: 0, followErPostListOffset: 0, followEePostListOffsetDate: new Date(), followErPostListOffsetDate: new Date() }, totalBuddies: 0, totalFollowers: 0, _id: 'user4' },
  ];

  beforeEach(async () => {
    const mockNotificationHelperService = {
      getAllActiveUsers: jest.fn(),
    };

    const mockFcmService = {
      subscribeNotificationTopic: jest.fn(),
      unsubscribeNotificationTopic: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TopicManagementService,
        {
          provide: NotificationHelperService,
          useValue: mockNotificationHelperService,
        },
        {
          provide: FcmService,
          useValue: mockFcmService,
        },
      ],
    }).compile();

    service = module.get<TopicManagementService>(TopicManagementService);
    notificationHelperService = module.get(NotificationHelperService);
    fcmService = module.get(FcmService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('subscribeToReminders', () => {
    it('should successfully subscribe a valid FCM token to reminders topic', async () => {
      fcmService.subscribeNotificationTopic.mockResolvedValue(undefined);

      const result = await service.subscribeToReminders('valid-token');

      expect(result).toBe(true);
      expect(fcmService.subscribeNotificationTopic).toHaveBeenCalledWith(
        'valid-token',
        'all-reminders'
      );
    });

    it('should return false for empty FCM token', async () => {
      const result = await service.subscribeToReminders('');

      expect(result).toBe(false);
      expect(fcmService.subscribeNotificationTopic).not.toHaveBeenCalled();
    });

    it('should handle FCM service errors gracefully', async () => {
      fcmService.subscribeNotificationTopic.mockRejectedValue(
        new Error('FCM service error')
      );

      const result = await service.subscribeToReminders('valid-token');

      expect(result).toBe(false);
      expect(fcmService.subscribeNotificationTopic).toHaveBeenCalledWith(
        'valid-token',
        'all-reminders'
      );
    });
  });

  describe('unsubscribeFromReminders', () => {
    it('should successfully unsubscribe a valid FCM token from reminders topic', async () => {
      fcmService.unsubscribeNotificationTopic.mockResolvedValue(true);

      const result = await service.unsubscribeFromReminders('valid-token');

      expect(result).toBe(true);
      expect(fcmService.unsubscribeNotificationTopic).toHaveBeenCalledWith(
        'valid-token',
        'all-reminders'
      );
    });

    it('should return false for empty FCM token', async () => {
      const result = await service.unsubscribeFromReminders('');

      expect(result).toBe(false);
      expect(fcmService.unsubscribeNotificationTopic).not.toHaveBeenCalled();
    });

    it('should handle FCM service errors gracefully', async () => {
      fcmService.unsubscribeNotificationTopic.mockRejectedValue(
        new Error('FCM service error')
      );

      const result = await service.unsubscribeFromReminders('valid-token');

      expect(result).toBe(false);
      expect(fcmService.unsubscribeNotificationTopic).toHaveBeenCalledWith(
        'valid-token',
        'all-reminders'
      );
    });
  });

  describe('bulkSubscribeActiveUsers', () => {
    it('should successfully subscribe all users with FCM tokens', async () => {
      notificationHelperService.getAllActiveUsers.mockResolvedValue(mockUsers);
      fcmService.subscribeNotificationTopic.mockResolvedValue(undefined);

      const result = await service.bulkSubscribeActiveUsers();

      expect(result.success).toBe(true);
      expect(result.subscribedCount).toBe(3); // Only users with valid FCM tokens
      expect(result.totalUsers).toBe(4);
      expect(fcmService.subscribeNotificationTopic).toHaveBeenCalledTimes(1); // Called once with batch
    });

    it('should handle partial subscription failures', async () => {
      notificationHelperService.getAllActiveUsers.mockResolvedValue(mockUsers);
      fcmService.subscribeNotificationTopic
        .mockResolvedValueOnce(true)  // user1 success
        .mockRejectedValueOnce(new Error('FCM error'))  // user2 fails
        .mockResolvedValueOnce(true); // user4 success

      const result = await service.bulkSubscribeActiveUsers();

      expect(result.success).toBe(true);
      expect(result.subscribedCount).toBe(2); // Only successful subscriptions
      expect(result.totalUsers).toBe(4);
      expect(fcmService.subscribeNotificationTopic).toHaveBeenCalledTimes(3);
    });

    it('should handle database errors gracefully', async () => {
      notificationHelperService.getAllActiveUsers.mockRejectedValue(
        new Error('Database error')
      );

      const result = await service.bulkSubscribeActiveUsers();

      expect(result.success).toBe(false);
      expect(result.subscribedCount).toBe(0);
      expect(result.totalUsers).toBe(0);
      expect(fcmService.subscribeNotificationTopic).not.toHaveBeenCalled();
    });
  });

  describe('subscribeUserToReminders', () => {
    it('should successfully subscribe a user with valid FCM token', async () => {
      fcmService.subscribeNotificationTopic.mockResolvedValue(true);

      const result = await service.subscribeUserToReminders('valid-token');

      expect(result).toBe(true);
      expect(fcmService.subscribeNotificationTopic).toHaveBeenCalledWith(
        'valid-token',
        'all-reminders'
      );
    });

    it('should return false for empty FCM token', async () => {
      const result = await service.subscribeUserToReminders('');

      expect(result).toBe(false);
      expect(fcmService.subscribeNotificationTopic).not.toHaveBeenCalled();
    });
  });

  describe('unsubscribeUserFromReminders', () => {
    it('should successfully unsubscribe a user with valid FCM token', async () => {
      fcmService.unsubscribeNotificationTopic.mockResolvedValue(true);

      const result = await service.unsubscribeUserFromReminders('valid-token');

      expect(result).toBe(true);
      expect(fcmService.unsubscribeNotificationTopic).toHaveBeenCalledWith(
        'valid-token',
        'all-reminders'
      );
    });

    it('should return false for empty FCM token', async () => {
      const result = await service.unsubscribeUserFromReminders('');

      expect(result).toBe(false);
      expect(fcmService.unsubscribeNotificationTopic).not.toHaveBeenCalled();
    });
  });

  describe('getSubscriptionStats', () => {
    it('should return correct subscription statistics', async () => {
      notificationHelperService.getAllActiveUsers.mockResolvedValue(mockUsers);

      const result = await service.getSubscriptionStats();

      expect(result.totalActiveUsers).toBe(4);
      expect(result.usersWithTokens).toBe(3); // Users with non-empty FCM tokens
    });

    it('should handle database errors gracefully', async () => {
      notificationHelperService.getAllActiveUsers.mockRejectedValue(
        new Error('Database error')
      );

      const result = await service.getSubscriptionStats();

      expect(result.totalActiveUsers).toBe(0);
      expect(result.usersWithTokens).toBe(0);
    });
  });

  describe('healthCheck', () => {
    it('should return healthy status when service is working', async () => {
      notificationHelperService.getAllActiveUsers.mockResolvedValue(mockUsers);

      const result = await service.healthCheck();

      expect(result.status).toBe('healthy');
      expect(result.message).toContain('3 users with FCM tokens out of 4 active users');
    });

    it('should return unhealthy status when service fails', async () => {
      notificationHelperService.getAllActiveUsers.mockRejectedValue(
        new Error('Service error')
      );

      const result = await service.healthCheck();

      expect(result.status).toBe('unhealthy');
      expect(result.message).toContain('Health check failed: Service error');
    });
  });
});
