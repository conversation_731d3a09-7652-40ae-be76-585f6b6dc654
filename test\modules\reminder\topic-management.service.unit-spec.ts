import { Test, TestingModule } from '@nestjs/testing';
import { TopicManagementService } from 'src/modules/reminder/services/topic-management.service';
import { NotificationHelperService } from 'src/modules/notification/services/helper.notification.service';
import { FcmServiceHelper } from 'src/helper/fcmService';

describe('TopicManagementService', () => {
  let service: TopicManagementService;
  let notificationHelperService: jest.Mocked<NotificationHelperService>;
  let fcmServiceHelper: jest.Mocked<FcmServiceHelper>;

  const mockUsers = [
    { id: 'user1', fcmToken: 'token1', email: '<EMAIL>' },
    { id: 'user2', fcmToken: 'token2', email: '<EMAIL>' },
    { id: 'user3', fcmToken: '', email: '<EMAIL>' }, // No FCM token
    { id: 'user4', fcmToken: 'token4', email: '<EMAIL>' },
  ];

  beforeEach(async () => {
    const mockNotificationHelperService = {
      getAllActiveUsers: jest.fn(),
    };

    const mockFcmServiceHelper = {
      subscribeNotificationTopic: jest.fn(),
      unsubscribeNotificationTopic: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TopicManagementService,
        {
          provide: NotificationHelperService,
          useValue: mockNotificationHelperService,
        },
        {
          provide: FcmServiceHelper,
          useValue: mockFcmServiceHelper,
        },
      ],
    }).compile();

    service = module.get<TopicManagementService>(TopicManagementService);
    notificationHelperService = module.get(NotificationHelperService);
    fcmServiceHelper = module.get(FcmServiceHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('subscribeToReminders', () => {
    it('should successfully subscribe a valid FCM token to reminders topic', async () => {
      fcmServiceHelper.subscribeNotificationTopic.mockResolvedValue(true);

      const result = await service.subscribeToReminders('valid-token');

      expect(result).toBe(true);
      expect(fcmServiceHelper.subscribeNotificationTopic).toHaveBeenCalledWith(
        'valid-token',
        'all-reminders'
      );
    });

    it('should return false for empty FCM token', async () => {
      const result = await service.subscribeToReminders('');

      expect(result).toBe(false);
      expect(fcmServiceHelper.subscribeNotificationTopic).not.toHaveBeenCalled();
    });

    it('should handle FCM service errors gracefully', async () => {
      fcmServiceHelper.subscribeNotificationTopic.mockRejectedValue(
        new Error('FCM service error')
      );

      const result = await service.subscribeToReminders('valid-token');

      expect(result).toBe(false);
      expect(fcmServiceHelper.subscribeNotificationTopic).toHaveBeenCalledWith(
        'valid-token',
        'all-reminders'
      );
    });
  });

  describe('unsubscribeFromReminders', () => {
    it('should successfully unsubscribe a valid FCM token from reminders topic', async () => {
      fcmServiceHelper.unsubscribeNotificationTopic.mockResolvedValue(true);

      const result = await service.unsubscribeFromReminders('valid-token');

      expect(result).toBe(true);
      expect(fcmServiceHelper.unsubscribeNotificationTopic).toHaveBeenCalledWith(
        'valid-token',
        'all-reminders'
      );
    });

    it('should return false for empty FCM token', async () => {
      const result = await service.unsubscribeFromReminders('');

      expect(result).toBe(false);
      expect(fcmServiceHelper.unsubscribeNotificationTopic).not.toHaveBeenCalled();
    });

    it('should handle FCM service errors gracefully', async () => {
      fcmServiceHelper.unsubscribeNotificationTopic.mockRejectedValue(
        new Error('FCM service error')
      );

      const result = await service.unsubscribeFromReminders('valid-token');

      expect(result).toBe(false);
      expect(fcmServiceHelper.unsubscribeNotificationTopic).toHaveBeenCalledWith(
        'valid-token',
        'all-reminders'
      );
    });
  });

  describe('bulkSubscribeAllUsersToReminders', () => {
    it('should successfully subscribe all users with FCM tokens', async () => {
      notificationHelperService.getAllActiveUsers.mockResolvedValue(mockUsers);
      fcmServiceHelper.subscribeNotificationTopic.mockResolvedValue(true);

      const result = await service.bulkSubscribeAllUsersToReminders();

      expect(result.success).toBe(true);
      expect(result.subscribedCount).toBe(3); // Only users with valid FCM tokens
      expect(result.totalUsers).toBe(4);
      expect(fcmServiceHelper.subscribeNotificationTopic).toHaveBeenCalledTimes(3);
    });

    it('should handle partial subscription failures', async () => {
      notificationHelperService.getAllActiveUsers.mockResolvedValue(mockUsers);
      fcmServiceHelper.subscribeNotificationTopic
        .mockResolvedValueOnce(true)  // user1 success
        .mockRejectedValueOnce(new Error('FCM error'))  // user2 fails
        .mockResolvedValueOnce(true); // user4 success

      const result = await service.bulkSubscribeAllUsersToReminders();

      expect(result.success).toBe(true);
      expect(result.subscribedCount).toBe(2); // Only successful subscriptions
      expect(result.totalUsers).toBe(4);
      expect(fcmServiceHelper.subscribeNotificationTopic).toHaveBeenCalledTimes(3);
    });

    it('should handle database errors gracefully', async () => {
      notificationHelperService.getAllActiveUsers.mockRejectedValue(
        new Error('Database error')
      );

      const result = await service.bulkSubscribeAllUsersToReminders();

      expect(result.success).toBe(false);
      expect(result.subscribedCount).toBe(0);
      expect(result.totalUsers).toBe(0);
      expect(fcmServiceHelper.subscribeNotificationTopic).not.toHaveBeenCalled();
    });
  });

  describe('subscribeUserToReminders', () => {
    it('should successfully subscribe a user with valid FCM token', async () => {
      fcmServiceHelper.subscribeNotificationTopic.mockResolvedValue(true);

      const result = await service.subscribeUserToReminders('valid-token');

      expect(result).toBe(true);
      expect(fcmServiceHelper.subscribeNotificationTopic).toHaveBeenCalledWith(
        'valid-token',
        'all-reminders'
      );
    });

    it('should return false for empty FCM token', async () => {
      const result = await service.subscribeUserToReminders('');

      expect(result).toBe(false);
      expect(fcmServiceHelper.subscribeNotificationTopic).not.toHaveBeenCalled();
    });
  });

  describe('unsubscribeUserFromReminders', () => {
    it('should successfully unsubscribe a user with valid FCM token', async () => {
      fcmServiceHelper.unsubscribeNotificationTopic.mockResolvedValue(true);

      const result = await service.unsubscribeUserFromReminders('valid-token');

      expect(result).toBe(true);
      expect(fcmServiceHelper.unsubscribeNotificationTopic).toHaveBeenCalledWith(
        'valid-token',
        'all-reminders'
      );
    });

    it('should return false for empty FCM token', async () => {
      const result = await service.unsubscribeUserFromReminders('');

      expect(result).toBe(false);
      expect(fcmServiceHelper.unsubscribeNotificationTopic).not.toHaveBeenCalled();
    });
  });

  describe('getSubscriptionStats', () => {
    it('should return correct subscription statistics', async () => {
      notificationHelperService.getAllActiveUsers.mockResolvedValue(mockUsers);

      const result = await service.getSubscriptionStats();

      expect(result.totalActiveUsers).toBe(4);
      expect(result.usersWithTokens).toBe(3); // Users with non-empty FCM tokens
    });

    it('should handle database errors gracefully', async () => {
      notificationHelperService.getAllActiveUsers.mockRejectedValue(
        new Error('Database error')
      );

      const result = await service.getSubscriptionStats();

      expect(result.totalActiveUsers).toBe(0);
      expect(result.usersWithTokens).toBe(0);
    });
  });

  describe('healthCheck', () => {
    it('should return healthy status when service is working', async () => {
      notificationHelperService.getAllActiveUsers.mockResolvedValue(mockUsers);

      const result = await service.healthCheck();

      expect(result.status).toBe('healthy');
      expect(result.message).toContain('3 users with FCM tokens out of 4 active users');
    });

    it('should return unhealthy status when service fails', async () => {
      notificationHelperService.getAllActiveUsers.mockRejectedValue(
        new Error('Service error')
      );

      const result = await service.healthCheck();

      expect(result.status).toBe('unhealthy');
      expect(result.message).toContain('Health check failed: Service error');
    });
  });
});
