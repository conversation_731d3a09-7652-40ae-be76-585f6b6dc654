import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { Helper } from 'src/helper/helper.interface';
import { deepCasting } from 'src/internal/casting/object.casting';
import {
  throwBadReqErrIf,
  throwNotFoundErr,
} from 'src/internal/exception/api.exception.ext';
import { CategoryProviderForUser } from '../../category/providers/user.provider';
import {
  COACH_PROFILE_NOT_FOUND,
  INCOMPLETE_QUERY_PARAMS,
} from '../../common/const/coach.const';
import { DEFAULT_PAGINATION_LIMIT } from '../../common/const/coach.pagination.const';
import { CoachProfileEntity } from '../../common/entities/coach-profile.entity';
import { PendingProfileProviderForUser } from '../../pending-profile/providers/user.provider';
import { ProgramProviderForUser } from '../../program/providers/user.provider';
import { SubCategoryProviderForUser } from '../../sub-category/providers/user.provider';
import { SubscriptionProviderForUser } from '../../subscription/providers/user.provider';
import { UserInfoProviderForUser } from '../../user-info/providers/user.provider';
import {
  ActiveCoachProfileDetailsResponseDtoForUser,
  ProfileStatusResponseDtoForUser,
  ProfileStatusSuccessResponseDtoForUser,
} from '../dtos/user-get-profile-status.dto';
import {
  GetProfileResponseDtoForUser,
  GetProfileSuccessResponseDtoForUser,
} from '../dtos/user-get-verified-profile.dto';
import {
  GetCoachProfilesFilterTypeForUser,
  GetCoachProfilesQueryDtoForUser,
  GetCoachProfilesResponseDtoForUser,
  GetCoachProfilesSuccessResponseDtoForUser,
} from '../dtos/user-get-verified-profiles.dto';
import { VerifiedProfileRepositoryForUser } from '../repositories/user.repository';

@Injectable()
export class VerifiedProfileServiceForUser {
  constructor(
    private readonly coachProfileRepository: VerifiedProfileRepositoryForUser,
    private readonly helper: Helper,
    @Inject(forwardRef(() => UserInfoProviderForUser))
    private readonly userInfoProvider: UserInfoProviderForUser,
    @Inject(forwardRef(() => PendingProfileProviderForUser))
    private readonly pendingProfileProvider: PendingProfileProviderForUser,
    @Inject(forwardRef(() => SubscriptionProviderForUser))
    private readonly subscriptionProvider: SubscriptionProviderForUser,
    @Inject(forwardRef(() => CategoryProviderForUser))
    private readonly categoryProvider: CategoryProviderForUser,
    @Inject(forwardRef(() => SubCategoryProviderForUser))
    private readonly subCategoryProvider: SubCategoryProviderForUser,
    @Inject(forwardRef(() => ProgramProviderForUser))
    private readonly programProvider: ProgramProviderForUser,
  ) {}

  async getSingleCoachProfile(
    userId: string,
    coachId: string,
  ): Promise<GetProfileSuccessResponseDtoForUser> {
    const profile = await this.coachProfileRepository.getSingleCoachProfile(
      coachId,
    );
    throwNotFoundErr(
      !profile,
      'Coach profile not found',
      COACH_PROFILE_NOT_FOUND,
    );

    const responseDto = deepCasting(GetProfileResponseDtoForUser, profile);
    responseDto.userName = await this.userInfoProvider.getUserName(
      profile.userId,
    );
    responseDto.coachCategoryName =
      await this.categoryProvider.getCoachCategoryName(profile.coachCategoryId);
    responseDto.hasMySubscription =
      await this.subscriptionProvider.doesUserSubscribedInAnyProgramOfTheCoach(
        userId,
        coachId,
      );

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  /**
   * User can filter the coach profiles in multiple ways, all these ways we have defined in the enum
   * GetMultipleCoachProfileByUserFilterType. Based on the filter type we will return the coach profiles.
   * And also the api user has to pass the required parameters that is mandatory for differnt filter type.
   * @param queryDto
   * @returns
   */
  async getFilteredCoachProfiles(
    queryDto: GetCoachProfilesQueryDtoForUser,
  ): Promise<GetCoachProfilesSuccessResponseDtoForUser> {
    const coachProfiles = await this.fetchCoachProfilesByFilter(queryDto);

    const validCoachProfiles = coachProfiles.filter(
      (profile): profile is CoachProfileEntity => !!profile,
    );

    const userNames = await this.userInfoProvider.getUserNames(
      validCoachProfiles.map((profile) => profile.userId),
    );

    const categoryNameMap =
      await this.categoryProvider.getCoachCategoryIdAndNameMap();

    const dtos: GetCoachProfilesResponseDtoForUser[] = [];

    validCoachProfiles.forEach((profile, index) => {
      try {
        const dto = deepCasting(GetCoachProfilesResponseDtoForUser, profile);
        dto.userName = userNames[index] || '';
        dto.coachCategoryName =
          categoryNameMap.get(profile.coachCategoryId) || '';

        // Transform the image URL to include "small/" before "original/"
        if (dto.media[0].url && dto.media[0].url.includes('/original/')) {
          dto.media[0].url = dto.media[0].url.replace(
            '/original/',
            '/small/original/',
          );
        }

        dtos.push(dto);
      } catch (error) {
        console.warn('Error casting or mapping profile:', profile, error);
      }
    });

    return this.helper.serviceResponse.successResponse(dtos);
  }

  private async fetchCoachProfilesByFilter(
    queryDto: GetCoachProfilesQueryDtoForUser,
  ): Promise<CoachProfileEntity[]> {
    const offset = queryDto.offset ?? 0;
    const limit = queryDto.limit ?? DEFAULT_PAGINATION_LIMIT;

    if (queryDto.filterType === GetCoachProfilesFilterTypeForUser.CATEGORY) {
      throwBadReqErrIf(
        !queryDto.categoryId,
        'Category ID is required',
        INCOMPLETE_QUERY_PARAMS,
      );
      const coachIds =
        await this.categoryProvider.selectDistinctCoachIdsByCategoryId(
          queryDto.categoryId,
          offset,
          limit,
        );
      return await this.coachProfileRepository.getCoachProfilesByCoachIds(
        coachIds,
      );
    }

    if (
      queryDto.filterType === GetCoachProfilesFilterTypeForUser.SUB_CATEGORY
    ) {
      throwBadReqErrIf(
        !queryDto.subCategoryId,
        'Sub category ID is required',
        INCOMPLETE_QUERY_PARAMS,
      );
      const coachIds =
        await this.subCategoryProvider.selectDistinctCoachIdsBySubCategoryId(
          queryDto.subCategoryId,
          offset,
          limit,
        );

      console.log('coach ids', coachIds);

      const profiles =
        await this.coachProfileRepository.getCoachProfilesByCoachIds(coachIds);
      console.log('coach profiles', profiles);
      return profiles;
    }

    if (queryDto.filterType === GetCoachProfilesFilterTypeForUser.BEST) {
      return await this.coachProfileRepository.getBestProfilesForEachCategory(
        offset,
        limit,
      );
    }

    if (queryDto.filterType === GetCoachProfilesFilterTypeForUser.NAME) {
      throwBadReqErrIf(
        !queryDto.namePrefix,
        'Name prefix is required',
        INCOMPLETE_QUERY_PARAMS,
      );
      return await this.coachProfileRepository.getProfilesByNamePrefix(
        queryDto.namePrefix,
        offset,
        limit,
      );
    }

    return [];
  }

  /**
   * This function will basically return the profile status of the user. Profile status includes
   * whether the user has verified coach profile, pending coach profile, rejected coach profile and
   * total subscriptions count. So that the phone side can easily distinguish the app user whether
   * he is a coach or a student. Check response dto for more details.
   * @param userId current user id
   * @returns profile status response dto
   */
  async getProfileStatus(
    userId: string,
  ): Promise<ProfileStatusSuccessResponseDtoForUser> {
    const responseDto = new ProfileStatusResponseDtoForUser();

    const activeProfiles =
      await this.coachProfileRepository.getActiveProfilesByUserId(userId);
    responseDto.activeCoachProfiles = activeProfiles.map((profile) => {
      const singleProfileDto =
        new ActiveCoachProfileDetailsResponseDtoForUser();
      singleProfileDto.id = profile.id;
      singleProfileDto.profileName = profile.profileName;
      return singleProfileDto;
    });

    responseDto.activeSubscriptions =
      await this.subscriptionProvider.getActiveSubscriptionIds(userId);
    responseDto.hasActiveCoachProfile = activeProfiles.length > 0;
    responseDto.pendingProfileCount =
      await this.pendingProfileProvider.getPendingApplicationCount(userId);
    responseDto.approvedProfileCount =
      await this.pendingProfileProvider.getApprovedApplicationCount(userId);
    responseDto.rejectedProfileCount =
      await this.pendingProfileProvider.getRejectedApplicationCount(userId);
    responseDto.totalSubscriptionsCount =
      await this.subscriptionProvider.getTotalSubscriptionCount(userId);

    return this.helper.serviceResponse.successResponse(responseDto);
  }
}
