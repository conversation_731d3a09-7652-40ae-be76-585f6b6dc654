/* eslint-disable @typescript-eslint/no-unused-vars */
import { Injectable } from '@nestjs/common';
import { buddyAffinityConfig, postConfig } from 'config/post';
import { sharedConfig } from 'config/shared';
import { randomUUID } from 'crypto';
import {
  CreatePost,
  CreatePostCommentRequest,
  FitBuddiesType,
  GetPostListWithUserInfo,
  GetTimelineListWithUserInfo,
  IPreferenceRes,
  PostLiker,
  UpdatePostBody,
} from 'models';
import mongoose, { PipelineStage } from 'mongoose';
import { FitBuddiesModel } from 'src/database/fitBuddies/fidBuddies.model';
import { PendingPostModel } from 'src/database/post/pendingPost.model';
import { PostModel } from 'src/database/post/post.model';
import { PostCommentModel } from 'src/database/post/postComment.model';
import { PostReactionModel } from 'src/database/post/postReactions.model';
import { UserModel } from 'src/database/user/user.model';
import { FitBuddies } from 'src/entity/fitBuddies';
import {
  Post,
  PostComment,
  PostPrivacy,
  PostReaction,
  PostReactionerUserType,
  PostType,
} from 'src/entity/post';
import { User } from 'src/entity/user';
import { Helper } from 'src/helper/helper.interface';
import { PreferenceRepository } from 'src/modules/preference/repository';
import { PostHelperRepository } from './post.helper.repository';

@Injectable()
export class PostRepository {
  constructor(
    private readonly postHelperRepository: PostHelperRepository,
    private readonly preferenceRepository: PreferenceRepository,
    private helper: Helper,
  ) {}

  async createPost(body: CreatePost): Promise<Post | null> {
    try {
      body.id = randomUUID();
      const pendingPost = await PendingPostModel.create(body);
      const newPendingPost = pendingPost?.toObject();
      delete newPendingPost?._id;
      return newPendingPost;
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async deletePost(postId: string, userId: string): Promise<Post | null> {
    try {
      const post = await PostModel.findOneAndRemove({
        id: postId,
        userId: userId,
      })
        .select('-_id')
        .lean();

      if (post) {
        await PostCommentModel.findOneAndRemove({
          postId: postId,
          userId: userId,
        })
          .select('-_id')
          .lean();

        await PostReactionModel.findOneAndRemove({
          postId: postId,
          userId: userId,
        })
          .select('-_id')
          .lean();
      }
      return post;
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async updatePost(
    query: Record<string, any>,
    data: UpdatePostBody,
  ): Promise<Post | null> {
    try {
      return await PostModel.findOneAndUpdate(
        query,
        { $set: data },
        { new: true },
      ).lean();
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async getPost(query: Record<string, any>): Promise<Post | null> {
    try {
      return await PostModel.findOne(query).lean();
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async getUserInfo(query: Record<string, any>): Promise<User | null> {
    try {
      return await UserModel.findOne(query).select('-_id id name image').lean();
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async getPostComments(
    query: Record<string, any>,
    skip?: number,
    limit?: number,
  ): Promise<PostComment[] | null> {
    try {
      const comments = await PostCommentModel.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .select('-_id -postId -updatedAt')
        .lean();

      const users: User[] = await UserModel.find({
        id: { $in: [...comments.map((comment) => comment.userId)] },
      })
        .select('-_id name image id')
        .lean();

      const map = new Map<string, User>();
      users?.length &&
        (await Promise.allSettled(
          users.map(async (user: User) => {
            map.set(user.id, user);
          }),
        ));

      return [
        ...comments.map((comment) => {
          return { ...comment, userInfo: map.get(comment.userId) };
        }),
      ];
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  // single reaction
  getPostLikersPipeline(postId: string, skip: number, limit: number) {
    const allowedReactions = [
      'LIKE',
      'LOVE',
      'FIRE',
      'FLEX',
      'RUN',
      'DUMBBELL',
      'WATER',
      'SWEAT',
      'GOAL',
      'HEART',
      'THUMBS_UP',
      'CLAP',
      'SURPRISED',
      'FIST',
      'FACE_ASTONISHED',
      'HIGH_FIVE',
      'FLAG',
      'GREEN_HEART',
    ];
    const aggregatePipeLine: PipelineStage[] = [
      {
        $match: {
          postId: postId,
          type: { $in: allowedReactions },
        },
      },
      { $sort: { updatedAt: -1 } },
      { $skip: Number(skip) || sharedConfig.defaultSkip },
      {
        $limit: Number(limit) || sharedConfig.defaultLimit,
      },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: 'id',
          as: 'likerInfo',
        },
      },
      {
        $project: {
          id: 1,
          _id: 0,
          userId: 1,
          name: {
            $first: '$likerInfo.name',
          },
          image: {
            $first: '$likerInfo.image',
          },
        },
      },
    ];
    return aggregatePipeLine;
  }

  async getPostLikersInfo(
    postId: string,
    skip?: number,
    limit?: number,
  ): Promise<PostLiker[] | null> {
    try {
      const stage = this.getPostLikersPipeline(postId, skip, limit);
      const likersList = await PostReactionModel.aggregate(stage);

      return likersList;
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  getPostMultiLikersPipeline(postId: string, skip: number, limit: number) {
    const allowedReactions = [
      'LIKE',
      'LOVE',
      'FIRE',
      'FLEX',
      'RUN',
      'DUMBBELL',
      'WATER',
      'SWEAT',
      'GOAL',
      'HEART',
      'THUMBS_UP',
      'CLAP',
      'SURPRISED',
      'FIST',
      'FACE_ASTONISHED',
      'HIGH_FIVE',
      'FLAG',
      'GREEN_HEART',
    ];

    const aggregatePipeline: PipelineStage[] = [
      {
        $match: {
          postId: postId,
          type: { $in: allowedReactions },
        },
      },
      { $sort: { updatedAt: -1 } },
      { $skip: Number(skip) || 0 },
      { $limit: Number(limit) || 10 },
      {
        $group: {
          _id: '$type',
          users: { $addToSet: '$userId' },
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'users',
          foreignField: 'id',
          as: 'userDetails',
        },
      },
      {
        $project: {
          type: '$_id',
          _id: 0,
          users: {
            $map: {
              input: '$userDetails',
              as: 'user',
              in: {
                id: '$$user.id',
                name: '$$user.name',
                image: {
                  profile: '$$user.image',
                },
              },
            },
          },
        },
      },
    ];

    return aggregatePipeline;
  }

  async getPostMultiLikersInfo(
    postId: string,
    skip?: number,
    limit?: number,
  ): Promise<PostLiker[] | null> {
    try {
      const stage = this.getPostMultiLikersPipeline(postId, skip, limit);
      // console.log(stage)
      const likersList = await PostReactionModel.aggregate(stage);

      return likersList;
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  // Multireaction

  getAllPostReactionerPipeline(postId: string, skip: number, limit: number) {
    const allowedReactions = [
      'LIKE',
      'LOVE',
      'FIRE',
      'FLEX',
      'RUN',
      'DUMBBELL',
      'WATER',
      'SWEAT',
      'GOAL',
      'HEART',
      'THUMBS_UP',
      'CLAP',
      'SURPRISED',
      'FIST',
      'FACE_ASTONISHED',
      'HIGH_FIVE',
      'FLAG',
      'GREEN_HEART',
    ];
    const aggregatePipeLine: PipelineStage[] = [
      {
        $match: {
          postId: postId,
          type: { $in: allowedReactions },
        },
      },
      { $sort: { updatedAt: -1 } },
      { $skip: Number(skip) || sharedConfig.defaultSkip },
      {
        $limit: Number(limit) || sharedConfig.defaultLimit,
      },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: 'id',
          as: 'likerInfo',
        },
      },
      {
        $project: {
          id: 1,
          _id: 0,
          userId: 1,
          name: {
            $first: '$likerInfo.name',
          },
          image: {
            $first: '$likerInfo.image',
          },
        },
      },
    ];
    return aggregatePipeLine;
  }

  getEachPostReactionerPipeline(
    postId: string,
    reactionType: PostReactionerUserType,
    skip: number,
    limit: number,
  ) {
    const aggregatePipeLine: PipelineStage[] = [
      {
        $match: {
          postId: postId,
          type: reactionType,
        },
      },
      { $sort: { updatedAt: -1 } },
      { $skip: Number(skip) || sharedConfig.defaultSkip },
      {
        $limit: Number(limit) || sharedConfig.defaultLimit,
      },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: 'id',
          as: 'likerInfo',
        },
      },
      {
        $project: {
          id: 1,
          _id: 0,
          userId: 1,
          name: {
            $first: '$likerInfo.name',
          },
          image: {
            $first: '$likerInfo.image',
          },
        },
      },
    ];
    return aggregatePipeLine;
  }

  async getPostReactionerInfo(
    postId: string,
    skip?: number,
    limit?: number,
    reactionType?: PostReactionerUserType,
  ): Promise<any> {
    try {
      let likersList = {};

      if (reactionType === PostReactionerUserType.ALL) {
        console.log(reactionType, 'All Reaction type');

        const stage = this.getAllPostReactionerPipeline(postId, skip, limit);
        const userByReaction = await PostReactionModel.aggregate(stage);
        likersList = {
          reactionType: reactionType,
          users: userByReaction,
        };
      } else {
        const stage = this.getEachPostReactionerPipeline(
          postId,
          reactionType,
          skip,
          limit,
        );
        const userByReaction = await PostReactionModel.aggregate(stage);

        likersList = {
          reactionType: reactionType,
          users: userByReaction,
        };
      }

      // console.log(likersList)

      return likersList;
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async getPostMedia(
    userId: string,
    buddy: FitBuddies,
  ): Promise<Post[] | null> {
    try {
      const mergeObjArray = new Set<{ userId: string; privacy: string }>();
      buddy?.type === FitBuddiesType.FIT_BUDDY &&
        mergeObjArray.add({
          userId,
          privacy: PostPrivacy.FRIENDS,
        });

      mergeObjArray.add({
        userId,
        privacy: PostPrivacy.PUBLIC,
      });

      const aggregatePipeLine: PipelineStage[] =
        this.postHelperRepository.getFetchPostAggregateQuery(
          new Set<string>().add(userId),
          mergeObjArray,
        );

      const posts = await PostModel.aggregate(aggregatePipeLine);
      return this.mappedSharedPostUserInfo(
        posts as Post & Partial<GetPostListWithUserInfo>[],
      );
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async getOwnPostMedia(userId: string): Promise<Post[] | null> {
    try {
      return await PostModel.find(
        { userId, type: { $ne: PostType.SHARE } },
        { _id: 0, id: 1, images: 1, videos: 1 },
      )
        .sort({ updatedAt: -1 })
        .lean();
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  getPostPipeline = (query) => {
    const allowedReactions = [
      'LIKE',
      'LOVE',
      'FIRE',
      'FLEX',
      'RUN',
      'DUMBBELL',
      'WATER',
      'SWEAT',
      'GOAL',
      'HEART',
      'THUMBS_UP',
      'CLAP',
      'SURPRISED',
      'FIST',
      'FACE_ASTONISHED',
      'HIGH_FIVE',
      'FLAG',
      'GREEN_HEART',
    ];

    const aggregatePipeLine: PipelineStage[] = [
      {
        $match: {
          ...query,
          type: { $ne: PostType.SHARE },
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: 'id',
          pipeline: [
            {
              $project: {
                id: 1,
                name: 1,
                image: 1,
                _id: 0,
              },
            },
          ],
          as: 'userInfo',
        },
      },
      {
        $lookup: {
          from: 'post-reactions',
          localField: 'id',
          foreignField: 'postId',
          pipeline: [
            {
              // $match: { type: 'LIKE' },
              $match: { type: { $in: allowedReactions } },
            },
            {
              $limit: postConfig.likersInfoLimit,
            },
            {
              $lookup: {
                from: 'users',
                localField: 'userId',
                foreignField: 'id',
                as: 'likerInfo',
              },
            },
            {
              $project: {
                id: 1,
                _id: 0,
                userId: 1,
                name: {
                  $first: '$likerInfo.name',
                },
                image: {
                  $first: '$likerInfo.image',
                },
              },
            },
          ],
          as: 'likersInfo',
        },
      },
      {
        $lookup: {
          from: 'post-reactions',
          localField: 'id',
          foreignField: 'postId',
          pipeline: [
            {
              // $match: { type: 'LIKE' },
              $match: { type: { $in: allowedReactions } },
            },
            {
              $limit: postConfig.likersInfoLimit,
            },
            {
              $lookup: {
                from: 'users',
                localField: 'userId',
                foreignField: 'id',
                as: 'totalReactionerInfo',
              },
            },
            {
              $project: {
                id: 1,
                _id: 0,
                userId: 1,
                name: {
                  $first: '$totalReactionerInfo.name',
                },
                image: {
                  $first: '$totalReactionerInfo.image',
                },
              },
            },
          ],
          as: 'totalReactionerInfo',
        },
      },

      // Each type reaction
      {
        $lookup: {
          from: 'post-reactions',
          localField: 'id',
          foreignField: 'postId',
          pipeline: [
            {
              $match: { type: { $in: allowedReactions } }, // Filter based on allowed reaction types
            },
            {
              $group: {
                _id: '$type', // Group by reaction type
                users: { $addToSet: '$userId' }, // Collect users for each reaction type
              },
            },
            {
              $lookup: {
                from: 'users',
                localField: 'users',
                foreignField: 'id',
                as: 'reactionUsers', // Get user details for each reaction type
              },
            },
            {
              $project: {
                type: '$_id', // Rename _id to type (reaction type)
                _id: 0,
                users: {
                  $map: {
                    input: '$reactionUsers',
                    as: 'user',
                    in: {
                      id: '$$user.id',
                      name: '$$user.name',
                      image: '$$user.image',
                    },
                  },
                },
              },
            },
          ],
          as: 'reactionTypeUserInfo', // Store reaction details in the result
        },
      },
      {
        $project: {
          _id: 0,
          weight: 0,
        },
      },
      {
        $unwind: '$userInfo',
      },
    ];

    return aggregatePipeLine;
  };

  async getPostAggregate(
    query: Record<string, any>,
  ): Promise<(Post & Partial<GetPostListWithUserInfo>) | null> {
    try {
      const stage = this.getPostPipeline(query);
      const posts = await PostModel.aggregate(stage);

      return posts.length ? posts[0] : null;
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async findFitBuddy(query: Record<string, any>): Promise<FitBuddies | null> {
    try {
      return await FitBuddiesModel.findOne(query).lean();
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async createPostComment(
    userId: string,
    data: CreatePostCommentRequest,
    postId: string,
    ownerId: string,
    buddy: FitBuddies,
  ): Promise<(PostComment & { userInfo: any }) | null> {
    // transaction session
    const session = await mongoose.startSession();
    try {
      // start session
      session.startTransaction();

      const doc: Partial<PostComment> = {
        ...data,
        userId,
        postId,
      };

      const query =
        buddy?.type === FitBuddiesType.FIT_BUDDY
          ? {
              userId,
              fitBuddyId: ownerId,
            }
          : {
              userId: ownerId,
              fitBuddyId: userId,
            };

      // Parallel API
      const [postComment, _, __] = await Promise.all([
        // create new post comment
        await new PostCommentModel(doc).save({ session }),

        // increment post comments count
        await PostModel.findOneAndUpdate(
          {
            id: postId,
          },
          {
            $inc: {
              totalComments: 1,
              weight: userId !== ownerId ? postConfig.comment_weight : 0,
            },
          },
          { session },
        )
          .lean()
          .exec(),

        // increment fitBuddies affinity
        buddy &&
          buddy.type &&
          (await FitBuddiesModel.findOneAndUpdate(
            query,
            {
              $push: {
                affinity: {
                  score: buddyAffinityConfig.comment_weight,
                },
              },
            },
            { session },
          )
            .lean()
            .exec()),
      ]);

      // commit the session
      await session.commitTransaction();
      const comment = postComment?.toObject();
      delete comment._id;
      return { ...comment, userInfo: null };
    } catch (error) {
      console.log(error);
      // abort & end session
      await session.abortTransaction();
      await session.endSession();
      return null;
    } finally {
      await session.endSession();
    }
  }

  // async addPostReaction(
  //   userId: string,
  //   postId: string,
  //   liked: boolean,
  //   type: string,
  //   ownerId: string,
  //   buddy: FitBuddies,
  // ): Promise<PostReaction | null> {
  //   // transaction session
  //   const session = await mongoose.startSession();
  //   try {
  //     // start session
  //     session.startTransaction();

  //     const doc = {
  //       type,
  //       userId,
  //       postId,
  //     };

  //     const reaction = liked
  //       ? await new PostReactionModel(doc).save({ session })
  //       : await PostReactionModel.findOneAndDelete(
  //           {
  //             postId,
  //             userId,
  //             type,
  //           },
  //           {
  //             session,
  //           },
  //         ).lean();

  //     // increment or decrement post likes count
  //     // const post = await PostModel.findOneAndUpdate(
  //     //   {
  //     //     id: postId,
  //     //     totalLikes: { $gte: liked ? 0 : 1 },
  //     //   },
  //     //   {
  //     //     $inc: {
  //     //       totalLikes: liked ? 1 : -1,
  //     //       weight:
  //     //         userId !== ownerId
  //     //           ? liked
  //     //             ? postConfig.like_weight
  //     //             : -postConfig.like_weight
  //     //           : 0,
  //     //     },
  //     //   },
  //     //   { session },
  //     // )
  //     //   .lean()
  //     //   .exec();

  //     const post = await PostModel.findOneAndUpdate(
  //       {
  //         id: postId,
  //         totalReactions: { $gte: liked ? 0 : 1 },
  //         totalLikes: { $gte: liked ? 0 : 1 },
  //       },
  //       {
  //         $inc: {
  //           totalReactions: liked ? 1 : -1,
  //           totalLikes: liked ? 1 : -1,
  //           weight:
  //             userId !== ownerId
  //               ? liked
  //                 ? postConfig.like_weight
  //                 : -postConfig.like_weight
  //               : 0,
  //         },
  //       },
  //       { session },
  //     )
  //       .lean()
  //       .exec();

  //     const query =
  //       buddy?.type === FitBuddiesType.FIT_BUDDY
  //         ? {
  //             userId,
  //             fitBuddyId: ownerId,
  //           }
  //         : {
  //             userId: ownerId,
  //             fitBuddyId: userId,
  //           };

  //     // increment fitBuddies affinity
  //     liked &&
  //       buddy &&
  //       buddy.type &&
  //       (await FitBuddiesModel.findOneAndUpdate(
  //         query,
  //         {
  //           $push: {
  //             affinity: {
  //               score: buddyAffinityConfig.like_weight,
  //             },
  //           },
  //         },
  //         { session },
  //       )
  //         .lean()
  //         .exec());

  //     // commit the session
  //     await session.commitTransaction();
  //     return reaction;
  //   } catch (error) {
  //     console.log(error);
  //     // abort & end session
  //     await session.abortTransaction();
  //     await session.endSession();
  //     return null;
  //   } finally {
  //     await session.endSession();
  //   }
  // }

  async addPostReaction(
    userId: string,
    postId: string,
    liked: boolean,
    type: string,
    ownerId: string,
    buddy: FitBuddies,
  ): Promise<PostReaction | null> {
    // transaction session
    const session = await mongoose.startSession();
    try {
      // start session
      session.startTransaction();

      const doc = {
        type,
        userId,
        postId,
      };

      // console.log(doc)
      // console.log(liked)

      const reaction = liked
        ? await new PostReactionModel(doc).save({ session })
        : await PostReactionModel.findOneAndDelete(
            {
              postId,
              userId,
              type,
            },
            {
              session,
            },
          ).lean();

      // increment or decrement post likes count
      const post = await PostModel.findOneAndUpdate(
        {
          id: postId,
          totalLikes: { $gte: liked ? 0 : 1 },
          totalReactions: { $gte: liked ? 0 : 1 },
        },
        {
          $inc: {
            totalLikes: liked ? 1 : -1,
            totalReactions: liked ? 1 : -1,
            weight:
              userId !== ownerId
                ? liked
                  ? postConfig.like_weight
                  : -postConfig.like_weight
                : 0,
          },
        },
        { session },
      )
        .lean()
        .exec();

      const query =
        buddy?.type === FitBuddiesType.FIT_BUDDY
          ? {
              userId,
              fitBuddyId: ownerId,
            }
          : {
              userId: ownerId,
              fitBuddyId: userId,
            };

      // increment fitBuddies affinity
      liked &&
        buddy &&
        buddy.type &&
        (await FitBuddiesModel.findOneAndUpdate(
          query,
          {
            $push: {
              affinity: {
                score: buddyAffinityConfig.like_weight,
              },
            },
          },
          { session },
        )
          .lean()
          .exec());

      // commit the session
      await session.commitTransaction();
      return reaction;
    } catch (error) {
      console.log(error);
      // abort & end session
      await session.abortTransaction();
      await session.endSession();
      return null;
    } finally {
      await session.endSession();
    }
  }

  async addPostMultiReaction(
    userId: string,
    postId: string,
    liked: boolean,
    type: string,
    ownerId: string,
    buddy: FitBuddies,
  ): Promise<PostReaction | null> {
    // transaction session
    const session = await mongoose.startSession();
    try {
      // start session
      session.startTransaction();
      const doc = {
        type,
        userId,
        postId,
      };
      // console.log(doc)
      // console.log(liked)
      // const reaction = liked
      //   ? await new PostReactionModel(doc).save({ session })
      //   : await PostReactionModel.findOneAndDelete(
      //       {
      //         postId,
      //         userId,
      //         type,
      //       },
      //       {
      //         session,
      //       },
      //     ).lean();
      let reaction: PostReaction;
      if (liked) {
        // If the user is liking the post, create a new reaction
        reaction = await new PostReactionModel({
          type,
          userId,
          postId,
        }).save({ session });
      } else {
        // If the user is unliking the post, delete the reaction but return it with type: null
        reaction = await PostReactionModel.findOneAndDelete(
          { postId, userId, type },
          { session },
        ).lean();
        // Set type to null before returning the response
        if (reaction) {
          reaction.type = null;
        }
      }
      // increment or decrement post likes count
      const post = await PostModel.findOneAndUpdate(
        {
          id: postId,
          totalLikes: { $gte: liked ? 0 : 1 },
          totalReactions: { $gte: liked ? 0 : 1 },
        },
        {
          $inc: {
            totalLikes: liked ? 1 : -1,
            totalReactions: liked ? 1 : -1,
            weight:
              userId !== ownerId
                ? liked
                  ? postConfig.like_weight
                  : -postConfig.like_weight
                : 0,
          },
        },
        { session },
      )
        .lean()
        .exec();
      const query =
        buddy?.type === FitBuddiesType.FIT_BUDDY
          ? {
              userId,
              fitBuddyId: ownerId,
            }
          : {
              userId: ownerId,
              fitBuddyId: userId,
            };
      // increment fitBuddies affinity
      liked &&
        buddy &&
        buddy.type &&
        (await FitBuddiesModel.findOneAndUpdate(
          query,
          {
            $push: {
              affinity: {
                score: buddyAffinityConfig.like_weight,
              },
            },
          },
          { session },
        )
          .lean()
          .exec());
      // commit the session
      await session.commitTransaction();
      // console.log(reaction)
      return reaction;
    } catch (error) {
      console.log(error);
      // abort & end session
      await session.abortTransaction();
      await session.endSession();
      return null;
    } finally {
      await session.endSession();
    }
  }

  async checkPostReaction(
    query: Record<string, any>,
  ): Promise<PostReaction | null> {
    try {
      return await PostReactionModel.findOne(query).lean();
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  // async checkLikedPost(
  //   query: Record<string, any>,
  // ): Promise<PostReaction | null> {
  //   try {
  //     return await PostReactionModel.findOne(query).lean();
  //   } catch (error) {
  //     console.log(error);
  //     return null;
  //   }
  // }

  async updatePostReactionType(
    query: Record<string, any>,
    newReactionType: string,
  ): Promise<PostReaction | null> {
    try {
      return await PostReactionModel.findOneAndUpdate(
        query, // Filter to find the post reaction
        { $set: { type: newReactionType } }, // Update only the reaction type
        { new: true, lean: true }, // Return the updated document
      ).exec();
    } catch (error) {
      console.error('Error updating post reaction type:', error);
      return null;
    }
  }

  async updateReactionsCount(
    postId: string,
    reactionType: string,
    increment: boolean,
  ): Promise<any> {
    try {
      const post = await PostModel.findOne({ id: postId });

      const reactions: any = post.reactions; // Your reactions object
      // console.log(reactions)

      // const reactionsCount = reactions.size
      // console.log(reactionsCount)

      const reactionsCount: any = Array.from(reactions.values()).reduce(
        (sum: any, count: any) => sum + count,
        0,
      );
      // console.log(reactionsCount);

      let update: any;

      if (increment) {
        update = { $inc: { [`reactions.${reactionType}`]: 1 } };
      } else {
        if (reactionsCount > 0) {
          update = { $inc: { [`reactions.${reactionType}`]: -1 } };
        }
      }
      // console.log(update)

      // const update = increment
      // ? { $inc: { [`reactions.${reactionType}`]: 1 } }
      // : { $inc: { [`reactions.${reactionType}`]: -1 } };

      return await PostModel.findOneAndUpdate({ id: postId }, update).exec();
    } catch (error) {
      console.error('Error updating post reaction count:', error);
      return null;
    }
  }

  async checkLikedPost(
    query: Record<string, any>,
  ): Promise<PostReaction | null> {
    console.log(query);
    try {
      return await PostReactionModel.findOne(query).lean();
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async sharePost(
    originalPostId: string,
    ownerId: string,
    buddyType: string,
    body: CreatePost,
  ): Promise<Post | null> {
    // transaction session
    const session = await mongoose.startSession();
    try {
      // start session
      session.startTransaction();
      body.id = randomUUID();
      const { userId } = body;

      const query =
        buddyType === FitBuddiesType.FIT_BUDDY
          ? {
              userId,
              fitBuddyId: ownerId,
            }
          : {
              userId: ownerId,
              fitBuddyId: userId,
            };

      // Parallel API
      const [pendingPost, _, __] = await Promise.all([
        // create new post comment
        await new PendingPostModel(body).save({ session }),

        // increment post comments count
        await PostModel.findOneAndUpdate(
          {
            id: originalPostId,
          },
          {
            $inc: {
              totalShares: 1,
              weight: userId !== ownerId ? postConfig.share_weight : 0,
            },
          },
          { session },
        ).lean(),

        // increment fitBuddies affinity
        await FitBuddiesModel.findOneAndUpdate(
          query,
          {
            $push: {
              affinity: {
                score: buddyAffinityConfig.share_weight,
              },
            },
          },
          { session },
        ).lean(),
      ]);

      // commit the session
      await session.commitTransaction();
      const newSharedPendingPost = pendingPost?.toObject();
      delete newSharedPendingPost?._id;
      return newSharedPendingPost;
    } catch (error) {
      console.log(error);
      // abort & end session
      await session.abortTransaction();
      await session.endSession();
      return null;
    } finally {
      await session.endSession();
    }
  }

  async mappedSharedPostUserInfo(
    posts: Post & Partial<GetPostListWithUserInfo>[],
  ): Promise<(Post & Partial<GetPostListWithUserInfo>)[] | null> {
    const sharedPostOwnerIds = [];
    for (let i = 0, len = posts.length; i < len; i++) {
      posts[i].sharedPost &&
        sharedPostOwnerIds.push(posts[i].sharedPost.userId);
    }

    const users: User[] = await UserModel.find({
      id: {
        $in: [
          ...sharedPostOwnerIds.map((sharedPostOwnerId) => sharedPostOwnerId),
        ],
      },
    })
      .select('-_id id name image')
      .lean();

    const map = new Map<string, User>();
    for (let i = 0, len = users.length; i < len; i++) {
      map.set(users[i].id, users[i]);
    }

    return [
      ...posts.map((post: any) => {
        post.sharedPost &&
          (post.sharedPost.userInfo = map.get(post.sharedPost.userId));
        return post;
      }),
    ];
  }

  async getUserPostList(
    buddiesIds: Set<string>,
    mergeObjArray: Set<{ userId: string; privacy: string }>,
    offset?: number,
    limit?: number,
  ): Promise<(Post & Partial<GetTimelineListWithUserInfo>)[] | null> {
    try {
      const aggregatePipeLine: PipelineStage[] =
        this.postHelperRepository.getFetchPostAggregateQuery(
          buddiesIds,
          mergeObjArray,
          offset,
          limit,
        );
      return await PostModel.aggregate(aggregatePipeLine);
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async getOwnPostList(
    myId: string,
    offset?: number,
    limit?: number,
  ): Promise<(Post & Partial<GetTimelineListWithUserInfo>)[] | null> {
    const allowedReactions = [
      'LIKE',
      'LOVE',
      'FIRE',
      'FLEX',
      'RUN',
      'DUMBBELL',
      'WATER',
      'SWEAT',
      'GOAL',
      'HEART',
      'THUMBS_UP',
      'CLAP',
      'SURPRISED',
      'FIST',
      'FACE_ASTONISHED',
      'HIGH_FIVE',
      'FLAG',
      'GREEN_HEART',
    ];
    try {
      const aggregatePipeLine: PipelineStage[] = [
        {
          $match: {
            userId: { $eq: myId },
          },
        },
        {
          $sort: {
            createdAt: -1,
          },
        },
        {
          $lookup: {
            from: 'post-reactions',
            localField: 'id',
            foreignField: 'postId',
            pipeline: [
              {
                // $match: { type: 'LIKE' },
                $match: { type: { $in: allowedReactions } },
              },
              {
                $lookup: {
                  from: 'users',
                  localField: 'userId',
                  foreignField: 'id',
                  as: 'likerInfo',
                },
              },
              {
                $project: {
                  id: 1,
                  _id: 0,
                  userId: 1,
                  name: {
                    $first: '$likerInfo.name',
                  },
                  image: {
                    $first: '$likerInfo.image',
                  },
                },
              },
            ],
            as: 'likersInfo',
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: 'id',
            pipeline: [
              {
                $project: {
                  name: 1,
                  image: 1,
                  _id: 0,
                },
              },
            ],
            as: 'userInfo',
          },
        },
        {
          $lookup: {
            from: 'post-reactions',
            localField: 'id',
            foreignField: 'postId',
            pipeline: [
              {
                // $match: { type: 'LIKE' },
                $match: { type: { $in: allowedReactions } },
              },
              {
                $limit: postConfig.likersInfoLimit,
              },
              {
                $lookup: {
                  from: 'users',
                  localField: 'userId',
                  foreignField: 'id',
                  as: 'totalReactionerInfo',
                },
              },
              {
                $project: {
                  id: 1,
                  _id: 0,
                  userId: 1,
                  name: {
                    $first: '$totalReactionerInfo.name',
                  },
                  image: {
                    $first: '$totalReactionerInfo.image',
                  },
                },
              },
            ],
            as: 'totalReactionerInfo',
          },
        },

        // Each type reaction
        {
          $lookup: {
            from: 'post-reactions',
            localField: 'id',
            foreignField: 'postId',
            pipeline: [
              {
                $match: { type: { $in: allowedReactions } }, // Filter based on allowed reaction types
              },
              {
                $group: {
                  _id: '$type', // Group by reaction type
                  users: { $addToSet: '$userId' }, // Collect users for each reaction type
                },
              },
              {
                $lookup: {
                  from: 'users',
                  localField: 'users',
                  foreignField: 'id',
                  as: 'reactionUsers', // Get user details for each reaction type
                },
              },
              {
                $project: {
                  type: '$_id', // Rename _id to type (reaction type)
                  _id: 0,
                  users: {
                    $map: {
                      input: '$reactionUsers',
                      as: 'user',
                      in: {
                        id: '$$user.id',
                        name: '$$user.name',
                        image: '$$user.image',
                      },
                    },
                  },
                },
              },
            ],
            as: 'reactionTypeUserInfo', // Store reaction details in the result
          },
        },

        {
          $unwind: '$userInfo',
        },
        {
          $project: { weight: 0 },
        },
        {
          $skip: Number(offset) || 0,
        },
        {
          $limit: Number(limit) || postConfig.timelineDefaultLimit,
        },
      ];

      return await PostModel.aggregate(aggregatePipeLine);
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async getUserPreference(userId: string): Promise<IPreferenceRes | null> {
    try {
      return await this.preferenceRepository.findOneByUserId(userId);
    } catch (error) {
      console.log(error);
      return null;
    }
  }
}
