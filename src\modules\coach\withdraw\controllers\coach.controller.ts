import { Body, Controller, HttpStatus, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags, } from '@nestjs/swagger';
import { OnlyAuthGuard, OnlyRoleGuard, Role, Roles, } from 'src/authentication/guards/auth-role.guard';
import { COACH_API_FOR_COACH } from '../../common/const/swagger.const';

import { User as UserInfo } from 'src/decorators/auth.decorator';
import { User } from 'src/entity/user';
import { WithdrawServiceForUser } from '../services/coach.service';
import {
  CreateWithdrawCoachMobileNumberRequestDtoForUser,
  CreateWithdrawCoachMobileNumberSuccessfulResponseDtoForUser,
} from './dtos/coach-create-withdraw-mobile.dto';
import {
  CreateWithdrawRequestDtoForUser,
  CreateWithdrawSuccessfulResponseDtoForUser,
  UpdateWithdrawMobileNumberRequestDtoForUser,
} from './dtos/coach-create-withdraw-req.dto';

@ApiTags(COACH_API_FOR_COACH)
@ApiBearerAuth()
@Controller('coach/:coachId')
export class WithdrawControllerForUser {
  constructor(private service: WithdrawServiceForUser) {}

  @Post('withdraw')
  @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
  @Roles(Role.User)
  @ApiOperation({ summary: 'coach to withdraw money' })
  @ApiParam({
    name: 'coachId',
    description: 'The coachId whose money to be withdrawn',
    type: String,
  })
  @ApiBody({
    description: 'Coach Withdraw information',
    type: CreateWithdrawRequestDtoForUser,
  })
  @ApiResponse({
    description: 'Returns the withdraw information',
    type: CreateWithdrawSuccessfulResponseDtoForUser,
    status: HttpStatus.CREATED,
  })
  async addCoachWithdraw(
    @UserInfo() user: User,
    @Param('coachId') coachId: string,
    @Body() reqDto: CreateWithdrawRequestDtoForUser,
  ) {
    return await this.service.makeWithdrawRequest(user.id, coachId, reqDto);
  }

  @Post('withdraw/mobile-number')
  @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
  @Roles(Role.User)
  @ApiOperation({ summary: 'coach mobile number to withdraw money' })
  @ApiParam({
    name: 'coachId',
    description: 'The coachId whose number to add',
    type: String,
  })
  @ApiBody({
    description: 'Coach Mobile information',
    type: CreateWithdrawCoachMobileNumberRequestDtoForUser,
  })
  @ApiResponse({
    description: 'Returns the withdraw information',
    type: CreateWithdrawCoachMobileNumberSuccessfulResponseDtoForUser,
    status: HttpStatus.CREATED,
  })
  async addCoachWithdrawMobileNumber(
    @UserInfo() user: User,
    @Param('coachId') coachId: string,
    @Body() reqDto: CreateWithdrawCoachMobileNumberRequestDtoForUser,
  ) {
    return await this.service.addCoachWithdrawMobileNumber(user.id,coachId, reqDto);
  }

  @Patch('withdraw/mobile-number')
  @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
  @Roles(Role.User)
  @ApiOperation({ summary: 'Update Mobile number' })
  @ApiParam({
    name: 'coachId',
    description: 'The coachId whose mobile number to update',
    type: String,
  })
  @ApiBody({
    description: 'Coach Withdraw information',
    type: UpdateWithdrawMobileNumberRequestDtoForUser,
  })
  @ApiResponse({
    description: 'Returns the withdraw information',
    type: CreateWithdrawSuccessfulResponseDtoForUser,
    status: HttpStatus.CREATED,
  })
  async updateCoachNumber(
    @UserInfo() user: User,
    @Param('coachId') coachId: string,
    @Body() reqDto: UpdateWithdrawMobileNumberRequestDtoForUser,
  ) {
    return await this.service.updateMobileNumber(user.id,coachId, reqDto);
  }
}
