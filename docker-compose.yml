services:
  api:
    build:
      dockerfile: Dockerfile
      context: .
      # Only will build development stage from our dockerfile
      target: development
    volumes:
      - .:/usr/src/app
    env_file:
      - .env
    # Run a command against the development stage of the image
    command: yarn start:dev
    ports:
      - 3000:3000
  mongo1:
    container_name: mongo1
    image: mongo:6.0.3
    volumes:
      - ./data/db:/data/db
      - ./config/docker/rs-init.sh:/scripts/rs-init.sh
    networks:
      - fit-net
    ports:
      - 27021:27017
    restart: always
    entrypoint: ['/usr/bin/mongod', '--bind_ip_all', '--replSet', 'dbrs']
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: 123

  redis:
    image: redis:alpine
    container_name: 'redis'
    ports:
      - 6379:6379
    volumes:
      - ./data/redis/data/:/var/lib/redis/
      - ./data/redis/log/:/var/log/redis
    networks:
      - fit-net

  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: 'rabbitmq'
    ports:
      - 5672:5672
      - 15672:15672
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq/
      - rabbitmq_logs:/var/log/rabbitmq
    networks:
      - fit-net

volumes:
  rabbitmq_data:
  rabbitmq_logs:

networks:
  fit-net:
    driver: bridge
