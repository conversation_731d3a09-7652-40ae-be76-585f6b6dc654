import { Modu<PERSON> } from '@nestjs/common';
import { NotificationHelperService } from '../notification/services/helper.notification.service';
import { TaskModule } from '../task/task.module';
import { UserRepository } from '../user/repositories';
import { DietRepository } from './repositories';
import { DietHelperRepository } from './repositories/diet.helper.repository';
import { DietController } from './rest/diet.controller';
import { DietService } from './services';
import { DietHelperService } from './services/diet.helper.service';

@Module({
  imports: [TaskModule],
  controllers: [DietController],
  providers: [
    DietService,
    DietRepository,
    DietHelperRepository,
    NotificationHelperService,
    UserRepository,
    DietHelperService,
  ],
})
export class DietModule {}
