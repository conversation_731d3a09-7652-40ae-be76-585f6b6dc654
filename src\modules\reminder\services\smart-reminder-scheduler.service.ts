import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { SchedulerRegistry } from '@nestjs/schedule';
import { CronJob } from 'cron';
import { ReminderService } from './reminder.service';
import { SMART_SCHEDULER_CONFIG } from '../../../config/smart-scheduler.config';
import Redis from 'ioredis';

interface ServerMetrics {
  cpuUsage: number;
  memoryUsage: number;
  timestamp: number;
}

interface SchedulerMetrics {
  lastRun: number;
  consecutiveEmptyChecks: number;
  currentInterval: number;
  totalRuns: number;
  successfulRuns: number;
  failedRuns: number;
}

@Injectable()
export class SmartReminderSchedulerService implements OnModuleInit, OnModuleDestroy {
  private redis: Redis;
  private currentJob: CronJob | null = null;
  private metrics: SchedulerMetrics = {
    lastRun: 0,
    consecutiveEmptyChecks: 0,
    currentInterval: SMART_SCHEDULER_CONFIG.defaultInterval,
    totalRuns: 0,
    successfulRuns: 0,
    failedRuns: 0
  };
  private reminderCache: Set<string> = new Set();
  private lastCacheRefresh = 0;

  constructor(
    private readonly reminderService: ReminderService,
    private readonly schedulerRegistry: SchedulerRegistry,
  ) {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      maxRetriesPerRequest: 3,
    });
  }

  async onModuleInit() {
    console.log('🚀 Smart Reminder Scheduler initializing...');
    
    // Wait for Redis connection
    await this.waitForRedisConnection();
    
    // Initialize reminder cache
    await this.refreshReminderCache();
    
    // Schedule existing reminders
    await this.scheduleExistingReminders();
    
    // Start the smart scheduler
    await this.startSmartScheduler();
    
    console.log(`✅ Smart Reminder Scheduler started with ${this.metrics.currentInterval}-minute intervals`);
  }

  async onModuleDestroy() {
    if (this.currentJob) {
      this.currentJob.stop();
      this.schedulerRegistry.deleteCronJob('smart-reminder-scheduler');
    }
    await this.redis.quit();
  }

  private async waitForRedisConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.redis.status === 'ready') {
        console.log('Redis Client connected');
        resolve();
        return;
      }

      this.redis.on('ready', () => {
        console.log('Redis Client connected');
        resolve();
      });

      this.redis.on('error', (err) => {
        console.error('Redis Client Error:', err);
        reject(err);
      });

      // Timeout after 10 seconds
      setTimeout(() => {
        reject(new Error('Redis connection timeout'));
      }, 10000);
    });
  }

  private async refreshReminderCache(): Promise<void> {
    try {
      const now = Date.now();
      if (now - this.lastCacheRefresh < SMART_SCHEDULER_CONFIG.cacheRefreshInterval * 60 * 1000) {
        return; // Cache is still fresh
      }

      const activeReminders = await this.reminderService.getAllReminders();
      const activeReminderTimes = activeReminders
        .filter(reminder => reminder.isActive)
        .map(reminder => reminder.time);

      this.reminderCache = new Set(activeReminderTimes);
      this.lastCacheRefresh = now;
      
      console.log(`📋 Cached ${this.reminderCache.size} unique reminder time slots`);
    } catch (error) {
      console.error('Failed to refresh reminder cache:', error.message);
    }
  }

  private async scheduleExistingReminders(): Promise<void> {
    try {
      const activeReminders = await this.reminderService.getAllReminders();
      const filteredReminders = activeReminders.filter(reminder => reminder.isActive);
      console.log(`Scheduled ${filteredReminders.length} active reminders...`);

      // Only cache the reminders, don't trigger them immediately
      // The smart scheduler will handle triggering them at the right time
    } catch (error) {
      console.error('Failed to schedule existing reminders:', error.message);
    }
  }

  private async startSmartScheduler(): Promise<void> {
    await this.scheduleNextRun();
  }

  private async scheduleNextRun(): Promise<void> {
    try {
      // Stop current job if exists
      if (this.currentJob) {
        this.currentJob.stop();
        try {
          this.schedulerRegistry.deleteCronJob('smart-reminder-scheduler');
        } catch (e) {
          // Job might not exist in registry
        }
      }

      // Calculate next interval
      const nextInterval = await this.calculateOptimalInterval();
      this.metrics.currentInterval = nextInterval;

      // Create new cron job
      const cronExpression = `*/${nextInterval} * * * *`; // Every N minutes
      this.currentJob = new CronJob(cronExpression, async () => {
        await this.executeSmartCheck();
      });

      // Register and start the job
      this.schedulerRegistry.addCronJob('smart-reminder-scheduler', this.currentJob);
      this.currentJob.start();

    } catch (error) {
      console.error('Failed to schedule next run:', error.message);
      // Fallback to default interval
      setTimeout(() => this.scheduleNextRun(), SMART_SCHEDULER_CONFIG.defaultInterval * 60 * 1000);
    }
  }

  private async executeSmartCheck(): Promise<void> {
    const startTime = Date.now();
    this.metrics.totalRuns++;
    this.metrics.lastRun = startTime;

    try {
      // Refresh cache if needed
      await this.refreshReminderCache();

      // Peak hour optimizations are disabled to avoid timezone issues
      // Reminders now run 24/7 without time-based restrictions

      // Execute reminder check
      const hadReminders = await this.checkAndSendReminders();
      
      if (hadReminders) {
        this.metrics.consecutiveEmptyChecks = 0;
        this.metrics.successfulRuns++;
      } else {
        this.metrics.consecutiveEmptyChecks++;
      }

      // Schedule next run with updated interval
      await this.scheduleNextRun();

    } catch (error) {
      this.metrics.failedRuns++;
      console.error('Smart scheduler execution failed:', error.message);
      
      // Schedule next run even on failure
      setTimeout(() => this.scheduleNextRun(), SMART_SCHEDULER_CONFIG.defaultInterval * 60 * 1000);
    }
  }

  private async checkAndSendReminders(): Promise<boolean> {
    try {
      const currentTime = new Date();
      const timeString = `${currentTime.getHours().toString().padStart(2, '0')}:${currentTime.getMinutes().toString().padStart(2, '0')}`;

      // Quick cache check first
      if (!this.reminderCache.has(timeString)) {
        return false;
      }

      // Execute reminder notifications
      await this.reminderService.sendReminderNotifications();
      return true;
    } catch (error) {
      console.error('Failed to check and send reminders:', error.message);
      return false;
    }
  }

  private async calculateOptimalInterval(): Promise<number> {
    try {
      // Get server metrics
      const serverMetrics = await this.getServerMetrics();

      // Base interval from server load
      let interval = this.getIntervalFromServerLoad(serverMetrics);

      // Apply peak hour adjustments
      const peakAdjustment = this.getPeakHourAdjustment(new Date());
      interval = Math.round(interval * peakAdjustment.frequencyMultiplier);

      // Apply empty check optimization
      if (this.metrics.consecutiveEmptyChecks >= SMART_SCHEDULER_CONFIG.emptyCheckThreshold) {
        interval = Math.round(interval * SMART_SCHEDULER_CONFIG.emptyCheckMultiplier);
      }

      // Ensure interval is within bounds
      interval = Math.max(SMART_SCHEDULER_CONFIG.minInterval,
                         Math.min(SMART_SCHEDULER_CONFIG.maxInterval, interval));

      return interval;
    } catch (error) {
      console.error('Failed to calculate optimal interval:', error.message);
      return SMART_SCHEDULER_CONFIG.defaultInterval;
    }
  }

  private async getServerMetrics(): Promise<ServerMetrics> {
    try {
      const memUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();

      // Calculate CPU percentage (simplified)
      const cpuPercent = (cpuUsage.user + cpuUsage.system) / 1000000; // Convert to seconds

      // Calculate memory percentage
      const memPercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;

      return {
        cpuUsage: Math.min(100, cpuPercent * 10), // Rough approximation
        memoryUsage: memPercent,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Failed to get server metrics:', error.message);
      return {
        cpuUsage: 50, // Default values
        memoryUsage: 50,
        timestamp: Date.now()
      };
    }
  }

  private getIntervalFromServerLoad(metrics: ServerMetrics): number {
    const config = SMART_SCHEDULER_CONFIG.serverLoad;

    if (metrics.cpuUsage >= config.high.cpu || metrics.memoryUsage >= config.high.memory) {
      return config.high.interval;
    } else if (metrics.cpuUsage >= config.medium.cpu || metrics.memoryUsage >= config.medium.memory) {
      return config.medium.interval;
    } else {
      return config.low.interval;
    }
  }

  private getPeakHourAdjustment(now: Date): { skip: boolean; frequencyMultiplier: number } {
    const config = SMART_SCHEDULER_CONFIG.peakHours;

    // Peak hour optimizations are disabled to avoid timezone issues
    if (!config.enabled) {
      return {
        skip: false,
        frequencyMultiplier: 1.0 // Consistent frequency 24/7
      };
    }

    // Legacy peak hour logic (not used when disabled)
    const hour = now.getHours();
    const isMorningPeak = hour >= config.morning.start && hour < config.morning.end;
    const isEveningPeak = hour >= config.evening.start && hour < config.evening.end;

    if (isMorningPeak || isEveningPeak) {
      return {
        skip: false,
        frequencyMultiplier: 0.8 // Slightly more frequent during peak
      };
    }

    // Regular hours
    return {
      skip: false,
      frequencyMultiplier: 1.0
    };
  }



  // Public method to get current metrics for monitoring
  public getMetrics(): SchedulerMetrics {
    return { ...this.metrics };
  }

  // Public method to force cache refresh
  public async forceCacheRefresh(): Promise<void> {
    this.lastCacheRefresh = 0;
    await this.refreshReminderCache();
  }
}
