import { Injectable, OnModuleInit } from '@nestjs/common';
import { Cron, SchedulerRegistry } from '@nestjs/schedule';
import { CronJob } from 'cron';
import { ReminderService } from './reminder.service';
import { ReminderRepository } from '../repositories/reminder.repository';
import { SMART_SCHEDULER_CONFIG } from '../../../config/smart-scheduler.config';

interface ScheduleConfig {
  interval: number; // minutes
  reason: string;
  isActive: boolean;
}

interface ServerLoadMetrics {
  cpuUsage: number;
  memoryUsage: number;
  lastUpdated: Date;
}

@Injectable()
export class SmartReminderSchedulerService implements OnModuleInit {
  private currentSchedule: ScheduleConfig = {
    interval: SMART_SCHEDULER_CONFIG.defaultInterval,
    reason: 'Initial optimized schedule',
    isActive: true
  };

  private serverLoad: ServerLoadMetrics = {
    cpuUsage: 0,
    memoryUsage: 0,
    lastUpdated: new Date()
  };

  private reminderCache: Map<string, boolean> = new Map(); // Cache for time slots with reminders
  private lastReminderCheck: Date = new Date();
  private consecutiveEmptyChecks: number = 0;

  constructor(
    private readonly reminderService: ReminderService,
    private readonly reminderRepository: ReminderRepository,
    private readonly schedulerRegistry: SchedulerRegistry
  ) {}

  async onModuleInit() {
    console.log('🚀 Smart Reminder Scheduler initializing...');
    
    // Initialize reminder cache
    await this.buildReminderCache();
    
    // Start with optimized default schedule
    await this.scheduleAllExistingReminders();
    
    // Start the dynamic scheduler
    this.startDynamicScheduler();
    
    console.log(`✅ Smart Reminder Scheduler started with ${this.currentSchedule.interval}-minute intervals`);
  }

  /**
   * Build cache of time slots that have active reminders
   * This prevents unnecessary database queries
   */
  private async buildReminderCache(): Promise<void> {
    try {
      const allReminders = await this.reminderRepository.findAllReminders();
      const activeReminders = allReminders.filter(r => r.isActive);
      
      this.reminderCache.clear();
      
      // Cache all unique time slots that have reminders
      activeReminders.forEach(reminder => {
        this.reminderCache.set(reminder.time, true);
      });
      
      console.log(`📋 Cached ${this.reminderCache.size} unique reminder time slots`);
    } catch (error) {
      console.error('Failed to build reminder cache:', error.message);
    }
  }

  /**
   * Dynamic scheduler that adjusts frequency based on multiple factors
   */
  private startDynamicScheduler(): void {
    // Main dynamic cron job - starts with configured default interval
    const cronExpression = `*/${SMART_SCHEDULER_CONFIG.defaultInterval} * * * *`;
    const dynamicJob = new CronJob(cronExpression, async () => {
      await this.intelligentReminderCheck();
    });

    this.schedulerRegistry.addCronJob('smart-reminder-check', dynamicJob);
    dynamicJob.start();

    // Server load monitoring - configurable interval
    const loadMonitorExpression = `*/${SMART_SCHEDULER_CONFIG.loadMonitorInterval} * * * *`;
    const loadMonitorJob = new CronJob(loadMonitorExpression, async () => {
      await this.updateServerLoadMetrics();
      await this.adjustScheduleBasedOnLoad();
    });

    this.schedulerRegistry.addCronJob('server-load-monitor', loadMonitorJob);
    loadMonitorJob.start();

    // Cache refresh - configurable interval
    const cacheRefreshExpression = `*/${SMART_SCHEDULER_CONFIG.cacheRefreshInterval} * * * *`;
    const cacheRefreshJob = new CronJob(cacheRefreshExpression, async () => {
      await this.buildReminderCache();
    });

    this.schedulerRegistry.addCronJob('reminder-cache-refresh', cacheRefreshJob);
    cacheRefreshJob.start();
  }

  /**
   * Intelligent reminder checking with multiple optimization strategies
   */
  private async intelligentReminderCheck(): Promise<void> {
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    // Strategy 1: Check cache first to avoid unnecessary DB queries
    if (!this.shouldCheckReminders(currentTime)) {
      console.log(`⏭️ Skipping reminder check for ${currentTime} - no reminders cached for this time`);
      this.consecutiveEmptyChecks++;
      await this.adjustScheduleForEmptyChecks();
      return;
    }

    // Strategy 2: Peak/Off-peak hour optimization
    const scheduleAdjustment = this.getPeakHourAdjustment(now);
    if (scheduleAdjustment.skip) {
      console.log(`😴 Skipping reminder check during off-peak hours (${currentTime})`);
      return;
    }

    // Strategy 3: Server load consideration
    if (this.isServerOverloaded()) {
      console.log(`🔥 Skipping reminder check due to high server load`);
      return;
    }

    // Proceed with reminder check
    console.log(`🔍 Smart reminder check at ${currentTime}...`);
    await this.reminderService.sendReminderNotifications();
    
    this.consecutiveEmptyChecks = 0;
    this.lastReminderCheck = now;
  }

  /**
   * Check if we should process reminders for the current time
   */
  private shouldCheckReminders(currentTime: string): boolean {
    // Check if this exact time has reminders
    if (this.reminderCache.has(currentTime)) {
      return true;
    }

    // Check nearby time slots (±2 minutes) for flexibility
    const [hours, minutes] = currentTime.split(':').map(Number);
    
    for (let offset = -2; offset <= 2; offset++) {
      const checkMinutes = minutes + offset;
      const adjustedHours = hours + Math.floor(checkMinutes / 60);
      const adjustedMinutes = ((checkMinutes % 60) + 60) % 60;
      
      if (adjustedHours >= 0 && adjustedHours < 24) {
        const checkTime = `${adjustedHours.toString().padStart(2, '0')}:${adjustedMinutes.toString().padStart(2, '0')}`;
        if (this.reminderCache.has(checkTime)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Get peak hour adjustments for scheduling
   */
  private getPeakHourAdjustment(now: Date): { skip: boolean; frequencyMultiplier: number } {
    const hour = now.getHours();
    const config = SMART_SCHEDULER_CONFIG.peakHours;

    // Peak hours: configurable morning and evening peaks
    const isMorningPeak = hour >= config.morning.start && hour <= config.morning.end;
    const isEveningPeak = hour >= config.evening.start && hour <= config.evening.end;
    const isPeakHour = isMorningPeak || isEveningPeak;

    // Off-peak hours: configurable off-peak period
    const isOffPeak = hour >= config.offPeak.start || hour <= config.offPeak.end;

    if (isOffPeak) {
      return {
        skip: Math.random() > (1 - config.offPeak.skipProbability),
        frequencyMultiplier: 0.3
      };
    }

    if (isPeakHour) {
      return { skip: false, frequencyMultiplier: 1.5 }; // More frequent during peak
    }

    return { skip: false, frequencyMultiplier: 1.0 }; // Normal frequency
  }

  /**
   * Update server load metrics
   */
  private async updateServerLoadMetrics(): Promise<void> {
    try {
      // Get memory usage
      const memUsage = process.memoryUsage();
      const memoryUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
      
      // Simple CPU usage estimation (this is basic - in production you might want more sophisticated monitoring)
      const cpuUsage = process.cpuUsage();
      const cpuPercent = (cpuUsage.user + cpuUsage.system) / 1000000; // Convert to percentage approximation
      
      this.serverLoad = {
        cpuUsage: Math.min(cpuPercent, 100),
        memoryUsage: memoryUsagePercent,
        lastUpdated: new Date()
      };
      
    } catch (error) {
      console.error('Failed to update server load metrics:', error.message);
    }
  }

  /**
   * Check if server is overloaded
   */
  private isServerOverloaded(): boolean {
    const { cpuUsage, memoryUsage } = this.serverLoad;
    const config = SMART_SCHEDULER_CONFIG.serverLoad.high;

    // Consider server overloaded based on configured thresholds
    return cpuUsage > config.cpu || memoryUsage > config.memory;
  }

  /**
   * Adjust schedule based on server load
   */
  private async adjustScheduleBasedOnLoad(): Promise<void> {
    const { cpuUsage, memoryUsage } = this.serverLoad;
    const config = SMART_SCHEDULER_CONFIG.serverLoad;

    let newInterval = this.currentSchedule.interval;
    let reason = '';

    if (cpuUsage > config.high.cpu || memoryUsage > config.high.memory) {
      // High load: use configured high load interval
      newInterval = config.high.interval;
      reason = `High server load (CPU: ${cpuUsage.toFixed(1)}%, Memory: ${memoryUsage.toFixed(1)}%)`;
    } else if (cpuUsage > config.medium.cpu || memoryUsage > config.medium.memory) {
      // Medium load: use configured medium load interval
      newInterval = config.medium.interval;
      reason = `Medium server load (CPU: ${cpuUsage.toFixed(1)}%, Memory: ${memoryUsage.toFixed(1)}%)`;
    } else if (cpuUsage < config.low.cpu && memoryUsage < config.low.memory) {
      // Low load: use configured low load interval
      newInterval = config.low.interval;
      reason = `Low server load (CPU: ${cpuUsage.toFixed(1)}%, Memory: ${memoryUsage.toFixed(1)}%)`;
    } else {
      // Normal load: use default interval
      newInterval = SMART_SCHEDULER_CONFIG.defaultInterval;
      reason = `Normal server load (CPU: ${cpuUsage.toFixed(1)}%, Memory: ${memoryUsage.toFixed(1)}%)`;
    }

    if (newInterval !== this.currentSchedule.interval) {
      await this.updateScheduleInterval(newInterval, reason);
    }
  }

  /**
   * Adjust schedule based on consecutive empty checks
   */
  private async adjustScheduleForEmptyChecks(): Promise<void> {
    if (this.consecutiveEmptyChecks >= SMART_SCHEDULER_CONFIG.emptyCheckThreshold) {
      // After configured threshold of consecutive empty checks, reduce frequency
      const newInterval = Math.min(
        this.currentSchedule.interval * SMART_SCHEDULER_CONFIG.emptyCheckMultiplier,
        SMART_SCHEDULER_CONFIG.maxInterval
      );
      await this.updateScheduleInterval(newInterval, `${this.consecutiveEmptyChecks} consecutive empty checks`);
    }
  }

  /**
   * Update the schedule interval dynamically
   */
  private async updateScheduleInterval(newInterval: number, reason: string): Promise<void> {
    if (newInterval === this.currentSchedule.interval) return;
    
    try {
      // Remove existing job
      this.schedulerRegistry.deleteCronJob('smart-reminder-check');
      
      // Create new job with updated interval
      const cronExpression = `*/${newInterval} * * * *`;
      const newJob = new CronJob(cronExpression, async () => {
        await this.intelligentReminderCheck();
      });
      
      this.schedulerRegistry.addCronJob('smart-reminder-check', newJob);
      newJob.start();
      
      // Update current schedule
      const oldInterval = this.currentSchedule.interval;
      this.currentSchedule = {
        interval: newInterval,
        reason,
        isActive: true
      };
      
      console.log(`⚡ Schedule updated: ${oldInterval}min → ${newInterval}min (${reason})`);
      
    } catch (error) {
      console.error('Failed to update schedule interval:', error.message);
    }
  }

  /**
   * Load and schedule all active reminders on module initialization
   */
  async scheduleAllExistingReminders() {
    console.log('📅 Smart scheduler initializing existing reminders...');
    await this.reminderService.scheduleAllActiveReminders();
  }

  /**
   * Get current scheduler status for monitoring
   */
  getSchedulerStatus() {
    return {
      currentSchedule: this.currentSchedule,
      serverLoad: this.serverLoad,
      cacheSize: this.reminderCache.size,
      consecutiveEmptyChecks: this.consecutiveEmptyChecks,
      lastCheck: this.lastReminderCheck
    };
  }

  /**
   * Force refresh reminder cache (useful for testing or manual refresh)
   */
  async refreshReminderCache(): Promise<void> {
    await this.buildReminderCache();
    console.log('🔄 Reminder cache manually refreshed');
  }
}
