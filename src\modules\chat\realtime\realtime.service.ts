import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { WsException } from '@nestjs/websockets';
import {
  IActiveFitBuddy,
  ICreateGroupChatReq,
  ICreateOneToOneChatReq,
} from 'models';
import { Helper } from 'src/helper/helper.interface';
import { GroupChatService, OneToOneChatService } from '../services';
import { RedisDBService } from './redis/redis.db.service';
import { SocketGateway } from './socket.gateway';

@Injectable()
export class RealtimeService {
  constructor(
    private readonly oneToOneChatService: OneToOneChatService,
    private readonly groupChatService: GroupChatService,
    @Inject(forwardRef(() => RedisDBService))
    private readonly redisDBService: RedisDBService,
    @Inject(forwardRef(() => SocketGateway))
    private readonly socketGateway: SocketGateway,
    private readonly helper: Helper,
  ) {}

  async saveDirectMessageToDb(
    senderId: string,
    receiverId: string,
    body: ICreateOneToOneChatReq,
  ): Promise<void> {
    const res = await this.oneToOneChatService.create(
      senderId,
      receiverId,
      body,
    );
    if (!res.success) {
      console.log(res.message);
      throw new WsException(res.message);
    }
  }

  async saveGroupMessageToDb(
    senderId: string,
    groupId: string,
    body: ICreateGroupChatReq,
  ): Promise<void> {
    const res = await this.groupChatService.create(senderId, groupId, body);
    if (!res.success) {
      console.log(res.message);
      throw new WsException(res.message);
    }
  }

  async getActiveFitBuddies(clientId: string): Promise<IActiveFitBuddy[]> {
    const userId = await this.redisDBService.getUserIdByClientId(clientId);
    const fitBuddies =
      await this.oneToOneChatService.activeBuddyListWithShortInfo(userId);
    return fitBuddies;
  }

  async updateUserIsOnline(userId: string, isOnline: boolean): Promise<void> {
    await this.oneToOneChatService.updateUserIsOnline(userId, isOnline);
  }

  async updateIsLastSeen(
    userId: string,
    senderId: string,
    messageId: string,
  ): Promise<void> {
    await this.oneToOneChatService.updateIsLastSeen(
      userId,
      senderId,
      messageId,
    );
  }

  async joinNewMemberToGroup(userId: string, groupId: string): Promise<void> {
    await this.socketGateway.joinUserToRoom(userId, groupId);
    return;
  }

  async removeMemberFromGroup(userId: string, groupId: string): Promise<void> {
    await this.socketGateway.removeUserFromRoom(userId, groupId);
    return;
  }

  async subscribeToGroupNotification(userId: string, groupId: string) {
    const fcmToken = await this.groupChatService.getUserFcmToken(userId);
    await this.helper.fcmService.subscribeNotificationTopic(fcmToken, groupId);
  }

  async unsubscribeToGroupNotification(userId: string, groupId: string) {
    const fcmToken = await this.groupChatService.getUserFcmToken(userId);
    await this.helper.fcmService.unsubscribeNotificationTopic(
      fcmToken,
      groupId,
    );
  }

  async groupSubscribeNotification(
    fcmToken: string | string[],
    groupId: string,
  ) {
    await this.helper.fcmService.subscribeNotificationTopic(fcmToken, groupId);
  }
}
