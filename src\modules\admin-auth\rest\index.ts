import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Request } from 'express';
import { RolesGuard } from 'src/authentication/guards/auth.guard';
import { User as UserInfo } from 'src/decorators/auth.decorator';
import { User } from 'src/entity/user';
import { AdminAuthService } from '../services';
import {
  ForgotPasswordDto,
  ForgotPasswordSuccessResponseDto,
  SignInDataDto,
  SignInSuccessResponseDto,
} from './dto';
@Controller('admin-auth')
@ApiTags('Authentication API - Admin')
export class AuthController {
  constructor(private adminAuthService: AdminAuthService) {}

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Get('logout')
  async logout(@UserInfo() user: User) {
    return await this.adminAuthService.logout(user.id);
  }

  // @Post('signup')
  // @ApiResponse({
  //   description: 'Admin Sign Up Success Response',
  //   type: CreateAdminSuccessResponseDto,
  //   status: HttpStatus.CREATED,
  // })
  // @ApiOperation({ summary: 'Sign up as an admin.' })
  // async register(@Body() admin: CreateAdminDto) {
  //   return await this.adminAuthService.signUp(admin);
  // }

  @Post('sign-in')
  @ApiResponse({
    description: 'Admin Sign In Success Response',
    type: SignInSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Sign in as an admin.' })
  async signin(@Body() data: SignInDataDto) {
    return await this.adminAuthService.signIn(data);
  }

  @Post('forgot-password')
  @ApiResponse({
    description: 'Forgot Password Success Response',
    type: ForgotPasswordSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Admin forgot password.' })
  async forgotPassword(@Body() data: ForgotPasswordDto, @Req() req: Request) {
    const url = req.protocol + '://' + req.headers.host;
    return await this.adminAuthService.forgotPassword(data.username, url);
  }
}
