import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { Helper } from 'src/helper/helper.interface';
import {
  deepCasting,
  shallowCasting,
} from 'src/internal/casting/object.casting';
import {
  throwConflictErrIf,
  throwInternalErrIf,
  throwNotFoundErr,
  throwUnauthorizedErrIf,
} from 'src/internal/exception/api.exception.ext';
import {
  DUPLICATE_RATING_REQUEST,
  PROGRAM_RATING_DOES_NOT_EXIST,
  REVIEW_DELETE_FAILED,
  REVIEW_INSERTION_FAILED,
  REVIEW_UPDATE_FAILED,
  USER_DOES_NOT_OWN_THIS_RATING,
} from '../../common/const/coach.const';
import { CoachProgramRatingEntity } from '../../common/entities/coach-rating.entity';
import { ProgramProviderForUser } from '../../program/providers/user.provider';
import { SubscriptionProviderForUser } from '../../subscription/providers/user.provider';
import { UserInfoProviderForUser } from '../../user-info/providers/user.provider';
import {
  CreateProgramRatingRequestDtoForUser,
  CreateProgramRatingResponseDtoForUser,
  CreateProgramRatingSuccessResponseDtoForUser,
} from '../dtos/user-create-program-rating.dto';
import {
  GetCoachProgramRatingResponseDtoForUser,
  GetCoachProgramRatingSuccessResponseDtoForUser,
} from '../dtos/user-get-program-rating.dto';
import {
  GetCoachProgramRatingsResponseDtoForUser,
  GetCoachProgramRatingsSuccessResponseDtoForUser,
} from '../dtos/user-get-program-ratings.dto';
import {
  UpdateCoachProgramRatingRequestDtoForUser,
  UpdateCoachProgramRatingResponseDtoForUser,
  UpdateCoachProgramRatingSuccessResponseDtoForUser,
} from '../dtos/user-update-program-rating.dto';
import { CoachProgramRatingRepositoryForUser } from '../repositories/user.repository';

@Injectable()
export class CoachProgramRatingServiceForUser {
  constructor(
    private readonly repository: CoachProgramRatingRepositoryForUser,
    private readonly helper: Helper,
    @Inject(forwardRef(() => UserInfoProviderForUser))
    private readonly userInfoProvider: UserInfoProviderForUser,
    @Inject(forwardRef(() => ProgramProviderForUser))
    private readonly programProvider: ProgramProviderForUser,
    @Inject(forwardRef(() => SubscriptionProviderForUser))
    private readonly subscriptionProvider: SubscriptionProviderForUser,
  ) {}

  async createProgramRating(
    reqDto: CreateProgramRatingRequestDtoForUser,
    userId: string,
    programId: string,
  ): Promise<CreateProgramRatingSuccessResponseDtoForUser> {
    const oldRating = await this.repository.getPreviousRatingOfAnUser(
      userId,
      programId,
    );
    throwConflictErrIf(
      oldRating,
      'You have already rated this program',
      DUPLICATE_RATING_REQUEST,
    );

    // const isSubscribed = await this.subscriptionProvider.checkActiveSubscription(userId, programId);
    // throwConflictErrIf(!isSubscribed, 'You are not subscribed to this program', USER_NOT_SUBSCRIBED_TO_PROGRAM);

    const ratingInfo = shallowCasting(CoachProgramRatingEntity, reqDto);
    ratingInfo.userId = userId;
    ratingInfo.programId = programId;

    const coachProgram = await this.programProvider.handleNewReviewInsertion(
      programId,
      ratingInfo.rating,
    );
    throwInternalErrIf(
      !coachProgram,
      'Review insertion failed',
      REVIEW_INSERTION_FAILED,
    );

    const newRating = await this.repository.createProgramRating(ratingInfo);
    const responseDto = deepCasting(
      CreateProgramRatingResponseDtoForUser,
      newRating,
    );

    const { userName, profileImage } =
      await this.userInfoProvider.getUserNameAndProfileImage(userId);
    responseDto.userName = userName;
    responseDto.userImage = profileImage;

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getMultipleProgramRatings(
    programId: string,
    offset: number,
    limit: number,
  ): Promise<GetCoachProgramRatingsSuccessResponseDtoForUser> {
    const ratings = await this.repository.getProgramRatingsByProgramId(
      programId,
      offset,
      limit,
    );
    const responseDtos = ratings.map((rating) =>
      deepCasting(GetCoachProgramRatingsResponseDtoForUser, rating),
    );
    const userNameAndImages =
      await this.userInfoProvider.getUserNamesAndProfileImages(
        ratings.map((rating) => rating.userId),
      );
    responseDtos.forEach((dto, index) => {
      dto.userName = userNameAndImages[index].userName;
      dto.userImage = userNameAndImages[index].profileImage;
    });
    return this.helper.serviceResponse.successResponse(responseDtos);
  }

  async getSingleProgramRating(
    ratingId: string,
  ): Promise<GetCoachProgramRatingSuccessResponseDtoForUser> {
    const rating = await this.repository.getProgramRating(ratingId);
    throwNotFoundErr(
      !rating,
      'Program rating does not exist',
      PROGRAM_RATING_DOES_NOT_EXIST,
    );

    const responseDto = deepCasting(
      GetCoachProgramRatingResponseDtoForUser,
      rating,
    );

    const { userName, profileImage } =
      await this.userInfoProvider.getUserNameAndProfileImage(rating.userId);
    responseDto.userName = userName;
    responseDto.userImage = profileImage;

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async updateProgramRating(
    ratingId: string,
    updateDto: UpdateCoachProgramRatingRequestDtoForUser,
    userId: string,
  ): Promise<UpdateCoachProgramRatingSuccessResponseDtoForUser> {
    const oldRating = await this.repository.getProgramRating(ratingId);
    throwNotFoundErr(
      !oldRating,
      'Program rating does not exist',
      PROGRAM_RATING_DOES_NOT_EXIST,
    );

    const invalidOwnership = oldRating.userId !== userId;
    throwUnauthorizedErrIf(
      invalidOwnership,
      'You are not allowed to update this rating',
      USER_DOES_NOT_OWN_THIS_RATING,
    );

    const coachProgram = await this.programProvider.handleOldReviewUpdate(
      oldRating.programId,
      oldRating.rating,
      updateDto.rating,
    );
    throwInternalErrIf(
      !coachProgram,
      'Review update failed',
      REVIEW_UPDATE_FAILED,
    );

    const newRatingInfo = shallowCasting(CoachProgramRatingEntity, updateDto);
    const updatedRating = await this.repository.updateProgramRating(
      ratingId,
      newRatingInfo,
    );
    const responseDto = deepCasting(
      UpdateCoachProgramRatingResponseDtoForUser,
      updatedRating,
    );

    const { userName, profileImage } =
      await this.userInfoProvider.getUserNameAndProfileImage(userId);
    responseDto.userName = userName;
    responseDto.userImage = profileImage;

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async deleteProgramRating(
    ratingId: string,
    userId: string,
  ): Promise<GetCoachProgramRatingSuccessResponseDtoForUser> {
    const oldRating = await this.repository.getProgramRating(ratingId);
    throwNotFoundErr(
      !oldRating,
      'Program rating does not exist',
      PROGRAM_RATING_DOES_NOT_EXIST,
    );

    const invalidOwnership = oldRating.userId !== userId;
    throwUnauthorizedErrIf(
      invalidOwnership,
      'You are not allowed to delete this rating',
      USER_DOES_NOT_OWN_THIS_RATING,
    );

    const coachProgram = await this.programProvider.handleOldReviewDeletion(
      oldRating.programId,
      oldRating.rating,
    );
    throwInternalErrIf(
      !coachProgram,
      'Review deletion failed',
      REVIEW_DELETE_FAILED,
    );

    const deletedRating = await this.repository.deleteProgramRating(ratingId);
    const responseDto = deepCasting(
      GetCoachProgramRatingResponseDtoForUser,
      deletedRating,
    );

    const { userName, profileImage } =
      await this.userInfoProvider.getUserNameAndProfileImage(userId);
    responseDto.userName = userName;
    responseDto.userImage = profileImage;

    return this.helper.serviceResponse.successResponse(responseDto);
  }
}
