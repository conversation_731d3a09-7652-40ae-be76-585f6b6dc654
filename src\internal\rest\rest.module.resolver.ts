import { AboutModule } from 'src/modules/about/about.rest.module';
import { AccountingModule } from 'src/modules/accounting/accounting.rest.module';
import { AdminAuthModule } from 'src/modules/admin-auth/auth.rest.module';
import { AdminModule } from 'src/modules/admin/admin.rest.module';
import { BaseExcerciseModule } from 'src/modules/base-exercise/baseExercise.module';
import { BlogModule } from 'src/modules/blog/blog.rest.module';
import { BodyBuildingProgramModule } from 'src/modules/body-building-program/bodyProgram.rest.module';
import { BrandModule } from 'src/modules/brands/brand.rest.module';
import { CartModule } from 'src/modules/cart/cart.rest.module';
import { CategoryModule } from 'src/modules/category/category.rest.module';
import { ChallengeModule } from 'src/modules/challenge/challenge.module';
import { ChatModule } from 'src/modules/chat/chat.module';
import { ChatbotModule } from 'src/modules/chatbot/chatbot.module';
import { ClubModule } from 'src/modules/club/club.rest.module';
import { CoachModule } from 'src/modules/coach/coach.rest.module';
import { CompareModule } from 'src/modules/compare/compare.rest.module';
import { DashboardModule } from 'src/modules/dashboard/dashboard.rest.module';
import { DietModule } from 'src/modules/diet/diet.rest.module';
import { EventModule } from 'src/modules/event/event.rest.module';
import { SearchModule } from 'src/modules/global-search/search.module';
import { ManufacturerModule } from 'src/modules/manufacturer/manufacturer.rest.module';
import { MediaModule } from 'src/modules/media/media.rest.module';
import { MuscleGroupModule } from 'src/modules/muscle-group/trainingCategory.rest.module';
import { NoteModule } from 'src/modules/note/note.rest.module';
import { NotificationModule } from 'src/modules/notification/notification.module';
import { OrderModule } from 'src/modules/order/order.rest.module';
import { PackageModule } from 'src/modules/package/package.module';
import { PaymentModule } from 'src/modules/payment/payment.module';
import { PointRedemptionModule } from 'src/modules/point-redemption/point-redemption.module';
import { PollModule } from 'src/modules/poll/poll.rest.module';
import { PostModule } from 'src/modules/post/post.rest.module';
import { PreferenceModule } from 'src/modules/preference/preference.rest.module';
import { ProductModule } from 'src/modules/product/product.rest.module';
import { ReelsModule } from 'src/modules/reels/reels.module';
import { ReferralSystemModule } from 'src/modules/referral-system/referral-system.rest.module';
import { ReminderModule } from 'src/modules/reminder/reminder.module';
import { ShippingModule } from 'src/modules/shipping/shipping.module';
import { SpotModule } from 'src/modules/spot/spot.rest.module';
import { StoryModule } from 'src/modules/story/story.rest.module';
import { SubscriberModule } from 'src/modules/subscriber/subcriber.module';
import { TagsModule } from 'src/modules/tags/tags.rest.module';
import { TaskScheduleModule } from 'src/modules/task-scheduler/task.schedule.module';
import { TaskModule } from 'src/modules/task/task.module';
import { TrainingCategoryModule } from 'src/modules/training-category/trainingCategory.rest.module';
import { TrainingModule } from 'src/modules/training/training.rest.module';
import { UserActivityStreakModule } from 'src/modules/user-activity-streak/user-activity-streak.module';
import { UserAuthModule } from 'src/modules/user-auth/auth.rest.module';
import { UserPointsModule } from 'src/modules/user-points/user-points.module';
import { UserModule } from 'src/modules/user/user.rest.module';
import { VersionModule } from 'src/modules/version/version.rest.module';
import { WishListModule } from 'src/modules/wishlist/wishlist.rest.module';

export const ResolveRestModule = () => {
  return [
    UserAuthModule,
    UserModule,
    AdminAuthModule,
    AdminModule,
    SpotModule,
    BrandModule,
    TagsModule,
    ManufacturerModule,
    CategoryModule,
    MediaModule,
    ProductModule,
    CartModule,
    CompareModule,
    WishListModule,
    OrderModule,
    NotificationModule,
    ClubModule,
    StoryModule,
    NoteModule,
    PostModule,
    PreferenceModule,
    ChatModule,
    AboutModule,
    SearchModule,
    BaseExcerciseModule,
    ChallengeModule,
    DashboardModule,
    DietModule,
    TrainingModule,
    TrainingCategoryModule,
    MuscleGroupModule,
    BodyBuildingProgramModule,
    PaymentModule,
    UserPointsModule,
    ShippingModule,
    SubscriberModule,
    BlogModule,
    PackageModule,
    TaskScheduleModule,
    ChatbotModule,
    ReelsModule,
    EventModule,
    CoachModule,
    PollModule,
    AccountingModule,
    ReferralSystemModule,
    PointRedemptionModule,
    UserActivityStreakModule,
    VersionModule,
    ReminderModule,
    TaskModule,
  ];
};
