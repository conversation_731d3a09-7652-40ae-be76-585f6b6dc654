import { randomUUID } from 'crypto';
import { IUserChallengeStatusEnum } from 'models';
import { model, Schema } from 'mongoose';
import { UserChallenge } from 'src/entity/userChallenge';

const UserChallengeSchema = new Schema<UserChallenge>(
  {
    id: {
      type: String,
      unique: true,
      default: () => randomUUID(),
    },
    userId: {
      type: String,
      index: true,
    },
    challengeId: {
      type: String,
    },
    points: {
      type: Number,
      default: 0,
    },
    videoUrl: {
      type: String,
      default: null,
    },
    challengeName: {
      type: String,
      default: '',
    },
    lastStatus: {
      type: String,
      enum: IUserChallengeStatusEnum,
      default: IUserChallengeStatusEnum.IN_PROGRESS,
    },
    expireAt: {
      type: Date,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

const UserChallengeModel = model<UserChallenge>(
  'user-challenge',
  UserChallengeSchema,
);
export { UserChallengeModel };

