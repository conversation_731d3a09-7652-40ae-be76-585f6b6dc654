import { Injectable, OnModuleInit } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { ReminderService } from './reminder.service';
import { SmartReminderSchedulerService } from './smart-reminder-scheduler.service';
import { SMART_SCHEDULER_CONFIG } from '../../../config/smart-scheduler.config';

@Injectable()
export class ReminderSchedulerService implements OnModuleInit {
  constructor(
    private readonly reminderService: ReminderService,
    private readonly smartScheduler: SmartReminderSchedulerService
  ) {}

  onModuleInit() {
    // The smart scheduler will handle initialization
    console.log('🔄 Legacy reminder scheduler delegating to smart scheduler...');
  }

  /**
   * Fallback cron job that runs every 30 minutes as a safety net
   * Reduced from 5 minutes to 30 minutes since smart scheduler handles primary scheduling
   * This acts as a safety net in case of smart scheduler failures
   */
  @Cron(`*/${SMART_SCHEDULER_CONFIG.fallbackCheckInterval} * * * *`) // Configurable fallback interval
  async checkMissedReminders() {
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    console.log(`🛡️ [FALLBACK-CRON] Safety check for missed reminders at ${currentTime}...`);

    // Only run if smart scheduler is not active or has issues
    const smartStatus = this.smartScheduler.getSchedulerStatus();
    const timeSinceLastCheck = Date.now() - smartStatus.lastCheck.getTime();
    const maxAllowedGap = SMART_SCHEDULER_CONFIG.maxAllowedGap * 60 * 1000; // Convert to milliseconds

    if (timeSinceLastCheck > maxAllowedGap) {
      console.log(`⚠️ [FALLBACK-CRON] Smart scheduler gap detected (${Math.round(timeSinceLastCheck / 60000)}min), running fallback check`);
      await this.reminderService.sendReminderNotifications();
    } else {
      console.log(`✅ [FALLBACK-CRON] Smart scheduler is active, skipping fallback check`);
    }
  }

  /**
   * Emergency manual trigger for reminders (useful for testing or manual intervention)
   */
  async manualReminderCheck(): Promise<void> {
    console.log('🔧 [MANUAL-TRIGGER] Manual reminder check initiated...');
    await this.reminderService.sendReminderNotifications();
  }
}
