import { Injectable, OnModuleInit } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ReminderService } from './reminder.service';
import { SmartReminderSchedulerService } from './smart-reminder-scheduler.service';

@Injectable()
export class ReminderSchedulerService implements OnModuleInit {
  private lastSmartSchedulerRun = 0;
  private readonly FALLBACK_GAP_THRESHOLD = 75 * 60 * 1000; // 75 minutes in milliseconds

  constructor(
    private readonly reminderService: ReminderService,
    private readonly smartSchedulerService: SmartReminderSchedulerService,
  ) {}

  onModuleInit() {

    // The smart scheduler will handle initialization
  }

  /**
   * Fallback cron job that runs every 30 minutes to check for any missed reminders
   * This acts as a safety net in case the smart scheduler fails
   */
  @Cron('*/30 * * * *') // Every 30 minutes
  async checkMissedReminders() {
    try {
      const smartMetrics = this.smartSchedulerService.getMetrics();
      const now = Date.now();
      const timeSinceLastRun = now - smartMetrics.lastRun;

      // Only run fallback if smart scheduler hasn't run in the threshold time
      if (timeSinceLastRun > this.FALLBACK_GAP_THRESHOLD) {
        console.log(`⚠️ Smart scheduler gap detected (${Math.round(timeSinceLastRun / 60000)} minutes). Running fallback check...`);
        await this.reminderService.sendReminderNotifications();
      }
    } catch (error) {
      console.error('Fallback reminder check failed:', error.message);
      // Run the check anyway as a last resort
      await this.reminderService.sendReminderNotifications();
    }
  }
}
