import { Injectable } from '@nestjs/common';
import {
  IFcmService,
  IndividualFcmRequest,
  TopicFcmRequest,
  PushPriority,
} from './fcm.service.interface';
import * as admin from 'firebase-admin';
import { fireBaseConfig } from 'config/fcm';

@Injectable()
export class FcmService implements IFcmService {
  constructor() {
    !admin.apps.length &&
      admin.initializeApp({
        credential: admin.credential.cert(
          JSON.parse(JSON.stringify(fireBaseConfig)),
        ),
      });
  }

  async sendToIndividual({
    token,
    title,
    body,
    documentId,
    data,
    isHighPriority,
    ttl,
  }: IndividualFcmRequest): Promise<boolean> {
    try {
      const payload = {
        notification: {
          title,
          body,
        },
        data: {
          ...data,
          documentId,
          title,
          body,
        },
        android: {
          // notification: {
          //   title,
          //   body,
          // },
          // Priority of the message. Must be either `normal` or `high`.
          priority: isHighPriority ? PushPriority.HIGH : PushPriority.NORMAL,
          collapseKey: '',
          ttl, // Time-to-live duration of the message in seconds.
        },
        apns: {
          headers: {
            'apns-expiration': ((new Date().getTime() + ttl) / 1000).toFixed(),
            'apns-priority': isHighPriority ? '10' : '5',
          },
        },
        token,
      };
      await admin.messaging().send(payload);
      return true;
    } catch (error) {
      return false;
    }
  }

  async sendToMany(
    tokens: string[],
    title: string,
    body: string,
    documentId: string,
    data: object,
    isHighPriority: boolean,
    ttl: number,
  ): Promise<boolean> {
    try {
      const payload = {
        notification: {
          title,
          body,
        },
        data: {
          ...data,
          documentId,
        },
        android: {
          notification: {
            title,
            body,
          },
          // Priority of the message. Must be either `normal` or `high`.
          priority: isHighPriority ? PushPriority.HIGH : PushPriority.NORMAL,
          collapseKey: '',
          ttl, // Time-to-live duration of the message in seconds.
        },
        apns: {
          headers: {
            'apns-expiration': ((new Date().getTime() + ttl) / 1000).toFixed(),
            'apns-priority': isHighPriority ? '10' : '5',
          },
        },
        tokens,
      };
      await admin.messaging().sendMulticast(payload);
      return true;
    } catch (error) {
      console.error('FCM sendToMany error:', error?.message || error);
      return false;
    }
  }

  async sendToTopic({
    title,
    body,
    documentId,
    data,
    topic,
    isHighPriority = true,
    ttl = 3600,
  }: TopicFcmRequest): Promise<string> {
    try {
      // Convert all data values to strings as FCM requires
      const stringifiedData = {};
      if (data) {
        Object.keys(data).forEach(key => {
          stringifiedData[key] = typeof data[key] === 'string' ? data[key] : JSON.stringify(data[key]);
        });
      }

      const payload = {
        // Include notification object to ensure it appears in notification tray
        notification: {
          title,
          body,
        },
        // Include data for custom handling when app is opened
        data: {
          ...stringifiedData,
          documentId: documentId || '',
          title: title || '',
          body: body || '',
          type: 'reminder', // Add type for client-side handling
        },
        // Android-specific configuration
        android: {
          notification: {
            title,
            body,
            icon: 'ic_notification', // Default notification icon
            color: '#FF6B35', // Brand color for notification
            sound: 'default',
            channelId: 'reminders', // Notification channel for reminders
            priority: isHighPriority ? 'high' : 'default' as 'high' | 'default' | 'min' | 'low' | 'max',
            defaultSound: true,
            defaultVibrateTimings: true,
            defaultLightSettings: true,
          },
          priority: isHighPriority ? PushPriority.HIGH : PushPriority.NORMAL,
          ttl: ttl * 1000, // Convert to milliseconds
        },
        // iOS-specific configuration
        apns: {
          headers: {
            'apns-expiration': ((new Date().getTime() + (ttl * 1000)) / 1000).toFixed(),
            'apns-priority': isHighPriority ? '10' : '5',
          },
          payload: {
            aps: {
              alert: {
                title,
                body,
              },
              sound: 'default',
              badge: 1,
              'content-available': 1, // Enable background processing
            },
          },
        },
        topic,
      };

      const result = await admin.messaging().send(payload);
      console.log(`✅ [FCM-TOPIC] Successfully sent notification to topic '${topic}': ${result}`);
      return result;
    } catch (error) {
      console.error(`❌ [FCM-TOPIC] Failed to send to topic '${topic}':`, error?.message || error);
      throw error;
    }
  }

  async subscribeNotificationTopic(tokens: string | string[], topic: string) {
    try {
      await admin.messaging().subscribeToTopic(tokens, topic);
      // Subscription successful
    } catch (error) {
      console.error('FCM subscribeNotificationTopic error:', error?.message || error);
      return;
    }
  }

  async unsubscribeNotificationTopic(tokens: string | string[], topic: string) {
    try {
      await admin
        .messaging()
        .unsubscribeFromTopic(tokens, topic);
      // Unsubscription successful
    } catch (error) {
      console.error('FCM unsubscribeNotificationTopic error:', error?.message || error);
      return;
    }
  }
}
