import { Injectable } from '@nestjs/common';
import {
  IFcmService,
  IndividualFcmRequest,
  PushPriority,
} from './fcm.service.interface';
import * as admin from 'firebase-admin';
import { fireBaseConfig } from 'config/fcm';

@Injectable()
export class FcmService implements IFcmService {
  constructor() {
    !admin.apps.length &&
      admin.initializeApp({
        credential: admin.credential.cert(
          JSON.parse(JSON.stringify(fireBaseConfig)),
        ),
      });
  }

  async sendToIndividual({
    token,
    title,
    body,
    documentId,
    data,
    isHighPriority,
    ttl,
  }: IndividualFcmRequest): Promise<boolean> {
    try {
      const payload = {
        notification: {
          title,
          body,
        },
        data: {
          ...data,
          documentId,
          title,
          body,
        },
        android: {
          // notification: {
          //   title,
          //   body,
          // },
          // Priority of the message. Must be either `normal` or `high`.
          priority: isHighPriority ? PushPriority.HIGH : PushPriority.NORMAL,
          collapseKey: '',
          ttl, // Time-to-live duration of the message in seconds.
        },
        apns: {
          headers: {
            'apns-expiration': ((new Date().getTime() + ttl) / 1000).toFixed(),
            'apns-priority': isHighPriority ? '10' : '5',
          },
        },
        token,
      };
      await admin.messaging().send(payload);
      return true;
    } catch (error) {
      return false;
    }
  }

  async sendToMany(
    tokens: string[],
    title: string,
    body: string,
    documentId: string,
    data: object,
    isHighPriority: boolean,
    ttl: number,
  ): Promise<boolean> {
    try {
      const payload = {
        notification: {
          title,
          body,
        },
        data: {
          ...data,
          documentId,
        },
        android: {
          notification: {
            title,
            body,
          },
          // Priority of the message. Must be either `normal` or `high`.
          priority: isHighPriority ? PushPriority.HIGH : PushPriority.NORMAL,
          collapseKey: '',
          ttl, // Time-to-live duration of the message in seconds.
        },
        apns: {
          headers: {
            'apns-expiration': ((new Date().getTime() + ttl) / 1000).toFixed(),
            'apns-priority': isHighPriority ? '10' : '5',
          },
        },
        tokens,
      };
      await admin.messaging().sendMulticast(payload);
      return true;
    } catch (error) {
      console.error('FCM sendToMany error:', error?.message || error);
      return false;
    }
  }

  async sendToTopic({
    title,
    body,
    documentId,
    data,
    topic,
    isHighPriority = true,
    ttl = 600,
  }): Promise<string> {
    try {
      const payload = {
        notification: {
          title,
          body,
        },
        data: {
          ...data,
          documentId,
          title, // Include in data for app handling
          body,  // Include in data for app handling
          click_action: 'FLUTTER_NOTIFICATION_CLICK', // For Flutter apps
        },
        android: {
          notification: {
            title,
            body,
            icon: 'ic_notification',
            sound: 'default',
            channelId: 'reminders', // Specific channel for reminders
            priority: 'high' as const,
            defaultSound: true,
            defaultVibrateTimings: true,
            defaultLightSettings: true,
            clickAction: 'FLUTTER_NOTIFICATION_CLICK',
          },
          priority: isHighPriority ? 'high' as const : 'normal' as const,
          ttl: ttl * 1000, // Time-to-live duration in milliseconds
        },
        apns: {
          headers: {
            'apns-expiration': ((new Date().getTime() + ttl * 1000) / 1000).toFixed(),
            'apns-priority': isHighPriority ? '10' : '5',
          },
          payload: {
            aps: {
              alert: {
                title,
                body,
              },
              sound: 'default',
              badge: 1,
              'content-available': 1,
              category: 'REMINDER_CATEGORY',
            },
          },
        },
        webpush: {
          notification: {
            title,
            body,
            icon: '/icons/notification-icon.png',
            badge: '/icons/badge-icon.png',
            requireInteraction: true,
            actions: [
              {
                action: 'view',
                title: 'View Reminder',
              },
            ],
          },
        },
        topic,
      };

      const response = await admin.messaging().send(payload);
      return response;
    } catch (error) {
      console.error('FCM sendToTopic error:', error?.message || error);
      throw error;
    }
  }

  async subscribeNotificationTopic(tokens: string | string[], topic: string) {
    try {
      await admin.messaging().subscribeToTopic(tokens, topic);
      // Subscription successful
    } catch (error) {
      console.error('FCM subscribeNotificationTopic error:', error?.message || error);
      return;
    }
  }

  async unsubscribeNotificationTopic(tokens: string | string[], topic: string) {
    try {
      await admin
        .messaging()
        .unsubscribeFromTopic(tokens, topic);
      // Unsubscription successful
    } catch (error) {
      console.error('FCM unsubscribeNotificationTopic error:', error?.message || error);
      return;
    }
  }
}
