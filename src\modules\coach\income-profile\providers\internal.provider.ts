import { Injectable } from "@nestjs/common";
import { CoachIncomeProfileEntity } from "../../common/entities/coach-profile.entity";
import { IncomeProfileRepositoryForInternal } from "../repositories/internal.repository";

@Injectable()
export class IncomeProfileProviderForInternal {

  constructor(private readonly repository: IncomeProfileRepositoryForInternal) { }

    async onPaymentSuccessful(coachId: string, currentAccountBalance:number): Promise<CoachIncomeProfileEntity | null> {
      return await this.repository.updateIncomeProfileToCurrentAccountBalance(coachId,currentAccountBalance);
    }

    async onRefundSuccessful(coachId: string, refundAmount: number): Promise<CoachIncomeProfileEntity | null> {
      return await this.repository.updateAfterRefund(coachId, refundAmount);
    }
}