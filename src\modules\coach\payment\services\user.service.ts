import { forwardRef, Inject, Injectable } from "@nestjs/common";
import { randomUUID } from "crypto";
import { Helper } from "src/helper/helper.interface";
import { deepCasting, shallowCasting } from "src/internal/casting/object.casting";
import { throwConflictErrIf, throwNotFoundErr } from "src/internal/exception/api.exception.ext";
import { AccountingProvider } from "src/modules/accounting/account/providers/account.provider";
import { ModuleTypeEnum } from "src/modules/bkash/entities/bkash.entity";
import { BkashInternalProvider } from "src/modules/bkash/providers/internal.provider";
import { COACH_PROGRAM_NOT_FOUND, INVALID_PAYMENT_TERM, PRICE_NOT_SET_FOR_THIS_PAYMENT_TERM, SUBSCRIPTION_CANCELLED_ALREADY, SUBSCRIPTION_IS_NOT_ACTIVE, SUBSCRIPTION_NOT_FOUND, USER_DOES_NOT_OWN_THIS_SUBSCRIPTION } from "../../common/const/coach.const";
import { CoachSubscriptionPaymentEntity, CoachSubscriptionPaymentGateway, CoachSubscriptionPaymentStatus, CoachSubscriptionPaymentTerm } from "../../common/entities/coach-payment.entity";
import { ProgramProviderForUser } from "../../program/providers/user.provider";
import { SubscriptionProviderForUser } from "../../subscription/providers/user.provider";
import { CoachSubscriptionPaymentRequestDtoForUserByBkash, CoachSubscriptionPaymentResponseDtoForUserByBkash, CoachSubscriptionPaymentSuccessfulResponseDtoForUserByBkash } from "../dtos/user-payment-request.dto";
import { CoachSubscriptionPaymentRepositoryForUser } from "../repositories/user.repository";

@Injectable()
export class CoachSubscriptionPaymentServiceForUser {

  constructor(
    private readonly repository: CoachSubscriptionPaymentRepositoryForUser,
    private helper: Helper,
    @Inject(forwardRef(() => SubscriptionProviderForUser))
    private readonly subscriptionProvider: SubscriptionProviderForUser,
    @Inject(forwardRef(() => ProgramProviderForUser))
    private readonly programProvider: ProgramProviderForUser,
    @Inject(forwardRef(() => AccountingProvider))
    private readonly accountProvider: AccountingProvider,

    @Inject(forwardRef(() => BkashInternalProvider))
    private readonly bkashProvider: BkashInternalProvider,
  ) { }

  /**
   * This function is the initiation of the payment process through bKash for a subscription, we have lots of 
   * things to check before initiating the payment process.
   * 
   * 1. Check if the subscription exists or not
   * 2. Check if the user owns the subscription or not
   * 3. Check if the subscription is active or not
   * 4. Check if the subscription is already cancelled or not
   * 5. Check if the program exists or not
   * 6. Check if the payment term is available in the program or not
   * 7. If payment term exist then initiate the amount and payment process
   * 8. Get the url from bkash module and save the payment details
   * 9. Return the payment details and wait for the success or failed event
   * 
   * @param userId the user id who is making the payment
   * @param subscriptionId the subscription id for which the payment is being made
   * @param reqDto the payment request details
   * @returns the payment details
   */
  async payThroughBKash(userId: string, subscriptionId: string, reqDto: CoachSubscriptionPaymentRequestDtoForUserByBkash): Promise<CoachSubscriptionPaymentSuccessfulResponseDtoForUserByBkash> {
    const subscription = await this.subscriptionProvider.getSingleSubscription(subscriptionId);
    throwNotFoundErr(!subscription, 'Subscription not found', SUBSCRIPTION_NOT_FOUND);
    throwConflictErrIf(subscription.userId !== userId, 'User does not own this subscription', USER_DOES_NOT_OWN_THIS_SUBSCRIPTION);
    throwConflictErrIf(!subscription.isActive, 'Subscription is not active', SUBSCRIPTION_IS_NOT_ACTIVE);
    throwConflictErrIf(subscription.cancelStatus, 'Subscription is already cancelled', SUBSCRIPTION_CANCELLED_ALREADY);

    const programEntity = await this.programProvider.getSingleCoachProgram(subscription.programId);
    throwNotFoundErr(!programEntity, 'Program not found', COACH_PROGRAM_NOT_FOUND);

    let paymentEntity = shallowCasting(CoachSubscriptionPaymentEntity, reqDto);

    // before saving the payment details, we have to check whenther the payment term choosen by the user
    // is available in the program entity or not. Cause lets say user may choose to pay one time, but the
    // coach has not set any one time payment for that program. So, we have to check this.
    let correspondingAmount = 0;
    switch (reqDto.paymentTerm) {
      case CoachSubscriptionPaymentTerm.OneTime:
        throwConflictErrIf(!programEntity.oneTimePrice, 'One time payment is not available for this program', PRICE_NOT_SET_FOR_THIS_PAYMENT_TERM);
        correspondingAmount = programEntity.oneTimePrice.discountedPrice;
        break;
      case CoachSubscriptionPaymentTerm.Daily:
        throwConflictErrIf(!programEntity.dailyPrice, 'Daily payment is not available for this program', PRICE_NOT_SET_FOR_THIS_PAYMENT_TERM);
        correspondingAmount = programEntity.dailyPrice.discountedPrice;
        break;
      case CoachSubscriptionPaymentTerm.Weekly:
        throwConflictErrIf(!programEntity.weeklyPrice, 'Weekly payment is not available for this program', PRICE_NOT_SET_FOR_THIS_PAYMENT_TERM);
        correspondingAmount = programEntity.weeklyPrice.discountedPrice
        break;
      case CoachSubscriptionPaymentTerm.Monthly:
        throwConflictErrIf(!programEntity.monthlyPrice, 'Monthly payment is not available for this program', PRICE_NOT_SET_FOR_THIS_PAYMENT_TERM);
        correspondingAmount = programEntity.monthlyPrice.discountedPrice
        break;
      default:
        throwConflictErrIf(true, 'Invalid payment term', INVALID_PAYMENT_TERM);
    }
    const paymentId = randomUUID();
   
    paymentEntity.userId = userId;
    paymentEntity.subscriptionId = subscriptionId;
    paymentEntity.paymentStatus = CoachSubscriptionPaymentStatus.Triggered;
    paymentEntity.paymentAmount = correspondingAmount;
    paymentEntity.paymentDate = new Date();
    paymentEntity.paymentGateway = CoachSubscriptionPaymentGateway.BKash;

    const bkashUrl = await this.bkashProvider.createBkashPayment({
      userId,
      referenceId: paymentId ,
      amount: correspondingAmount,
      moduleType: ModuleTypeEnum.Coach,
      isSavePayment: false
    })
    paymentEntity.id = paymentId
    paymentEntity.paymentUrl = bkashUrl.paymentURL;
    paymentEntity = await this.repository.createPaymentEntity(paymentEntity);


    const responseDto = deepCasting(CoachSubscriptionPaymentResponseDtoForUserByBkash, paymentEntity);

    // await this.accountProvider.addAccount(
    //   AccountingReference.COACH,
    //   AccountingReferenceType.SUBSCRIPTION,
    //   subscriptionId, AccountingTransactionType.CREDIT,
    //   correspondingAmount)
    return this.helper.serviceResponse.successResponse(responseDto);
  }

}