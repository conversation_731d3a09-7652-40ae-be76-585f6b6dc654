import { HttpStatus, Injectable } from '@nestjs/common';
import { sharedConfig } from 'config/shared';
import {
  ChatErrorEnum,
  IActiveFitBuddy,
  ICreateOneToOneChat,
  ICreateOneToOneChatReq,
  IDeleteMessageRes,
  IOneToOneChatRes,
  IOneToOneConversationListRes,
  IOneToOneCreateChatRes,
} from 'models';
import { Helper } from 'src/helper/helper.interface';

import { APIException } from 'src/internal/exception/api.exception';
import { successResponse } from 'src/utils/response';
import { IServiceSuccessResponse } from 'src/utils/response/service.response.interface';
import { OneToOneChatRepository } from '../repositories';
import { ChatHelperService } from './chat.helper.service';

@Injectable()
export class OneToOneChatService {
  constructor(
    private readonly oneToOneChatRepository: OneToOneChatRepository,
    private readonly chatHelperService: ChatHelperService,
    private readonly helperSerice: Helper,
  ) {}

  async create(
    senderId: string,
    receiverId: string,
    body: ICreateOneToOneChatReq,
  ): Promise<IOneToOneCreateChatRes> {
    const isAllowedToSend = await this.chatHelperService.isAllowedToSend(
      senderId,
      receiverId,
    );
    if (!isAllowedToSend) {
      return this.chatHelperService.response(
        false,
        'You are not allowed to send message',
      );
    }

    const payload: ICreateOneToOneChat = {
      senderId,
      receiverId,
      ...body,
    };
    const newMessage = await this.oneToOneChatRepository.create(payload);
    if (!newMessage) {
      return this.chatHelperService.response(
        false,
        'Something went wrong in send message',
      );
    }
    const addToConversation =
      await this.chatHelperService.addToConversationHistory(
        newMessage.id,
        senderId,
        receiverId,
      );

    if (!addToConversation) {
      return this.chatHelperService.response(
        false,
        'Something went wrong in manage conversation',
      );
    }
    return this.chatHelperService.response(true, 'Send successfully');
  }

  async getMessageHistoryByUser(
    senderId: string,
    receiverId: string,
    offset = sharedConfig.oneToOneChatDefaultSkip,
    limit = sharedConfig.oneToOneChatDefaultLimit,
  ): Promise<IServiceSuccessResponse<IOneToOneChatRes[]>> {
    const chatHistory =
      await this.oneToOneChatRepository.getOneToOneMessageHistory(
        senderId,
        receiverId,
        offset,
        limit,
      );
    if (!chatHistory) {
      return successResponse(null, []);
    }

    const mapped = await Promise.all(
      chatHistory.map(async (e) => {
        if (e.type === 'image') {
          e.content =
            await this.helperSerice.azureBlobStorageService.generatePreSignedUrl(
              e.content,
              'original/chat',
            );
        }
        return e;
      }),
    );

    return successResponse(null, mapped);
  }

  // delete the target message
  // update conversation doc with immediate last message
  async deleteMessage(
    senderId: string,
    messageId: string,
  ): Promise<IServiceSuccessResponse<IDeleteMessageRes>> {
    const query = {
      senderId,
      id: messageId,
    };
    const deletedMessage =
      await this.oneToOneChatRepository.deleteSingleMessage(query);

    if (!deletedMessage) {
      throw new APIException(
        ChatErrorEnum.MESSAGE_CAN_NOT_BE_DELETED,
        'MESSAGE_CAN_NOT_BE_DELETED',
        HttpStatus.BAD_REQUEST,
      );
    }

    const updateConversation =
      await this.chatHelperService.handleConversationAfterDeleteMessage(
        deletedMessage.senderId,
        deletedMessage.receiverId,
      );
    if (!updateConversation) {
      throw new APIException(
        ChatErrorEnum.FAILED_TO_UPDATE_CONVERSATION,
        'FAILED_TO_UPDATE_CONVERSATION',
        HttpStatus.BAD_REQUEST,
      );
    }

    return successResponse(null, {
      message: 'Message has been deleted successfully',
    });
  }

  async conversationList(
    userId: string,
    offset = sharedConfig.oneToOneChatDefaultSkip,
    limit = sharedConfig.oneToOneChatDefaultLimit,
  ): Promise<IServiceSuccessResponse<IOneToOneConversationListRes[]>> {
    if (limit < 1 || limit > sharedConfig.oneToOneChatMaxLimit) {
      limit = sharedConfig.oneToOneChatDefaultLimit;
    }
    let list = await this.oneToOneChatRepository.getConversationList(
      userId,
      offset,
      limit,
    );
    if (!list) {
      return successResponse(null, []);
    }
    return successResponse(null, list);
  }

  async activeBuddyListWithShortInfo(
    userId: string,
    offset = sharedConfig.oneToOneChatDefaultSkip,
    limit = sharedConfig.oneToOneChatDefaultLimit,
  ): Promise<IActiveFitBuddy[]> {
    if (limit < 1 || limit > sharedConfig.oneToOneChatMaxLimit) {
      limit = sharedConfig.oneToOneChatDefaultLimit;
    }
    return await this.oneToOneChatRepository.activeBuddyListWithShortInfo(
      userId,
      offset,
      limit,
    );
  }

  async updateUserIsOnline(userId: string, isOnline: boolean): Promise<void> {
    await this.oneToOneChatRepository.updateUserIsOnline(userId, isOnline);
  }

  async updateIsLastSeen(
    userId: string,
    senderId: string,
    messageId: string,
  ): Promise<void> {
    await this.oneToOneChatRepository.updateIsLastSeen(
      userId,
      senderId,
      messageId,
    );
  }
}
