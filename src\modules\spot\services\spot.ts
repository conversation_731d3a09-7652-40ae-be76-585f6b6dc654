import { HttpStatus, Injectable } from '@nestjs/common';
import { redisCacheConfig } from 'config/cache';
import { sharedConfig } from 'config/shared';
import {
  BlockFitBuddyErrorMessages,
  BlockFitBuddySuccessMessages,
  BlockFitBuddySuccessResponse,
  BlockListQuery,
  BlockListResponse,
  CancelSpotRequestErrorMessages,
  CancelSpotRequestSuccessMessages,
  FindFitBuddiesListErrorMessages,
  FindFitBuddiesListQuery,
  FindUsersListErrorMessages,
  FindUsersListQuery,
  FindUsersListSuccessResponse,
  FitBuddiesStatus,
  FitBuddiesType,
  FitBuddyStatusAndType,
  FitBuddyStatusAndTypeErrorMessages,
  FitBuddyStatusAndTypeSuccessResponse,
  PackageFeatureEnum,
  RelationStatusEnum,
  SpotRequestBody,
  SpotRequestErrorMessages,
  SpotRequestListSuccessResponse,
  SpotRequestSuccessResponse,
  UnblockFitBuddyErrorMessages,
  UnblockFitBuddySuccessMessages,
  UnblockFitBuddySuccessResponse,
  UpdateSpotRequestBody,
  UpdateSpotRequestErrorMessages,
  UpdateSpotRequestSuccessResponse,
} from 'models';
import {
  UnfriendErrorMessages,
  UnFriendFitBuddySuccessMessages,
  UnFriendSuccessResponse,
} from 'models/spot/unFriend';
import { RedisCacheHelper } from 'src/cache/helper';
import { getMyFeed } from 'src/cache/post';
import { Helper } from 'src/helper/helper.interface';
import { APIException } from 'src/internal/exception/api.exception';
import { UserDailyActivityService } from 'src/modules/package/services/user.dailyActivity.service';
import { TaskProgressService } from 'src/modules/task/task-progress.service';
import { QueueInstance } from 'src/queue-system';
import {
  NotificationQueueName,
  QueuePayloadType,
} from 'src/queue-system/predefined-data';
import { SpotRepository } from '../repositories/spot';

@Injectable()
export class SpotService {
  constructor(
    private spotRepo: SpotRepository,
    private helper: Helper,
    private readonly userDailyActivityService: UserDailyActivityService,
    private readonly taskProgressService: TaskProgressService,
  ) {}

  async spotRequest(
    userId: string,
    data: SpotRequestBody,
  ): Promise<SpotRequestSuccessResponse> {
    const isBlocked = await this.spotRepo.isBlocked(userId, data.recipientId);
    if (isBlocked) {
      throw new APIException(
        SpotRequestErrorMessages.RECIPIENT_NOT_FOUND,
        'RECIPIENT_NOT_FOUND',
        HttpStatus.BAD_REQUEST,
      );
    }
    const [
      recipient,
      alreadySendSpotRequest,
      _alreadySendSpotRequest,
      requester,
    ] = await Promise.all([
      await this.spotRepo.findUser({
        $and: [{ id: data.recipientId }, { id: { $ne: userId } }],
      }),
      await this.spotRepo.findSpotRequest({
        recipientId: data.recipientId,
        requesterId: userId,
      }),
      await this.spotRepo.findSpotRequest({
        recipientId: userId,
        requesterId: data.recipientId,
      }),
      await this.spotRepo.findUser({
        id: userId,
      }),
    ]);
    if (!recipient) {
      throw new APIException(
        SpotRequestErrorMessages.RECIPIENT_NOT_FOUND,
        'RECIPIENT_NOT_FOUND',
        HttpStatus.BAD_REQUEST,
      );
    }
    if (
      alreadySendSpotRequest?.isAccepted ||
      _alreadySendSpotRequest?.isAccepted
    ) {
      throw new APIException(
        SpotRequestErrorMessages.ALREADY_SPOTTED,
        'ALREADY_SEND_SPOT_REQUEST',
        HttpStatus.BAD_REQUEST,
      );
    }
    if (alreadySendSpotRequest) {
      return this.helper.serviceResponse.successResponse(
        alreadySendSpotRequest,
      );
    }
    if (_alreadySendSpotRequest) {
      throw new APIException(
        SpotRequestErrorMessages.USER_ALREADY_SENT_YOU_SPOT_REQUEST,
        'USER_ALREADY_SENT_YOU_SPOT_REQUEST',
        HttpStatus.BAD_REQUEST,
      );
    }
    // checking subscription
    const handlePackage = await this.userDailyActivityService.verifyPackage(
      userId,
      PackageFeatureEnum.SPOT_REQUEST,
    );
    const spotReq = await this.spotRepo.spotRequest(userId, data);
    if (!spotReq)
      throw new APIException(
        SpotRequestErrorMessages.SPOT_REQUEST_FAILED,
        'SPOT_REQUEST_FAILED',
        HttpStatus.BAD_REQUEST,
      );

    try {
      if (recipient && requester) {
        // Notification Payload
        const payload = {
          targetUsers: [recipient.id],
          createdBy: {
            name: requester.name,
            userId: requester.id,
            avatar: requester?.image?.profile,
          },
          fcmToken: recipient?.fcmToken,
          payloadType: QueuePayloadType.SPOT_REQUEST_GENERATOR,
          documentId: requester.id,
        };

        // Send Notification queue
        (await QueueInstance).sendPayload(
          NotificationQueueName.SPOT_REQUEST_GENERATOR_QUEUE,
          Buffer.from(JSON.stringify(payload)),
        );
      }
    } catch (error: any) {
      console.log(error.message);
    }
    await this.taskProgressService.trackSpotRequest(userId);

    return this.helper.serviceResponse.successResponse(spotReq);
  }

  async updateSpotRequest(
    spotRequestId: string,
    userId: string,
    data: UpdateSpotRequestBody,
  ): Promise<UpdateSpotRequestSuccessResponse> {
    const doesSpotRequestExists = await this.spotRepo.findSpotRequest({
      id: spotRequestId,
      recipientId: userId,
      $or: [{ isAccepted: { $exists: false } }, { isAccepted: false }],
    });
    if (!doesSpotRequestExists)
      throw new APIException(
        UpdateSpotRequestErrorMessages.SPOT_REQUEST_NOT_FOUND,
        'SPOT_REQUEST_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );

    const { isAccepted } = data;
    const spotReq = await this.spotRepo.updateSpotRequest(
      { id: spotRequestId, recipientId: userId },
      isAccepted,
      doesSpotRequestExists.requesterId,
    );
    if (!spotReq)
      throw new APIException(
        UpdateSpotRequestErrorMessages.SPOT_REQUEST_UPDATED_FAILED,
        'SPOT_REQUEST_UPDATED_FAILED',
        HttpStatus.BAD_REQUEST,
      );

    try {
      if (isAccepted) {
        const { requesterId } = doesSpotRequestExists;
        // A parallel API call is used to obtain the recipient and requester.
        const [user, requester] = await Promise.all([
          await this.spotRepo.findUser({
            id: userId,
          }),
          await this.spotRepo.findUser({
            id: requesterId,
          }),
        ]);

        // Notification Payload
        const payload = {
          targetUsers: [requester.id],
          createdBy: {
            name: user.name,
            userId: user.id,
            avatar: user?.image?.profile,
          },
          fcmToken: requester?.fcmToken,
          payloadType: QueuePayloadType.SPOT_REQUEST_ACCEPT_GENERATOR,
          documentId: user.id,
        };
        // Send Notification queue
        (await QueueInstance).sendPayload(
          NotificationQueueName.SPOT_REQUEST_ACCEPT_GENERATOR_QUEUE,
          Buffer.from(JSON.stringify(payload)),
        );
      }
    } catch (error: any) {
      console.log(error.message);
    }
    return this.helper.serviceResponse.successResponse(spotReq);
  }

  async acceptSpotRequest(
    requesterId: string,
    userId: string,
    data: UpdateSpotRequestBody,
  ) {
    const isExist = await this.spotRepo.findSpotRequest({
      requesterId,
      recipientId: userId,
      $or: [{ isAccepted: { $exists: false } }, { isAccepted: false }],
    });
    if (!isExist)
      throw new APIException(
        UpdateSpotRequestErrorMessages.SPOT_REQUEST_NOT_FOUND,
        'SPOT_REQUEST_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    return await this.updateSpotRequest(isExist.id, userId, data);
  }

  async cancelSpotRequest(
    spotRequestId: string,
    userId: string,
  ): Promise<UpdateSpotRequestSuccessResponse> {
    const doesSpotRequestExists = await this.spotRepo.findSpotRequest({
      requesterId: userId,
      $and: [
        {
          $or: [{ id: spotRequestId }, { recipientId: spotRequestId }],
        },
        {
          $or: [{ isAccepted: { $exists: false } }, { isAccepted: false }],
        },
      ],
    });

    if (!doesSpotRequestExists) {
      throw new APIException(
        CancelSpotRequestErrorMessages.SPOT_REQUEST_NOT_FOUND,
        'SPOT_REQUEST_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }
    const cancelReq = await this.spotRepo.cancelSpotRequest(
      userId,
      spotRequestId,
      doesSpotRequestExists,
    );

    if (!cancelReq) {
      throw new APIException(
        CancelSpotRequestErrorMessages.CAN_NOT_CANCEL_REQUEST,
        'CAN_NOT_CANCEL_REQUEST',
        HttpStatus.BAD_REQUEST,
      );
    }

    return this.helper.serviceResponse.successResponse({
      message:
        CancelSpotRequestSuccessMessages.SPOT_REQUEST_CANCELLED_SUCCESSFULLY,
    });
  }

  async unFriendFitbuddy(
    userId: string,
    fitBuddyId: string,
  ): Promise<UnFriendSuccessResponse> {
    const unFriendFitBuddy = await this.spotRepo.unFriendFitBuddy(
      userId,
      fitBuddyId,
    );

    if (!unFriendFitBuddy) {
      throw new APIException(
        UnfriendErrorMessages.FITBUDDY_CAN_NOT_UNFRIEND,
        'FITBUDDY_CAN_NOT_UNFRIEND',
        HttpStatus.NOT_FOUND,
      );
    }

    return this.helper.serviceResponse.successResponse({
      message: UnFriendFitBuddySuccessMessages.FITBUDDY_UNFRIEND_SUCCESSFULLY,
    });
  }

  async spotRequestList(
    recipientId: string, // recipientId means userId
  ): Promise<SpotRequestListSuccessResponse> {
    const spotReqList = await this.spotRepo.findSpotRequestList({
      recipientId,
      isAccepted: { $exists: false },
    });

    return this.helper.serviceResponse.successResponse(
      spotReqList.length ? spotReqList : [],
    );
  }

  async fitBuddiesList(
    userId: string,
    condition: FindFitBuddiesListQuery,
  ): Promise<SpotRequestListSuccessResponse> {
    const { type, offset, limit } = condition;

    const buddy = await this.spotRepo.fitBuddiesList(
      userId,
      type,
      offset,
      limit,
    );
    if (!buddy)
      throw new APIException(
        FindFitBuddiesListErrorMessages.FITBUDDIES_NOT_FOUND,
        'FITBUDDIES_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    return this.helper.serviceResponse.successResponse(
      // await this.mappedGeneratePresignedUrl(buddy, false),
      buddy,
    );
  }

  async findUsersList(
    userListQuery: FindUsersListQuery,
    userId: string,
  ): Promise<FindUsersListSuccessResponse> {
    const { name, offset, limit } = userListQuery;
    const setLimit =
      !limit || limit > sharedConfig.defaultMaxLImit
        ? sharedConfig.defaultLimit
        : limit;
    const query: Record<string, any> = {};
    query.userId = userId;
    query.isDeleted = false;
    name !== undefined && name !== '' && (query.name = new RegExp(name, 'i'));

    const userList = await this.spotRepo.findUsersList(
      query,
      userId,
      offset,
      setLimit,
    );
    if (!userList) {
      throw new APIException(
        FindUsersListErrorMessages.USERS_NOT_FOUND,
        'USERS_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }
    return this.helper.serviceResponse.successResponse(
      // await this.mappedGeneratePresignedUrl(userList, false),
      userList,
    );
  }

  async blockFitBuddy(
    userId: string,
    fitBuddyId: string,
  ): Promise<BlockFitBuddySuccessResponse> {
    const isBlocked = await this.spotRepo.isBlocked(userId, fitBuddyId);
    if (isBlocked) {
      throw new APIException(
        BlockFitBuddyErrorMessages.ALREADY_BLOCKED,
        'ALREADY_BLOCKED',
        HttpStatus.BAD_REQUEST,
      );
    }
    const blockedFitBuddy = await this.spotRepo.blockFitBuddy({
      userId,
      fitBuddyId,
      status: FitBuddiesStatus.ACTIVE,
    });
    if (!blockedFitBuddy) {
      const blockNonFitBuddy = await this.spotRepo.blockNonFitBuddyUser(
        userId,
        fitBuddyId,
      );
      if (!blockNonFitBuddy) {
        throw new APIException(
          BlockFitBuddyErrorMessages.CAN_NOT_BLOCK_USER,
          'CAN_NOT_BLOCK',
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    // update newsfeed cache for both users (this is not a optimized solution)
    await RedisCacheHelper.deleteData(
      `${redisCacheConfig.user_feed_channel}/${userId}`,
    );
    await RedisCacheHelper.deleteData(
      `${redisCacheConfig.user_feed_channel}/${fitBuddyId}`,
    );
    await getMyFeed(userId, true);
    await getMyFeed(fitBuddyId, true);

    return this.helper.serviceResponse.successResponse({
      message: BlockFitBuddySuccessMessages.FITBUDDY_BLOCKED_SUCCESSFULLY,
    });
  }

  async unblockFitBuddy(
    userId: string,
    fitBuddyId: string,
  ): Promise<UnblockFitBuddySuccessResponse> {
    const unblockedFitBuddy = await this.spotRepo.unblockFitBuddy({
      userId,
      fitBuddyId,
      status: FitBuddiesStatus.BLOCKED,
    });

    if (!unblockedFitBuddy) {
      const unblockedNonFitBuddy = await this.spotRepo.deleteNonFitBuddyUser(
        userId,
        fitBuddyId,
      );
      if (!unblockedNonFitBuddy) {
        throw new APIException(
          UnblockFitBuddyErrorMessages.CAN_NOT_UNBLOCK_FITBUDDY,
          'CAN_NOT_UNBLOCK_FITBUDDY',
          HttpStatus.BAD_REQUEST,
        );
      }
    }
    // if (unblockedFitBuddy.type === FitBuddiesType.CONNECTED) {
    //   await this.spotRepo.deleteNonFitBuddyUser(userId, fitBuddyId);
    // }
    return this.helper.serviceResponse.successResponse({
      message: UnblockFitBuddySuccessMessages.FITBUDDY_UNBLOCKED_SUCCESSFULLY,
    });
  }

  private async getRelationStatus(
    userId: string,
    buddyId: string,
  ): Promise<FitBuddyStatusAndType> {
    const buddy = await this.spotRepo.findFitBuddy({
      $or: [
        {
          userId,
          fitBuddyId: buddyId,
        },
        {
          userId: buddyId,
          fitBuddyId: userId,
        },
      ],
    });

    let relationStatus = RelationStatusEnum.NOT_SPOTTED;
    if (buddy) {
      if (buddy.type === FitBuddiesType.FIT_BUDDY) {
        relationStatus = RelationStatusEnum.SPOTTED;
      } else if (buddy.type === FitBuddiesType.FOLLOWER) {
        if (buddy.fitBuddyId === userId) {
          relationStatus = RelationStatusEnum.REQUESTED;
        } else {
          relationStatus = RelationStatusEnum.SPOT_BACK;
        }
      } else {
        relationStatus = null;
      }
      return {
        status: buddy.status,
        type: buddy.type,
        relationStatus,
      };
    } else {
      return {
        status: null,
        type: null,
        relationStatus,
      };
    }
  }

  async fitBuddyStatus(
    userId: string,
    fitBuddyId: string,
  ): Promise<FitBuddyStatusAndTypeSuccessResponse> {
    const status = await this.getRelationStatus(userId, fitBuddyId);
    if (!status) {
      throw new APIException(
        FitBuddyStatusAndTypeErrorMessages.CAN_NOT_GET_FITBUDDY_STATUS_AND_TYPE,
        'CAN_NOT_GET_FITBUDDY_STATUS_AND_TYPE',
        HttpStatus.NOT_FOUND,
      );
    }
    return this.helper.serviceResponse.successResponse(status);
  }

  // Set the pre-signed url for media.
  // private async mappedGeneratePresignedUrl(
  //   users: any[],
  //   nearestUserList: boolean,
  // ): Promise<User[] | null> {
  //   try {
  //     if (nearestUserList) {
  //       return await Promise.all(
  //         users.map(async (user: any) => {
  //           user.image = await Promise.all(
  //             user.image?.map(async (img: string) => {
  //               img =
  //                 await this.helper.s3FileUploadService.generatePreSignedUrl(
  //                   img,
  //                   'user',
  //                 );
  //               return img;
  //             }),
  //           );
  //           return user;
  //         }),
  //       );
  //     } else {
  //       return await Promise.all(
  //         users.map(async (user: User) => {
  //           user.image.profile = user.image?.profile
  //             ? await this.helper.s3FileUploadService.generatePreSignedUrl(
  //                 user.image?.profile,
  //                 'user',
  //               )
  //             : null;
  //           return user;
  //         }),
  //       );
  //     }
  //   } catch (error) {
  //     console.log(error.message);
  //     return null;
  //   }
  // }

  async blockList(
    userId: string,
    condition: BlockListQuery,
  ): Promise<BlockListResponse> {
    const { offset, limit } = condition;
    const buddy = await this.spotRepo.findBlockList(userId, offset, limit);
    return this.helper.serviceResponse.successResponse(
      // await this.mappedGeneratePresignedUrl(buddy, false),
      buddy ? buddy : [],
    );
  }
}
