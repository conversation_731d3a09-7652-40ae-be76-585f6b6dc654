import {
  CommonNotificationGeneratorModule,
  SpotRequestAcceptGeneratorModule,
  SpotRequestGeneratorModule,
} from './worker/generator';
import { DailyReminderGeneratorModule } from './worker/generator/reminder/daily-reminder';
import {
  ApprovePostQueueModule,
  ApproveReelsQueueModule,
  SendEmailQueueModule,
} from './worker/others';
import { UserDeleteQueueModule } from './worker/others/user-delete';
import {
  SpotRequestAcceptSenderModule,
  SpotRequestSenderModule,
} from './worker/sender';
import { CommonNotificationSenderModule } from './worker/sender/common';
import { DailyReminderSenderModule } from './worker/sender/reminder/daily-reminder';

export const ResolveQueueModule = () => {
  return [
    SpotRequestGeneratorModule,
    SpotRequestSenderModule,
    SpotRequestAcceptGeneratorModule,
    SpotRequestAcceptSenderModule,
    ApprovePostQueueModule,
    ApproveReelsQueueModule,
    CommonNotificationGeneratorModule,
    CommonNotificationSenderModule,
    UserDeleteQueueModule,
    SendEmailQueueModule,
    DailyReminderGeneratorModule,
    DailyReminderSenderModule,
  ];
};
