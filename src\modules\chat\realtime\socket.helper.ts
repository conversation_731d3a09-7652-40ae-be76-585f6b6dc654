import { Injectable } from '@nestjs/common';
import { randomUUID } from 'crypto';
import {
  ChatType,
  NotificationCategory,
  NotificationModule,
  NotificationType,
  NotificationUser
} from 'models';
import { Helper } from 'src/helper/helper.interface';
import { NotificationHelperService } from 'src/modules/notification/services/helper.notification.service';
import {
  ClientToServerSendMessageDto,
  GeneratePayloadDto,
  ServerToClientReceiveMessageDto,
} from './dto/socket.dto';
import { RedisDBService } from './redis/redis.db.service';

@Injectable()
export class SocketHelperService {
  constructor(
    private readonly redisDBService: RedisDBService,
    private readonly helperSerice: Helper,
    private notificationHelperService: NotificationHelperService,
  ) {}

  async getDirectMsgPayload(
    clientId: string,
    data: ClientToServerSendMessageDto,
  ): Promise<GeneratePayloadDto> {
    const senderId = await this.redisDBService.getUserIdByClientId(clientId);
    const receiverClientId = await this.redisDBService.getClientIdByUserId(
      data.receiverId,
    );
    const content =
      data.type === 'image'
        ? await this.helperSerice.azureBlobStorageService.generatePreSignedUrl(
            data.content,
            'original/chat',
          )
        : data.content;
    const payload: ServerToClientReceiveMessageDto = {
      id: randomUUID(),
      senderId,
      senderName: data.senderName || '',
      senderImage: data.senderImage || '',
      receiverId: data.receiverId,
      receiverName: data.receiverName || '',
      receiverImage: data.receiverImage || '',
      content,
      type: data.type,
    };
    return { receiverClientId, payload };
  }

  async getGroupMsgPayload(
    clientId: string,
    data: ClientToServerSendMessageDto,
  ): Promise<GeneratePayloadDto> {
    const senderId = await this.redisDBService.getUserIdByClientId(clientId);
    const content =
      data.type === 'image'
        ? await this.helperSerice.azureBlobStorageService.generatePreSignedUrl(
            data.content,
            'original/chat',
          )
        : data.content;
    const payload: ServerToClientReceiveMessageDto = {
      id: randomUUID(),
      senderId,
      senderName: data.senderName || '',
      senderImage: data.senderImage || '',
      receiverId: data.receiverId,
      receiverName: data.receiverName || '',
      receiverImage: data.receiverImage || '',
      content,
      type: data.type,
    };
    return { payload };
  }

  private getReceiverData(
    data: ClientToServerSendMessageDto,
  ): NotificationUser {
    return {
      image: {
        profile: data.receiverImage,
      },
      id: data.receiverId,
      name: data.receiverName,
    };
  }

  private getSenderData(
    data: ClientToServerSendMessageDto,
    senderId: string,
  ): NotificationUser {
    return {
      image: {
        profile: data.senderImage,
      },
      id: senderId,
      name: data.senderName,
    };
  }

  async handleNotification(
    type: ChatType,
    senderId: string,
    data?: ClientToServerSendMessageDto,
    groupId?: string,
  ) {
    let payload = null;
    if (type == ChatType.ONE_TO_ONE_CHAT) {
      const user = this.getReceiverData(data);
      const sender = this.getSenderData(data, senderId);
      payload = {
        recipient: user,
        createdBy: sender,
        title: 'New Message',
        content: `${data?.senderName} has sent you a message`,
        module: NotificationModule.ONE_TO_ONE_CHAT,
        type: NotificationType.ONE_TO_ONE_CHAT,
        category: NotificationCategory.ONE_TO_ONE_CHAT,
        documentId: user.id,
      };
    } else if (ChatType.GROUP_CHAT) {
      payload = {
        title: 'New message',
        body: `${data?.senderName} from ${
          data?.receiverName ? data?.receiverName : 'fritsomnia group'
        } has sent a message`,
        data: data,
        documentId: groupId,
        module: NotificationModule.GROUP_CHAT,
        type: NotificationType.GROUP_CHAT,
        category: NotificationCategory.GROUP_CHAT,
        topic: groupId,
      };
    }
    await this.notificationHelperService.sendNotifications(payload);
  }

  async isReceiverOnline(data: ClientToServerSendMessageDto): Promise<boolean> {
    const receiver = await this.redisDBService.getUserInfo(data.receiverId);
    if (!receiver) {
      return false;
    }
    return receiver.isOnline;
  }
}
