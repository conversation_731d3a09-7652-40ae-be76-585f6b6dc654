export const QueueName = {
  POST_APPROVE_QUEUE: 'post/approve',
  REELS_APPROVE_QUEUE: 'reels/approve',
  SEND_SCHEDULED_EMAIL_QUEUE: 'scheduler/send-email',
};

export const NotificationQueueName = {
  SPOT_REQUEST_GENERATOR_QUEUE: 'notification/generator/spot-request',
  SPOT_REQUEST_SENDER_QUEUE: 'notification/sender/spot-request',
  SPOT_REQUEST_ACCEPT_GENERATOR_QUEUE:
    'notification/generator/spot-request-accept',
  SPOT_REQUEST_ACCEPT_SENDER_QUEUE: 'notification/sender/spot-request-accept',
  POST_REACTION_GENERATOR_QUEUE: 'notification/generator/post-reaction',
  POST_REACTION_SENDER_QUEUE: 'notification/sender/post-reaction',
  POST_COMMENT_GENERATOR_QUEUE: 'notification/generator/post-comment',
  POST_COMMENT_SENDER_QUEUE: 'notification/sender/post-comment',
  REELS_REACTION_GENERATOR_QUEUE: 'notification/generator/reels-reaction',
  REELS_REACTION_SENDER_QUEUE: 'notification/sender/reels-reaction',
  REELS_COMMENT_GENERATOR_QUEUE: 'notification/generator/reels-comment',
  REELS_COMMENT_SENDER_QUEUE: 'notification/sender/reels-comment',
  STORY_VIEW_GENERATOR_QUEUE: 'notification/generator/story-view',
  STORY_VIEW_SENDER_QUEUE: 'notification/sender/story-view',
  DIET_ENDING_GENERATOR_QUEUE: 'notification/generator/diet-ending',
  DIET_ENDING_SENDER_QUEUE: 'notification/sender/diet-ending',
  CHALLENGE_ENDING_GENERATOR_QUEUE: 'notification/generator/challenge-ending',
  CHALLENGE_ENDING_SENDER_QUEUE: 'notification/sender/challenge-ending',
  CHALLENGE_STATUS_GENERATOR_QUEUE: 'notification/generator/challenge-status',
  CHALLENGE_STATUS_SENDER_QUEUE: 'notification/sender/challenge-status',
  ORDER_STATUS_GENERATOR_QUEUE: 'notification/generator/order-status',
  ORDER_STATUS_SENDER_QUEUE: 'notification/sender/order-status',
  ONE_TO_ONE_CHAT_GENERATOR_QUEUE: 'notification/generator/chat',
  ONE_TO_ONE_CHAT_SENDER_QUEUE: 'notification/sender/chat',
  GROUP_CHAT_GENERATOR_QUEUE: 'notification/generator/group-chat',
  GROUP_CHAT_SENDER_QUEUE: 'notification/sender/group-chat',
  DAILY_REMINDER_GENERATOR_QUEUE: 'notification/generator/daily-reminder',
  DAILY_REMINDER_SENDER_QUEUE: 'notification/sender/daily-reminder',
};

export enum QueuePayloadType {
  SPOT_REQUEST_GENERATOR = 'SPOT_REQUEST_GENERATOR',
  SPOT_REQUEST_SENDER = 'SPOT_REQUEST_SENDER',
  SPOT_REQUEST_ACCEPT_GENERATOR = 'SPOT_REQUEST_ACCEPT_GENERATOR',
  SPOT_REQUEST_ACCEPT_SENDER = 'SPOT_REQUEST_ACCEPT_SENDER',
  POST_APPROVE = 'POST_APPROVE',
  POST_REACTION_GENERATOR = 'POST_REACTION_GENERATOR',
  POST_REACTION_SENDER = 'POST_REACTION_SENDER',
  POST_COMMENT_GENERATOR = 'POST_COMMENT_GENERATOR',
  POST_COMMENT_SENDER = 'POST_COMMENT_SENDER',
  REELS_APPROVE = 'REELS_APPROVE',
  REELS_REACTION_GENERATOR = 'REELS_REACTION_GENERATOR',
  REELS_REACTION_SENDER = 'REELS_REACTION_SENDER',
  REELS_COMMENT_GENERATOR = 'REELS_COMMENT_GENERATOR',
  REELS_COMMENT_SENDER = 'REELS_COMMENT_SENDER',
  STORY_VIEW_GENERATOR = 'STORY_VIEW_GENERATOR',
  STORY_VIEW_SENDER = 'STORY_VIEW_SENDER',
  DIET_ENDING_GENERATOR = 'DIET_ENDING_GENERATOR',
  DIET_ENDING_SENDER = 'DIET_ENDING_SENDER',
  CHALLENGE_ENDING_GENERATOR = 'CHALLENGE_ENDING_GENERATOR',
  CHALLENGE_ENDING_SENDER = 'CHALLENGE_ENDING_SENDER',
  CHALLENGE_STATUS_GENERATOR = 'CHALLENGE_STATUS_GENERATOR',
  CHALLENGE_STATUS_SENDER = 'CHALLENGE_STATUS_SENDER',
  ORDER_STATUS_GENERATOR = 'ORDER_STATUS_GENERATOR',
  ORDER_STATUS_SENDER = 'ORDER_STATUS_SENDER',
  ONE_TO_ONE_CHAT_GENERATOR = 'ONE_TO_ONE_CHAT_GENERATOR',
  ONE_TO_ONE_CHAT_SENDER = 'ONE_TO_ONE_CHAT_SENDER',
  GROUP_CHAT_GENERATOR = 'GROUP_CHAT_GENERATOR',
  GROUP_CHAT_SENDER = 'GROUP_CHAT_SENDER',
  DAILY_REMINDER_GENERATOR = 'DAILY_REMINDER_GENERATOR',
  DAILY_REMINDER_SENDER = 'DAILY_REMINDER_SENDER',

  DELETE_ACCOUNT_HANDLER = 'DELETE_ACCOUNT_HANDLER',
  SEND_SCHEDULED_EMAIL = 'SEND_SCHEDULED_EMAIL',
}

export const DeleteAccountQueueName = {
  DELETE_ACCOUNT_QUEUE: 'user/account-delete',
};
