import { Global, Module } from '@nestjs/common';
import { AzureBlobStorageService } from './azureBlobService';
import { IAzureBlobStorageService } from './azureBlobService/azureBlobStorage.service.interface';
import { FcmService } from './fcmService';
import { IFcmService } from './fcmService/fcm.service.interface';
import { Helper } from './helper.interface';
import { HelperService } from './helper.service';
import { MailService } from './mailService';
import { IMailService } from './mailService/mail.service.interface';
import { S3FileUploadService } from './s3Service';
import { IS3FileUploadService } from './s3Service/s3.service.interface';
import { ServiceResponse } from './serviceResponse';
import { IServiceResponse } from './serviceResponse/service.response.interface';
import { SmsService } from './smsService';
import { ISmsServive } from './smsService/sms.service.interface';

@Global()
@Module({
  providers: [
    {
      provide: IServiceResponse,
      useClass: ServiceResponse,
    },
    {
      provide: Helper,
      useClass: HelperService,
    },
    {
      provide: IMailService,
      useClass: MailService,
    },
    {
      provide: IFcmService,
      useClass: FcmService,
    },
    {
      provide: IAzureBlobStorageService,
      useClass: AzureBlobStorageService,
    },
    {
      provide: IS3FileUploadService,
      useClass: S3FileUploadService,
    },
    {
      provide: ISmsServive,
      useClass: SmsService,
    },
  ],
  exports: [Helper, IMailService],
})
export class HelperModule {}
