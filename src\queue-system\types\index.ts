import { NotificationModule } from 'models';

export interface NotificationRequestEvent {
  title: string;
  content: string;
  // Notification Module like (POST, SPOT, GROUP, etc)
  module: NotificationModule;
  // Notification Module Type like (NEW_POST, SPOT_REQUEST, GROUP_REQUEST, etc)
  type: string;
  // refId means notification redirect Id -> If the notification module is post,/
  // then this notification will redirect post/postId.
  refId: string;
  // Notification creator
  createdBy: {
    userId: string;
    name: string;
    avatar: string;
  };
  // Notification receivers
  targetUsers: string[];
  fcmToken?: string;
  payloadType?: string;
  category?: string;
  documentId: string;
  saveDatabase?:boolean;
  body?: string;
  data?: {
    [key: string]: string;
  };
  topic?:string;
}

export interface FCMSenderPayload {
  title: string;
  body: string;
  token: string;
  tokens?: string[]; // For bulk notifications
  isHighPriority: boolean;
  payloadType: string;
  task: string;
  data?: {
    [key: string]: string;
  };
  documentId: string;
  topic?: string;
  userCount?: number; // For tracking bulk operations
}
