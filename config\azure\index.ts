const {
  AZURE_STORAGE_CONNECTION_STRING,
  AZURE_STORAGE_ACCOUNT_NAME,
  AZURE_STORAGE_ACCOUNT_KEY,
  AZURE_STORAGE_CONTAINER_NAME,
  AZURE_STORAGE_PUBLIC_CONTAINER_NAME,
  AZURE_PRESIGNED_EXPIRE_TIME,
  AZURE_STORAGE_CDN_URL,
  AZURE_STORAGE_PUBLIC_CDN_URL,
  AZURE_STORAGE_PUBLIC_URL,
} = process.env;

export const azureConfig = {
  azure_connection_string: AZURE_STORAGE_CONNECTION_STRING || '',
  azure_storage_account: AZURE_STORAGE_ACCOUNT_NAME || '',
  azure_storage_key: AZURE_STORAGE_ACCOUNT_KEY || '',
  azure_container_name: AZURE_STORAGE_CONTAINER_NAME || '',
  azure_public_container_name: AZURE_STORAGE_PUBLIC_CONTAINER_NAME || '',
  azure_presigned_expire_time: parseInt(AZURE_PRESIGNED_EXPIRE_TIME) || 86400, // In seconds
  azure_cdn_baseurl: AZURE_STORAGE_CDN_URL || '',
  azure_pub_cdn_url: AZURE_STORAGE_PUBLIC_CDN_URL || '',
  azure_pub_url: AZURE_STORAGE_PUBLIC_URL || '',
};
