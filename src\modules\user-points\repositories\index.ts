import { Injectable } from '@nestjs/common';
import {
  IUpdatePoints,
  IUserLeadersboard,
  IUserPoints,
  IUserPointsTransTypeEnum,
} from 'models';
import mongoose from 'mongoose';
import {
  UserPointsHistoryModel,
  UserPointsModel,
} from 'src/database/user-points/user-points.model';
import { UserPointsTransHistory } from 'src/entity/user-points';
import { HelperPointsRepository } from '../services/helper.points.repository';

@Injectable()
export class UserPointsRepository {
  constructor(
    private readonly helperUserPointsRepository: HelperPointsRepository,
  ) {}

  async updatePointsAtomic(
    userId: string,
    data: {
      type: IUserPointsTransTypeEnum;
      points: number;
      pointSourceType: any;
      pointSourceId: string;
      pointSourceName: string;
    },
  ): Promise<void> {
    const increment =
      data.type === IUserPointsTransTypeEnum.EARNED
        ? data.points
        : -data.points;

    if (increment < 0) {
      const result = await UserPointsModel.updateOne(
        { userId, points: { $gte: data.points } },
        { $inc: { points: increment } },
        { upsert: true },
      );

      if (result.matchedCount === 0) {
        throw new Error('Insufficient points');
      }
    } else {
      await UserPointsModel.updateOne(
        { userId },
        { $inc: { points: increment } },
        { upsert: true },
      );
    }

    await new UserPointsHistoryModel({
      userId,
      ...data,
    }).save();
  }

  async updateReferrerAndReferralPoints(
    referrerId: string,
    referralId: string,
    referrerPoints: number,
    referralPoints: number,
    pointSourceType: any,
    pointSourceId: string,
    pointSourceName: string,
  ): Promise<void> {
    await Promise.all([
      this.updatePointsAtomic(referrerId, {
        type: IUserPointsTransTypeEnum.EARNED,
        points: referrerPoints,
        pointSourceType,
        pointSourceId,
        pointSourceName,
      }),
      this.updatePointsAtomic(referralId, {
        type: IUserPointsTransTypeEnum.EARNED,
        points: referralPoints,
        pointSourceType,
        pointSourceId,
        pointSourceName,
      }),
    ]);
  }

  async updatePoints(
    userId: string,
    data: IUpdatePoints,
  ): Promise<IUserPoints | null> {
    // transaction session
    const session = await mongoose.startSession();
    try {
      // start session
      session.startTransaction();

      // create new doc if not exist else update points
      let isExist = await UserPointsModel.findOne({ userId });
      if (!isExist) {
        if (data.type === IUserPointsTransTypeEnum.EARNED) {
          isExist = await new UserPointsModel({
            userId,
            points: data.points,
          }).save({
            session,
          });
        }
      } else {
        if (
          data.type === IUserPointsTransTypeEnum.SPENT &&
          isExist.points >= data.points
        ) {
          isExist.points = isExist.points - data.points;
        } else if (data.type === IUserPointsTransTypeEnum.EARNED) {
          isExist.points = isExist.points + data.points;
        } else {
        }
        isExist = await UserPointsModel.findOneAndUpdate(
          {
            userId,
          },
          {
            $set: { points: isExist.points },
          },
          {
            new: true,
            session,
          },
        ).lean();
      }

      const history = await new UserPointsHistoryModel({
        userId,
        ...data,
      }).save({ session });
      // commit the session
      await session.commitTransaction();

      delete isExist?._id;
      return isExist;
    } catch (error) {
      // abort & end session
      await session.abortTransaction();
      await session.endSession();
      console.log(error.message);
      return null;
    } finally {
      await session.endSession();
    }
  }

  async getUserPointsWithRanking(
    userId: string,
  ): Promise<
    (IUserPoints & { userRanking: number; totalRanking: number }) | null
  > {
    const pipeline: any = [
      {
        $setWindowFields: {
          sortBy: { points: -1 },
          output: {
            userRanking: { $rank: {} },
          },
        },
      },
      {
        $match: { userId },
      },
      {
        $project: {
          _id: 0,
          userId: 1,
          points: 1,
          userRanking: 1,
        },
      },
    ];

    const userWithRank = await UserPointsModel.aggregate(pipeline).exec();
    if (!userWithRank.length) return null;

    const totalRanking = await UserPointsModel.countDocuments();

    return {
      ...userWithRank[0],
      totalRanking,
    };
  }

  async leaderBoard(
    offset: number,
    limit: number,
  ): Promise<IUserLeadersboard[] | null> {
    try {
      const query: any[] =
        this.helperUserPointsRepository.userPointsLeaderBoardAggQuery(
          offset,
          limit,
        );

      const leaderBoard = await UserPointsModel.aggregate(query);
      return leaderBoard;
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async getUserPointsHistory(
    userId: string,
    offset: number,
    limit: number,
    filter = 'all',
  ): Promise<UserPointsTransHistory[] | null> {
    try {
      const query =
        this.helperUserPointsRepository.pointsTransactionHistoryAggQuery(
          userId,
          offset,
          limit,
          filter,
        );
      const pointHistory = await UserPointsHistoryModel.aggregate(query);
      return pointHistory;
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async getPointsByUserId(userId: string): Promise<IUserPoints | null> {
    try {
      return await UserPointsModel.findOne({ userId }).select('-_id').lean();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }
}
