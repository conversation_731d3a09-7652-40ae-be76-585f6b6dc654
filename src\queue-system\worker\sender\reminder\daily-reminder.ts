import { Injectable, Module, OnApplicationBootstrap } from '@nestjs/common';
import { FcmService } from 'src/helper/fcmService';
import { QueueInstance } from 'src/queue-system';
import { NotificationQueueName } from 'src/queue-system/predefined-data';
import { FCMSenderPayload } from 'src/queue-system/types';

class FcmServiceHelper extends FcmService {}

@Injectable()
export class DailyReminderSenderQueue implements OnApplicationBootstrap {
  async onApplicationBootstrap() {
    try {
      await (
        await QueueInstance
      ).consume(NotificationQueueName.DAILY_REMINDER_SENDER_QUEUE);
    } catch (error) {
      console.error('Failed to start reminder queue consumer:', error.message);
    }
  }

  static async sender(payload: FCMSenderPayload): Promise<void> {

    try {
      const { title, body, token, tokens, isHighPriority, data, documentId, task, topic } = payload;

      // Validate required fields for topic messaging
      if (task === 'topic-sender') {
        if (!topic) {
          console.error('Topic is required for topic-sender task');
          return;
        }
        if (!title || !body) {
          console.error('Title and body are required for topic messaging');
          return;
        }

        // Use FCM sendToTopic for broadcasting with enhanced configuration
        const fcmService = new FcmServiceHelper();
        await fcmService.sendToTopic({
          title,
          body,
          documentId,
          data,
          topic,
          isHighPriority: isHighPriority ?? true,
          ttl: 3600, // 1 hour TTL for reminders
        });

        console.log(`Reminder broadcasted to topic '${topic}'`);
      }

      // Handle individual token messaging (fallback or specific user targeting)
      else if (token && body) {
        const fcmService = new FcmServiceHelper();
        const success = await fcmService.sendToIndividual({
          token,
          title,
          body,
          documentId,
          data,
          isHighPriority: isHighPriority ?? true,
          ttl: 3600,
        });

        if (success) {
          console.log(`Individual reminder sent`);
        } else {
          console.error(`Failed to send individual reminder`);
        }
      }

      // Handle batch token messaging
      else if (tokens && Array.isArray(tokens) && tokens.length > 0 && body) {
        const fcmService = new FcmServiceHelper();
        const success = await fcmService.sendToMany(
          tokens,
          title,
          body,
          documentId,
          data,
          isHighPriority ?? true,
          3600
        );

        if (success) {
          console.log(`Batch reminder sent to ${tokens.length} users`);
        } else {
          console.error(`Failed to send batch reminder to ${tokens.length} users`);
        }
      }

      // Invalid payload
      else {
        console.error('Invalid reminder payload: missing required fields');
      }
    } catch (error: any) {
      console.error('Failed to process reminder notification:', error.message);
    }
  }
}

@Module({
  providers: [DailyReminderSenderQueue],
})
export class DailyReminderSenderModule {}
