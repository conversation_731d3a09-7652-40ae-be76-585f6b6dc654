import { Injectable, Module, OnApplicationBootstrap } from '@nestjs/common';
import { FcmService } from 'src/helper/fcmService';
import { QueueInstance } from 'src/queue-system';
import { NotificationQueueName } from 'src/queue-system/predefined-data';
import { FCMSenderPayload } from 'src/queue-system/types';

class FcmServiceHelper extends FcmService {}

@Injectable()
export class DailyReminderSenderQueue implements OnApplicationBootstrap {
  async onApplicationBootstrap() {
    try {
      await (
        await QueueInstance
      ).consume(NotificationQueueName.DAILY_REMINDER_SENDER_QUEUE);
    } catch (error) {
      console.error('❌ [QUEUE-WORKER] Failed to start reminder queue consumer:', error.message);
    }
  }

  static async sender(payload: FCMSenderPayload): Promise<void> {
    const startTime = Date.now();
    let operationResult = 'unknown';

    try {
      const { title, body, token, tokens, isHighPriority, data, documentId, task, topic } = payload;

      // Validate required fields for topic messaging
      if (task === 'topic-sender') {
        if (!topic) {
          console.error('❌ [REMINDER-QUEUE] Topic is required for topic-sender task');
          return;
        }
        if (!title || !body) {
          console.error('❌ [REMINDER-QUEUE] Title and body are required for topic messaging');
          return;
        }

        console.log(`📤 [REMINDER-QUEUE] Processing topic broadcast: "${title}" → topic '${topic}'`);

        // Use FCM sendToTopic for broadcasting with enhanced configuration
        const fcmService = new FcmServiceHelper();
        const result = await fcmService.sendToTopic({
          title,
          body,
          documentId,
          data,
          topic,
          isHighPriority: isHighPriority ?? true,
          ttl: 3600, // 1 hour TTL for reminders
        });

        operationResult = 'topic_broadcast_success';
        const duration = Date.now() - startTime;
        console.log(`✅ [REMINDER-QUEUE] Successfully broadcasted "${title}" to topic '${topic}' in ${duration}ms`);

        // Log additional details for monitoring
        console.log(`📊 [REMINDER-QUEUE] Broadcast details: documentId=${documentId}, isHighPriority=${isHighPriority}, dataKeys=${Object.keys(data || {}).join(',')}`);
      }

      // Handle individual token messaging (fallback or specific user targeting)
      else if (token && body) {
        console.log(`📤 [REMINDER-QUEUE] Processing individual notification: "${title}" → token ${token.substring(0, 10)}...`);

        const fcmService = new FcmServiceHelper();
        const success = await fcmService.sendToIndividual({
          token,
          title,
          body,
          documentId,
          data,
          isHighPriority: isHighPriority ?? true,
          ttl: 3600,
        });

        operationResult = success ? 'individual_send_success' : 'individual_send_failed';
        const duration = Date.now() - startTime;

        if (success) {
          console.log(`✅ [REMINDER-QUEUE] Successfully sent individual notification "${title}" in ${duration}ms`);
        } else {
          console.error(`❌ [REMINDER-QUEUE] Failed to send individual notification "${title}" after ${duration}ms`);
        }
      }

      // Handle batch token messaging
      else if (tokens && Array.isArray(tokens) && tokens.length > 0 && body) {
        console.log(`📤 [REMINDER-QUEUE] Processing batch notification: "${title}" → ${tokens.length} tokens`);

        const fcmService = new FcmServiceHelper();
        const success = await fcmService.sendToMany(
          tokens,
          title,
          body,
          documentId,
          data,
          isHighPriority ?? true,
          3600
        );

        operationResult = success ? 'batch_send_success' : 'batch_send_failed';
        const duration = Date.now() - startTime;

        if (success) {
          console.log(`✅ [REMINDER-QUEUE] Successfully sent batch notification "${title}" to ${tokens.length} tokens in ${duration}ms`);
        } else {
          console.error(`❌ [REMINDER-QUEUE] Failed to send batch notification "${title}" to ${tokens.length} tokens after ${duration}ms`);
        }
      }

      // Invalid payload
      else {
        operationResult = 'invalid_payload';
        console.error('❌ [REMINDER-QUEUE] Invalid payload: missing required fields (topic for topic-sender, token/tokens for individual/batch)');
        console.error('📋 [REMINDER-QUEUE] Payload details:', {
          task,
          hasTitle: !!title,
          hasBody: !!body,
          hasTopic: !!topic,
          hasToken: !!token,
          hasTokens: !!(tokens && Array.isArray(tokens) && tokens.length > 0),
          documentId
        });
      }
    } catch (error: any) {
      operationResult = 'error';
      const duration = Date.now() - startTime;

      console.error(`❌ [REMINDER-QUEUE] Failed to process reminder notification after ${duration}ms:`, {
        error: error.message,
        stack: error.stack,
        payload: {
          task: payload.task,
          title: payload.title,
          topic: payload.topic,
          hasToken: !!payload.token,
          hasTokens: !!(payload.tokens && Array.isArray(payload.tokens)),
          documentId: payload.documentId
        }
      });

      // Attempt to log critical errors for monitoring
      if (error.code === 'messaging/invalid-argument' || error.code === 'messaging/registration-token-not-registered') {
        console.warn(`⚠️ [REMINDER-QUEUE] FCM token issue detected: ${error.code} - ${error.message}`);
      } else if (error.code === 'messaging/quota-exceeded') {
        console.error(`🚨 [REMINDER-QUEUE] FCM quota exceeded: ${error.message}`);
      } else {
        console.error(`🚨 [REMINDER-QUEUE] Unexpected error: ${error.message}`);
      }
    } finally {
      // Log operation completion for monitoring
      const totalDuration = Date.now() - startTime;
      console.log(`📈 [REMINDER-QUEUE] Operation completed: ${operationResult} in ${totalDuration}ms`);
    }
  }
}

@Module({
  providers: [DailyReminderSenderQueue],
})
export class DailyReminderSenderModule {}
