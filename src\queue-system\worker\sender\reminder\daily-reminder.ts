import { Injectable, Module, OnApplicationBootstrap } from '@nestjs/common';
import { FcmService } from 'src/helper/fcmService';
import { QueueInstance } from 'src/queue-system';
import { NotificationQueueName } from 'src/queue-system/predefined-data';
import { FCMSenderPayload } from 'src/queue-system/types';

class FcmServiceHelper extends FcmService {}

@Injectable()
export class DailyReminderSenderQueue implements OnApplicationBootstrap {
  async onApplicationBootstrap() {
    try {
      await (
        await QueueInstance
      ).consume(NotificationQueueName.DAILY_REMINDER_SENDER_QUEUE);
    } catch (error) {
      console.log(error.message);
    }
  }

  static async sender(payload: FCMSenderPayload): Promise<void> {
    try {
      const { title, body, token, tokens, isHighPriority, data, documentId, task, topic } = payload;

      // Handle topic messaging for broadcasting to all users
      if (task === 'topic-sender' && topic && body) {
        await new FcmServiceHelper().sendToTopic({
          title,
          body,
          documentId,
          data,
          topic,
          isHighPriority,
          ttl: 600,
        });
        console.log(`Topic notification sent successfully: ${title} to topic '${topic}'`);
        return;
      }

      // Handle bulk sending for better performance
      if (task === 'bulk-sender' && tokens && tokens.length > 0) {
        // Use FCM sendToMany for bulk notifications
        await new FcmServiceHelper().sendToMany(
          tokens,
          title,
          body,
          documentId,
          data,
          isHighPriority,
          600, // ttl
        );
      } else if (token && body) {
        // Individual notification
        await new FcmServiceHelper().sendToIndividual({
          token,
          title,
          body,
          documentId,
          data,
          isHighPriority,
          ttl: 600,
        });
      }
    } catch (error: any) {
      console.error('Daily reminder sender error:', error.message);
    }
  }
}

@Module({
  providers: [DailyReminderSenderQueue],
})
export class DailyReminderSenderModule {}
