import { InjectRedis, Redis } from '@nestjs-modules/ioredis';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ChatConversationModel } from 'src/database/chat/chatConversation.model';
import { UserActivityStreakProvider } from 'src/modules/user-activity-streak/providers/internal.provider';
import { RedisDbUserDto } from '../dto/socket.dto';
import { RealtimeService } from '../realtime.service';

@Injectable()
export class RedisDBService {
  constructor(
    @InjectRedis() private readonly redis: Redis,
    @Inject(forwardRef(() => RealtimeService))
    private readonly realtimeService: RealtimeService,
    private readonly userActivityStreakProvider: UserActivityStreakProvider,
  ) {}

  async getClientIdByUserId(userId: string): Promise<string> {
    const user = await this.redis.get('FIT_' + userId);
    const parsed = JSON.parse(user);
    return parsed ? parsed.clientId : null;
  }

  async getUserIdByClientId(clientId: string): Promise<string> {
    return await this.redis.get('SOCKET_' + clientId);
  }

  async getUserInfo(userId: string): Promise<RedisDbUserDto | null> {
    const user = await this.redis.get('FIT_' + userId);
    if (!user) {
      return null;
    }
    return JSON.parse(user);
  }

  async makeUserOnline(userData: RedisDbUserDto): Promise<string> {
    const { clientId, userId } = userData;
    const user = await this.getUserInfo(userData.userId);
    if (user) {
      user.isOnline = true;
      user.clientId = clientId;
      userData = user;
    }
    await this.setClientId(clientId, userId);
    await this.realtimeService.updateUserIsOnline(userData.userId, true);
    // This time will help to count the time of activity
    await this.redis.set('STIME_' + clientId, Date.now().toString());   // NEW
    console.log('Set STIME_' + clientId, Date.now().toString());
    return await this.setUserId(userId, userData);
  }

  async makeUserOffline(clientId: string): Promise<void> {
    const userId = await this.getUserIdByClientId(clientId);

    const start = await this.redis.get('STIME_' + clientId);
    console.log('Get STIME_' + clientId, start);
    if (start) {
      const end = Date.now();
      const startTime = new Date(Number(start));
      const endTime = new Date(end);
      const millis = end - Number(start);
      console.log("Add session record", userId, startTime, endTime, millis);
      await this.userActivityStreakProvider.addSessionRecord(userId, startTime, endTime, millis);
      await this.redis.del('STIME_' + clientId);
    }

    await this.realtimeService.updateUserIsOnline(userId, false);
    await this.removeClientId(clientId);
    await this.removeUserId(userId);
  }

  async getGroupIdsByClientId(clientId: string): Promise<string[]> {
    const userId = await this.getUserIdByClientId(clientId);
    const list = (
      await ChatConversationModel.find({
        users: { $all: [userId] },
        type: 'group',
      }).select('-_id groupId')
    ).map((e) => e?.groupId && e.groupId);
    return list ? list : [];
  }

  // -------- private methods ----------

  private async setClientId(clientId: string, userId: string): Promise<string> {
    return await this.redis.set('SOCKET_' + clientId, userId);
  }

  private async removeClientId(clientId: string): Promise<number> {
    return await this.redis.del('SOCKET_' + clientId);
  }

  private async setUserId(
    userId: string,
    userData: RedisDbUserDto,
  ): Promise<string> {
    return await this.redis.set('FIT_' + userId, JSON.stringify(userData));
  }

  private async removeUserId(userId: string): Promise<number> {
    return await this.redis.del('FIT_' + userId);
  }

  // ------- debuger method---------

  async clientIdList(): Promise<void> {
    const keys = await this.redis.keys('FIT_*');
    let user = null;
    for (let i = 0; i < keys.length; i++) {
      user = await this.redis.get(keys[i]);
      user = JSON.parse(user);
      if (user?.isOnline) {
        console.log('user ', i + 1, ' ', user);
      }
    }
  }
}
