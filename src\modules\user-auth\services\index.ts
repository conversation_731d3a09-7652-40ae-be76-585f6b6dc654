import { InjectRedis } from '@nestjs-modules/ioredis';
import { HttpStatus, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import appleSignin from 'apple-signin-auth';
import * as bcrypt from 'bcrypt';
import { authConfig } from 'config/auth';
import { randomUUID } from 'crypto';
import Redis from 'ioredis';
import {
  AppleSignInErrorMessages,
  AppleSignInRequest,
  AppleSignInSuccessResponse,
  CreateUserErrorMessages,
  CreateUserRequest,
  CreateUserSuccessResponse,
  FacebookSignInErrorMessages,
  FacebookSignInRequest,
  FacebookSignInSuccessResponse,
  GoogleUserSignInErrorMessages,
  GoogleUserSignInRequest,
  GoogleUserSignInSuccessResponse,
  IIndexNameEnum,
  SendOtpErrorMessages,
  SendOtpForSignupRequest,
  SendOtpRequest,
  SendOtpSuccessResponse,
  SignInErrorMessages,
  UserForgotPasswordErrorMessages,
  UserForgotPasswordRequest,
  UserForgotPasswordSuccessMessages,
  UserForgotPasswordSuccessResponse,
  UserSignInRequest,
  UserSignInSuccessResponse,
  VerifyOtpErrorMessages,
  VerifyOtpRequest,
  VerifyOtpSuccessMessages,
  VerifyOtpSuccessResponse,
} from 'models';
import fetch from 'node-fetch';
import { User } from 'src/entity/user';
import { UserJwtPayload } from 'src/entity/user-auth';
import { Helper } from 'src/helper/helper.interface';
import { APIException } from 'src/internal/exception/api.exception';
import { ESearchDbService } from 'src/modules/global-search/services/elastic.db.service';
import { UserRepository } from 'src/modules/user/repositories';
import { successResponse } from 'src/utils/response';
import { IServiceResponse } from 'src/utils/response/service.response.interface';
import { UserAuthRepository } from '../repositories';
import { TopicManagementService } from 'src/modules/reminder/services/topic-management.service';

@Injectable()
export class UserAuthService {
  constructor(
    @InjectRedis() private readonly redis: Redis,
    private userRepo: UserRepository,
    private helper: Helper,
    private jwtService: JwtService,
    private readonly eSearchDbService: ESearchDbService,
    private readonly userAuthRepository: UserAuthRepository,
    private readonly topicManagementService: TopicManagementService,
  ) {}

  async signup(data: CreateUserRequest): Promise<CreateUserSuccessResponse> {
    const { email } = data;
    const doesUserExist = email && (await this.userRepo.findUser({ email }));
    if (doesUserExist)
      throw new APIException(
        CreateUserErrorMessages.USER_ALREADY_EXITS,
        'USER_ALREADY_EXITS',
        HttpStatus.BAD_REQUEST,
      );

    const tmpUser = await this.userRepo.findOtpForSignup({
      ...data,
      otpExpireTime: { $gt: Date.now() },
    });
    if (!tmpUser)
      throw new APIException(
        CreateUserErrorMessages.OTP_EXPIRED_OR_INVALID_OTP,
        'OTP_EXPIRED_OR_INVALID_OTP',
        HttpStatus.BAD_REQUEST,
      );

    const user: any = tmpUser;
    user.email = email && email.toLowerCase();
    user.password = await bcrypt.hash(user.password, authConfig.salt);

    const registeredUser = await this.userRepo.createUser(user);
    if (!registeredUser)
      throw new APIException(
        CreateUserErrorMessages.CAN_NOT_CREATE_USER,
        'CAN_NOT_CREATE_USER',
        HttpStatus.BAD_REQUEST,
      );

    const payload: UserJwtPayload = {
      id: registeredUser.id,
      email: registeredUser.email,
      phone: registeredUser.phone,
      logInTime: Date.now(),
      role: 'user',
    };
    this.eSearchDbService.insertOneItemToES(
      IIndexNameEnum.USER,
      registeredUser.id,
    );
    const token = await this.generateToken(payload);
    return this.helper.serviceResponse.successResponse({ token });
  }

  async signupSendOTP(
    data: SendOtpForSignupRequest,
  ): Promise<SendOtpSuccessResponse> {
    const { email } = data;
    const doesUserExist = email && (await this.userRepo.findUser({ email }));
    if (doesUserExist)
      throw new APIException(
        SendOtpErrorMessages.USER_ALREADY_EXITS,
        'USER_ALREADY_EXITS',
        HttpStatus.BAD_REQUEST,
      );
    return await this.sendOtpForSignUp(data);
  }

  async sendOtpForSignUp(
    data: SendOtpForSignupRequest,
  ): Promise<SendOtpSuccessResponse> {
    const { email } = data;
    const otpExists = await this.userRepo.findOtpForSignup({
      email,
    });
    const randomOtp = 100000 + Math.floor(Math.random() * 900000);

    // Check if the OTP is already in the database.
    if (otpExists) {
      const otpUpdated = await this.userRepo.updateOtpForSignup(
        { email },
        {
          otp: randomOtp,
          otpExpireTime: Date.now() + authConfig.signup.otp_expiration_time,
          isVerified: false,
          ...data,
        },
      );

      if (!otpUpdated)
        throw new APIException(
          SendOtpErrorMessages.CAN_NOT_UPDATE_OTP,
          'CAN_NOT_UPDATE_OTP',
          HttpStatus.BAD_REQUEST,
        );
    } else {
      const otpSend = await this.userRepo.sendOtpForSignup({
        ...data,
        otp: randomOtp,
        otpExpireTime: Date.now() + authConfig.signup.otp_expiration_time,
      });

      if (!otpSend)
        throw new APIException(
          SendOtpErrorMessages.CAN_NOT_SEND_OTP,
          'CAN_NOT_SEND_OTP',
          HttpStatus.BAD_REQUEST,
        );
    }

    // send otp in email
    const mailBody = this.otpEmailBody(data.email, randomOtp);
    this.helper.mailService.sendMail(
      data.email,
      `${authConfig.signup.otp_subject}`,
      // `${authConfig.signup.otp_body_prefix} ${randomOtp}`,
      mailBody,
    );

    return this.helper.serviceResponse.successResponse({
      message: `Your FITSOMNIA OTP is send to email`,
    });
  }

  async sendOtpForForgotPassword(
    data: SendOtpRequest,
  ): Promise<SendOtpSuccessResponse> {
    const { email } = data;
    const otpExists = await this.userRepo.findOtp({ email });
    const randomOtp = 100000 + Math.floor(Math.random() * 900000);

    if (otpExists) {
      const otpUpdated = await this.userRepo.updateOtp(
        { email },
        {
          otp: randomOtp,
          otpExpireTime:
            Date.now() + authConfig.forgot_password.otp_expiration_time,
          isVerified: false,
        },
      );
      if (!otpUpdated)
        throw new APIException(
          SendOtpErrorMessages.CAN_NOT_UPDATE_OTP,
          'CAN_NOT_UPDATE_OTP',
          HttpStatus.BAD_REQUEST,
        );
    } else {
      const otpSend = await this.userRepo.sendOtp({
        ...data,
        otp: randomOtp,
        otpExpireTime:
          Date.now() + authConfig.forgot_password.otp_expiration_time,
      });
      if (!otpSend)
        throw new APIException(
          SendOtpErrorMessages.CAN_NOT_SEND_OTP,
          'CAN_NOT_SEND_OTP',
          HttpStatus.BAD_REQUEST,
        );
    }

    // send otp in email
    const mailBody = this.otpEmailBody(data.email, randomOtp);
    this.helper.mailService.sendMail(
      data.email,
      `${authConfig.forgot_password.otp_subject}`,
      // `${authConfig.forgot_password.otp_body_prefix} ${randomOtp}`,
      mailBody,
    );
    return this.helper.serviceResponse.successResponse({
      message: `Your FITSOMNIA OTP is send to Email`,
    });
  }

  async verifyOtp(data: VerifyOtpRequest): Promise<VerifyOtpSuccessResponse> {
    const verifyOtp = await this.userRepo.verifyOtp({
      ...data,
      otpExpireTime: { $gt: Date.now() },
    });

    if (!verifyOtp)
      throw new APIException(
        VerifyOtpErrorMessages.OTP_EXPIRED_OR_INVALID_OTP,
        'OTP_EXPIRED_OR_INVALID_OTP',
        HttpStatus.BAD_REQUEST,
      );

    return this.helper.serviceResponse.successResponse({
      message: VerifyOtpSuccessMessages.OTP_VERIFIED_SUCCESSFUL,
    });
  }

  async signIn(data: UserSignInRequest): Promise<UserSignInSuccessResponse> {
    const doesUserEmailExist =
      data.email &&
      (await this.userRepo.getUserPassword({ email: data.email }));
    const doesUserPhoneExist =
      data.phone &&
      (await this.userRepo.getUserPassword({ phone: data.phone }));
    if (!doesUserEmailExist && !doesUserPhoneExist)
      throw new APIException(
        SignInErrorMessages.INVALID_CREDENTIALS,
        'INVALID_CREDENTIALS',
        HttpStatus.BAD_REQUEST,
      );

    const user: User = doesUserEmailExist || doesUserPhoneExist;

    const doesUserPasswordMatch = await bcrypt.compare(
      data.password,
      user.password,
    );
    if (!doesUserPasswordMatch)
      throw new APIException(
        SignInErrorMessages.INVALID_CREDENTIALS,
        'INVALID_CREDENTIALS',
        HttpStatus.BAD_REQUEST,
      );

    const payload: UserJwtPayload = {
      id: user.id,
      email: user.email,
      phone: user.phone,
      logInTime: Date.now(),
      role: 'user',
    };

    const token = await this.generateToken(payload);
    if (token && data.fcmToken) {
      user.fcmToken = data.fcmToken;
      await this.userRepo.updateUser(user.id, user);
      // Subscribe user to notification topics
      await this.subscribeUserToTopics(user.id, data.fcmToken);
    }

    return this.helper.serviceResponse.successResponse({ token });
  }

  async forgotPassword(
    data: UserForgotPasswordRequest,
  ): Promise<UserForgotPasswordSuccessResponse> {
    const { email } = data;
    const user = await this.userRepo.getUserPassword({
      email,
    });
    if (!user)
      throw new APIException(
        UserForgotPasswordErrorMessages.CAN_NOT_GET_USER,
        'CAN_NOT_GET_USER',
        HttpStatus.BAD_REQUEST,
      );

    const otpVerified = await this.userRepo.findOtp({
      isVerified: true,
      email,
    });
    if (
      !otpVerified ||
      otpVerified.otpVerifiedAt +
        authConfig.forgot_password.set_password_expiration_time <
        Date.now()
    )
      throw new APIException(
        CreateUserErrorMessages.TIME_LIMIT_EXCEED_OR_UNVERIFIED_USER,
        'TIME_LIMIT_EXCEED_OR_UNVERIFIED_USER',
        HttpStatus.BAD_REQUEST,
      );

    // change the new password
    user.password = await bcrypt.hash(data.password, authConfig.salt);

    const updatedPassword = await this.userRepo.updateUser(user.id, user);
    if (!updatedPassword)
      throw new APIException(
        CreateUserErrorMessages.TIME_LIMIT_EXCEED_OR_UNVERIFIED_USER,
        'TIME_LIMIT_EXCEED_OR_UNVERIFIED_USER',
        HttpStatus.BAD_REQUEST,
      );

    await this.userRepo.deleteOtp({ email });
    return this.helper.serviceResponse.successResponse({
      message: UserForgotPasswordSuccessMessages.FORGOT_PASSWORD_SUCCESSFUL,
    });
  }

  async forgotPasswordSendOTP(
    data: SendOtpRequest,
  ): Promise<SendOtpSuccessResponse> {
    const { email } = data;
    const doesUserExist = await this.userRepo.findUser({ email });
    if (!doesUserExist)
      throw new APIException(
        UserForgotPasswordErrorMessages.CAN_NOT_GET_USER,
        'CAN_NOT_GET_USER',
        HttpStatus.BAD_REQUEST,
      );
    return this.sendOtpForForgotPassword(data);
  }

  async forgotPasswordVerifyOTP(
    data: VerifyOtpRequest,
  ): Promise<VerifyOtpSuccessResponse> {
    const { email } = data;
    const doesUserExist = await this.userRepo.findUser({ email });
    if (!doesUserExist)
      throw new APIException(
        UserForgotPasswordErrorMessages.CAN_NOT_GET_USER,
        'CAN_NOT_GET_USER',
        HttpStatus.BAD_REQUEST,
      );
    return this.verifyOtp(data);
  }

  async verifyFcmToken(userId: string, fcmToken: string) {
    await this.userRepo.verifyFcmtoken(userId, fcmToken);

    // Subscribe user to reminder topics when FCM token is updated
    if (fcmToken) {
      await this.subscribeUserToTopics(userId, fcmToken);
    }

    return 'token updated successfully';
  }

  /**
   * Centralized method to handle user subscription to notification topics
   * This ensures consistent topic subscription across all authentication flows
   */
  private async subscribeUserToTopics(userId: string, fcmToken: string): Promise<void> {
    try {
      console.log(`🔔 [USER-AUTH] Subscribing user ${userId} to notification topics...`);

      // Subscribe to reminder topics using the TopicManagementService
      const success = await this.topicManagementService.subscribeUserToReminders(fcmToken);

      if (success) {
        console.log(`✅ [USER-AUTH] Successfully subscribed user ${userId} to reminder topics`);
      } else {
        console.error(`❌ [USER-AUTH] Failed to subscribe user ${userId} to reminder topics`);
      }
    } catch (error) {
      console.error(`❌ [USER-AUTH] Error subscribing user ${userId} to topics:`, error.message);
    }
  }

  async googleSignIn(
    data: GoogleUserSignInRequest,
  ): Promise<GoogleUserSignInSuccessResponse> {
    // Verify Google Access token
    const verifiedUser = await this.verifyToken(data.accessToken, 'google');

    // console.log("google verified user info", verifiedUser)

    if (!verifiedUser)
      throw new APIException(
        GoogleUserSignInErrorMessages.NO_GOOGLE_USER_FOUND,
        'NO_GOOGLE_USER_FOUND',
        HttpStatus.NOT_FOUND,
      );

    // Check the user email already exists
    const user = await this.userRepo.findUser({
      email: verifiedUser.email,
    });
    let createdUser = null;
    if (!user) {
      // set a default password
      const hashPassword = await bcrypt.hash(
        verifiedUser.email + authConfig.social_default_password,
        10,
      );
      verifiedUser.password = hashPassword;
      verifiedUser.fcmToken = data.fcmToken;

      createdUser = await this.userRepo.createUser(verifiedUser);
      if (!createdUser)
        throw new APIException(
          GoogleUserSignInErrorMessages.SERVER_ERROR,
          'SERVER_ERROR',
          HttpStatus.BAD_REQUEST,
        );
    } else {
      const updateFcm = await this.userRepo.updateUser(user.id, {
        fcmToken: data.fcmToken,
      });
      if (!updateFcm) {
        console.log(
          'Fcm token is not updated after google login!!',
          verifiedUser?.email,
        );
      }
      if (data.fcmToken) {
        // Subscribe user to notification topics
        await this.subscribeUserToTopics(user.id, data.fcmToken);
      }
    }

    const payload: Partial<UserJwtPayload> = {
      id: createdUser ? createdUser.id : user.id,
      email: createdUser ? createdUser.email : user.email,
      logInTime: Date.now(),
      role: 'user',
    };

    // console.log('payload give for to create web token', payload)

    const token = await this.generateToken(payload);

    // console.log("jwt web token",token)
    return this.helper.serviceResponse.successResponse({ token });
  }

  async facebookSignIn(
    data: FacebookSignInRequest,
  ): Promise<FacebookSignInSuccessResponse> {
    // Verify Google Access token
    const verifiedUser = await this.verifyToken(data.accessToken, 'facebook');
    if (!verifiedUser)
      throw new APIException(
        FacebookSignInErrorMessages.NO_FACEBOOK_USER_FOUND,
        'NO_FACEBOOK_USER_FOUND',
        HttpStatus.NOT_FOUND,
      );

    // Check the user email already exists
    const user = await this.userRepo.findUser({
      email: verifiedUser.email,
    });
    let createdUser = null;
    if (!user) {
      // set a default password
      const hashPassword = await bcrypt.hash(
        verifiedUser.email + authConfig.social_default_password,
        10,
      );
      verifiedUser.password = hashPassword;
      verifiedUser.fcmToken = data.fcmToken;
      createdUser = await this.userRepo.createUser(verifiedUser);
      if (!createdUser)
        throw new APIException(
          FacebookSignInErrorMessages.SERVER_ERROR,
          'SERVER_ERROR',
          HttpStatus.BAD_REQUEST,
        );
    } else {
      const updateFcm = await this.userRepo.updateUser(user.id, {
        fcmToken: data.fcmToken,
      });
      if (!updateFcm) {
        console.log(
          'Fcm token is not updated after facebook login!!',
          verifiedUser?.email,
        );
      }
      if (data.fcmToken) {
        // Subscribe user to notification topics
        await this.subscribeUserToTopics(user.id, data.fcmToken);
      }
    }

    const payload: Partial<UserJwtPayload> = {
      id: createdUser ? createdUser.id : user.id,
      email: createdUser ? createdUser.email : user.email,
      logInTime: Date.now(),
      role: 'user',
    };

    const token = await this.generateToken(payload);
    return this.helper.serviceResponse.successResponse({ token });
  }

  private async verifyToken(token: string, type: string) {
    try {
      let rawResponse = null;
      switch (type) {
        case 'google':
          rawResponse = await fetch(
            `${authConfig.google.base_url}?access_token=${token}`,
            {
              method: 'GET',
              headers: {
                Accept: 'application/json',
                'Content-Type': 'application/json',
              },
            },
          );
          rawResponse = await rawResponse?.json();
          break;

        case 'facebook':
          rawResponse = await fetch(
            `${authConfig.facebook.base_url}?access_token=${token}&fields=id,name,email`,
            {
              method: 'GET',
              headers: {
                Accept: 'application/json',
                'Content-Type': 'application/json',
              },
            },
          );
          rawResponse = await rawResponse?.json();
          break;

        case 'apple':
          rawResponse = await appleSignin.verifyIdToken(token, {
            audience: authConfig.apple.clientId, // client id
            ignoreExpiration: true, // Token will not expire unless you manually do so.
          });
          break;

        default:
          break;
      }
      return rawResponse && !rawResponse?.error ? rawResponse : null;
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async appleSignIn(
    data: AppleSignInRequest,
  ): Promise<AppleSignInSuccessResponse> {
    // Verify Apple id token
    const verifiedUser = await this.verifyToken(data.idToken, 'apple');
    if (!verifiedUser)
      throw new APIException(
        AppleSignInErrorMessages.NO_APPLE_USER_FOUND,
        'NO_APPLE_USER_FOUND',
        HttpStatus.NOT_FOUND,
      );

    // Check the user email already exists
    const user = await this.userRepo.findUser({
      email: verifiedUser.email,
    });
    let createdUser = null;
    if (!user) {
      // set a default password
      const hashPassword = await bcrypt.hash(
        verifiedUser.email + authConfig.social_default_password,
        10,
      );
      verifiedUser.password = hashPassword;
      verifiedUser.name = data.name || verifiedUser.email?.split('@')[0];
      verifiedUser.fcmToken = data.fcmToken;
      createdUser = await this.userRepo.createUser(verifiedUser);
      if (!createdUser)
        throw new APIException(
          AppleSignInErrorMessages.SERVER_ERROR,
          'SERVER_ERROR',
          HttpStatus.BAD_REQUEST,
        );
    } else {
      const updateFcm = await this.userRepo.updateUser(user.id, {
        fcmToken: data.fcmToken,
      });
      if (!updateFcm) {
        console.log(
          'Fcm token is not updated after apple login!!',
          verifiedUser?.email,
        );
      }
      if (data.fcmToken) {
        // Subscribe user to notification topics
        await this.subscribeUserToTopics(user.id, data.fcmToken);
      }
    }

    const payload: Partial<UserJwtPayload> = {
      id: createdUser ? createdUser.id : user.id,
      email: createdUser ? createdUser.email : user.email,
      logInTime: Date.now(),
      role: 'user',
    };

    const token = await this.generateToken(payload);
    return this.helper.serviceResponse.successResponse({ token });
  }

  async generateToken(payload: Partial<UserJwtPayload>): Promise<string> {
    let session = await this.userAuthRepository.getUserSession(payload.id);

    // console.log('session value', session)

    if (session) {
      session = await this.userAuthRepository.updateUserSession(
        {
          sessionId: session.sessionId,
        },
        {
          expireAt: new Date(Date.now() + authConfig.session_expire_time),
          sessionId: randomUUID(),
        },
      );
    } else {
      session = await this.userAuthRepository.createUserSession(payload.id);
    }

    if (session?.sessionId) {
      await this.redis.set(
        `USER_SESSION_${payload.id}`,
        JSON.stringify(session),
      );
      return this.jwtService.sign({ ...payload, sessionId: session.sessionId });
    } else {
      throw new APIException(
        SignInErrorMessages.CAN_NOT_CREATE_SESSION,
        'CAN_NOT_CREATE_SESSION',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async logout(userId: string): Promise<IServiceResponse<boolean>> {
    try {
      // Get user's FCM token before removing it for unsubscription
      const user = await this.userRepo.findUser({ id: userId });
      const fcmToken = user?.fcmToken;

      // Remove fcmToken and session data from db and cache
      const session = await this.userAuthRepository.removeUserSessionyAndFcmToken(userId);
      await this.redis.del(`USER_SESSION_${userId}`);

      // Unsubscribe from notification topics if FCM token exists
      if (fcmToken) {
        await this.unsubscribeUserFromTopics(userId, fcmToken);
      }

      return successResponse(null, session ? true : false);
    } catch (error) {
      console.error(`❌ [USER-AUTH] Error during logout for user ${userId}:`, error.message);
      return successResponse(null, false);
    }
  }

  /**
   * Centralized method to handle user unsubscription from notification topics
   * This ensures users are properly unsubscribed when they logout or delete their account
   */
  private async unsubscribeUserFromTopics(userId: string, fcmToken: string): Promise<void> {
    try {
      console.log(`🔕 [USER-AUTH] Unsubscribing user ${userId} from notification topics...`);

      // Unsubscribe from reminder topics using the TopicManagementService
      const success = await this.topicManagementService.unsubscribeUserFromReminders(fcmToken);

      if (success) {
        console.log(`✅ [USER-AUTH] Successfully unsubscribed user ${userId} from reminder topics`);
      } else {
        console.error(`❌ [USER-AUTH] Failed to unsubscribe user ${userId} from reminder topics`);
      }
    } catch (error) {
      console.error(`❌ [USER-AUTH] Error unsubscribing user ${userId} from topics:`, error.message);
    }
  }

  private otpEmailBody(receiverName: string, otp: number): string {
    return `<p>Dear ${receiverName?.split('@')[0].toUpperCase() || 'User'},</p>
    <p>Thank you for using our services! As an added layer of security, we have generated a One-Time Password (OTP) for you to access your fitsomnia account. Please find your OTP details below:</p>
    
    <p>OTP: ${otp}
    
    <p>Please ensure to keep this OTP confidential and do not share it with anyone. It is valid for a limited time.
    If you did not request this OTP or have any concerns about your account's security, please contact our support team <NAME_EMAIL>.</p>
    
    <p>Thank you for your trust and cooperation.</p>
    
    Best regards,<br>
    Support Team<br>
    Fitsomnia<br>`;
  }
}
