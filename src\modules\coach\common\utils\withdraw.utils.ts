import { sharedConfig } from "config/shared";

export function calculateWithdrawSplit(
  amount: number,
  fitsomniaPercentage = sharedConfig.coachWithdrawSplitRatio
): {
  fitsomniaShare: number;
  coachShare: number;
} {
  const fitsomniaShare = parseFloat(((amount * fitsomniaPercentage) / 100).toFixed(2));
  const coachShare = parseFloat((amount - fitsomniaShare).toFixed(2));
  return { fitsomniaShare, coachShare };
}
