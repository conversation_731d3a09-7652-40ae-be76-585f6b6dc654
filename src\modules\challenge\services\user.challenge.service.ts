import { HttpStatus, Injectable } from '@nestjs/common';
import { sharedConfig } from 'config/shared';
import {
  ICreateBaseChallengeRes,
  IErrorUserChallengeEnum,
  IUserChallengeHistoryRes,
  IUserChallengeStatusEnum,
  IUserLeaderboard,
  IUserPointsHistory,
} from 'models';
import {
  ICreateUserChallenge,
  ICreateUserChallengeRes,
} from 'models/user-challenge/userChallenge.interface';
import { APIException } from 'src/internal/exception/api.exception';
import { NotificationHelperService } from 'src/modules/notification/services/helper.notification.service';
import { UserRepository } from 'src/modules/user/repositories';
import { successResponse } from 'src/utils/response';
import { IServiceResponse } from 'src/utils/response/service.response.interface';
import { BaseChallengeRepository } from '../repositories';
import { HelperChallengeRepository } from '../repositories/helper.challenge.repository';
import { UserChallengeRepository } from '../repositories/user.challenge.repository';

@Injectable()
export class UserChallengeService {
  constructor(
    private readonly baseChallengeRepository: BaseChallengeRepository,
    private readonly helperChallengeRepository: HelperChallengeRepository,
    private readonly userChallengeRepository: UserChallengeRepository,
    private readonly userRepository: UserRepository,
    private readonly notificationHelperService: NotificationHelperService,
  ) {}

  async createUserChallenge(
    userId: string,
    challengeId: string,
  ): Promise<IServiceResponse<ICreateUserChallengeRes>> {
    const challenge = await this.baseChallengeRepository.findOne(challengeId);
    if (!challenge) {
      throw new APIException(
        IErrorUserChallengeEnum.NO_CHALLENGE_FOUND,
        'NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }
    // check is already taken
    const isTaken = await this.userChallengeRepository.findOne({
      userId,
      challengeId,
      expireAt: { $gt: Date.now() },
    });
    if (isTaken) {
      throw new APIException(
        IErrorUserChallengeEnum.YOU_HAVE_ALREADY_TAKEN_THIS_CHALLENGE,
        'BAD_REQUEST',
        HttpStatus.BAD_REQUEST,
      );
    }

    const payload: ICreateUserChallenge = {
      userId,
      challengeId,
      points: challenge.points,
      expireAt: challenge.endDate,
      challengeName: challenge.name,
    };
    const userChallenge =
      await this.userChallengeRepository.createUserChallenge(payload);
    if (!userChallenge) {
      throw new APIException(
        IErrorUserChallengeEnum.ERROR_IN_ACCEPTING_NEW_CHALLENGE,
        'BAD_REQUEST',
        HttpStatus.BAD_REQUEST,
      );
    }
    const user = await this.userRepository.findUser({
      id: userId,
    });

    const { name, preview, duration, loop, points } = challenge;
    const result: ICreateUserChallengeRes = {
      ...userChallenge,
      challengeDetails: {
        name,
        preview,
        duration,
        loop,
        points,
      },
    };

    const prevDay = new Date(challenge.endDate);
    prevDay.setDate(prevDay.getDate() - 1);

    // Notification Payload
    // const notificationPayload = {
    //   recipient: user,
    //   createdBy:{
    //     name:'admin',
    //   },
    //   title: 'Challenge ending soon!' ,
    //   content: `Your challenge is ending soon.`,
    //   module: NotificationModule.CHALLENGE,
    //   type: NotificationType.CHALLENGE_ENDING,
    //   date: prevDay,
    //   category: NotificationCategory.CHALLENGE_ENDING
    // };

    // await this.notificationHelperService.sendNotifications(notificationPayload);

    return successResponse(null, result);
  }

  async getChallengeHistory(
    userId: string,
    filterBy: IUserChallengeStatusEnum,
    offset: number,
    limit: number,
  ): Promise<IServiceResponse<IUserChallengeHistoryRes[]>> {
    const history = await this.userChallengeRepository.getUserChallengeHistory(
      userId,
      filterBy,
      offset,
      limit,
    );
    if (!history) {
      return successResponse(null, []);
    }
    return successResponse(null, history ? history : []);
  }

  async getMonthlyChallenge(
    userId: string,
    offset: number,
    limit: number,
  ): Promise<IServiceResponse<ICreateBaseChallengeRes[]>> {
    const today = new Date();
    const firstDayOfMonth = new Date(
      Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), 1),
    );
    const lastDayOfMonth = new Date(
      Date.UTC(today.getUTCFullYear(), today.getUTCMonth() + 1, 0),
    );

    const query1 = {
      userId,
      expireAt: { $gte: firstDayOfMonth, $lte: lastDayOfMonth },
    };
    const acceptedChallengeIds =
      await this.userChallengeRepository.getAcceptedChallengeIds(query1);
    const query2 = {
      isActive: true,
      endDate: { $gte: firstDayOfMonth, $lte: lastDayOfMonth },
      id: { $nin: acceptedChallengeIds },
    };
    const list = await this.userChallengeRepository.getUserMonthlyChallenges(
      query2,
      offset,
      limit,
    );

    if (!list) {
      return successResponse(null, []);
    }
    return successResponse(null, list ? list : []);
  }

  async uploadVideo(
    userId: string,
    id: string,
    videoUrl: string,
  ): Promise<IServiceResponse<string>> {
    const actualVideoUrl = videoUrl || null;
    const uploaded = await this.userChallengeRepository.uploadVideo(
      userId,
      id,
      actualVideoUrl,
    );
    if (!uploaded) {
      throw new APIException(
        IErrorUserChallengeEnum.ERROR_IN_UPLOAD_VIDEO,
        'BAD_REQUEST',
        HttpStatus.BAD_REQUEST,
      );
    }
    return successResponse(null, 'Challenge completed successfully');
  }

  async userTotalPoints(userId: string): Promise<IServiceResponse<number>> {
    const point = await this.userChallengeRepository.getUserTotoalPoint(userId);
    return successResponse(null, point ? point : 0);
  }

  async userPointsHistory(
    userId: string,
    offset: number,
    limit: number,
  ): Promise<IServiceResponse<IUserPointsHistory[]>> {
    limit =
      limit > sharedConfig.defaultMaxLImit ? sharedConfig.defaultLimit : limit;

    const pointHistory =
      await this.userChallengeRepository.getUserPointsHistory(
        userId,
        offset,
        limit,
      );
    return successResponse(null, pointHistory ? pointHistory : []);
  }

  async leaderBoard(
    offset: number,
    limit: number,
  ): Promise<IServiceResponse<IUserLeaderboard[]>> {
    limit =
      limit > sharedConfig.defaultMaxLImit ? sharedConfig.defaultLimit : limit;

    const leaderBoard = await this.userChallengeRepository.leaderBoard(
      offset,
      limit,
    );
    return successResponse(null, leaderBoard ? leaderBoard : []);
  }
}
