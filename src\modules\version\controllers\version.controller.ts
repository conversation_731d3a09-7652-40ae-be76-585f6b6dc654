import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { RolesGuard } from 'src/authentication/guards/auth.guard';
import {
  CreateVersionRequestDto,
  CreateVersionSuccessResponseDto,
  DeleteVersionParamDto,
  DeleteVersionSuccessResponseDto,
  GetVersionSuccessResponseDto,
  UpdateVersionParamDto,
  UpdateVersionRequestDto,
  UpdateVersionSuccessResponseDto
} from '../dto/version.dto';
import { VersionService } from '../services/version.service';

@Controller('version')
@ApiTags('Version API')
export class VersionController {
  constructor(private versionService: VersionService) {}

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Post()
  @ApiResponse({
    description: 'Create Version Response',
    type: CreateVersionSuccessResponseDto,
    status: HttpStatus.CREATED,
  })
  async createVersion(@Body() data: CreateVersionRequestDto) {
    return await this.versionService.createVersion(data);
  }

  @Get('/latest')
  @ApiResponse({
    description: 'Get Latest Version Response',
    type: GetVersionSuccessResponseDto,
    status: HttpStatus.OK,
  })
  async getLatestVersion() {
    return await this.versionService.getLatestVersion();
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Patch('/:versionId')
  @ApiResponse({
    description: 'Update Version Response',
    type: UpdateVersionSuccessResponseDto,
    status: HttpStatus.OK,
  })
  async updateVersion(
    @Param() params: UpdateVersionParamDto,
    @Body() data: UpdateVersionRequestDto,
  ) {
    return await this.versionService.updateVersion(params.versionId, data);
  }

  @UseGuards(new RolesGuard(['admin']))
  @ApiBearerAuth()
  @Delete('/:versionId')
  @ApiResponse({
    description: 'Delete Version Response',
    type: DeleteVersionSuccessResponseDto,
    status: HttpStatus.OK,
  })
  async deleteVersion(@Param() params: DeleteVersionParamDto) {
    return await this.versionService.deleteVersion(params.versionId);
  }
}
