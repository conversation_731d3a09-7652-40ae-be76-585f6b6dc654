import { <PERSON>du<PERSON> } from '@nestjs/common';
import { <PERSON>minder<PERSON>ontroller } from './rest/reminder.controller';
import { ReminderService } from './services/reminder.service';
import { ReminderRepository } from './repositories/reminder.repository';
import { ReminderSchedulerService } from './services/reminder-scheduler.service';
import { SmartReminderSchedulerService } from './services/smart-reminder-scheduler.service';
import { TopicManagementService } from './services/topic-management.service';
import { NotificationModule } from '../notification/notification.module';
import { FcmService } from '../../helper/fcmService';

@Module({
  imports: [NotificationModule],
  controllers: [ReminderController],
  providers: [
    ReminderService,
    ReminderRepository,
    ReminderSchedulerService,
    SmartReminderSchedulerService,
    TopicManagementService,
    FcmService,
  ],
  exports: [ReminderService, TopicManagementService, SmartReminderSchedulerService],
})
export class ReminderModule {}
