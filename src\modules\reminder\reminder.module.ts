import { Modu<PERSON> } from '@nestjs/common';
import { <PERSON>minder<PERSON>ontroller } from './rest/reminder.controller';
import { ReminderService } from './services/reminder.service';
import { ReminderRepository } from './repositories/reminder.repository';
import { ReminderSchedulerService } from './services/reminder-scheduler.service';
import { SmartReminderSchedulerService } from './services/smart-reminder-scheduler.service';
import { TopicSubscriptionService } from './services/topic-subscription.service';
import { NotificationModule } from '../notification/notification.module';
import { FcmService } from '../../helper/fcmService';

@Module({
  imports: [NotificationModule],
  controllers: [ReminderController],
  providers: [
    ReminderService,
    ReminderRepository,
    ReminderSchedulerService,
    SmartReminderSchedulerService,
    TopicSubscriptionService,
    FcmService,
  ],
  exports: [
    ReminderService,
    SmartReminderSchedulerService,
    TopicSubscriptionService
  ],
})
export class ReminderModule {}
