
import { forwardRef, Inject, Injectable } from "@nestjs/common";
import { EventEmitter2 } from '@nestjs/event-emitter';
import { AccountingProvider } from "src/modules/accounting/account/providers/account.provider";
import { AccountingReference, AccountingReferenceType, AccountingTransactionType } from "src/modules/accounting/common/entities/account.entity";
import { CoachSubscriptionPaymentEntity } from "../../common/entities/coach-payment.entity";
import { CoachPayoutPlanEntity } from "../../common/entities/coach-payoutplan.entity";
import { IncomeProfileProviderForInternal } from "../../income-profile/providers/internal.provider";
import { CoachProgramProviderForInternal } from "../../program/providers/internal.provider";
import { SubscriptionProviderForInternal } from "../../subscription/providers/internal.provider";
import { CoachSubscriptionPaymentRepositoryForInternal } from "../repositories/internal.repository";

@Injectable()
export class CoachPaymentProviderForInternal {

  constructor(
    private readonly repository: CoachSubscriptionPaymentRepositoryForInternal,
    @Inject(forwardRef(() => SubscriptionProviderForInternal))
    private readonly subscriptionProvider: SubscriptionProviderForInternal,
    @Inject(forwardRef(() => CoachProgramProviderForInternal))
    private readonly programProvider: CoachProgramProviderForInternal,
    @Inject(forwardRef(() => IncomeProfileProviderForInternal))
    private readonly incomeProfileProvider: IncomeProfileProviderForInternal,
    private eventEmitter: EventEmitter2,
    @Inject(forwardRef(() => AccountingProvider))
    private readonly accountProvider: AccountingProvider,
  ) { }

  /**
   * Since the payment is successfull so we have to perform some actions here.
   * 1. Update the payment status first -> successful
   * 2. Update the subscription status -> firstPaymentDone, isPaymentNeeded etc.
   * 3. Update the program status -> totalEarnings etc.
   * 5. Split amount day wise and save each of them in the database
   * @param paymentId 
   */
  async onBkashPaymentSuccessful(paymentId: string): Promise<void> {
    console.log(`Receiving payment successful callback from bkash module for payment id: ${paymentId}`);
    const paymentEntity = await this.repository.updatePaymentStatusSuccessful(paymentId);
    if (!paymentEntity) {
      // todo - notify the issue tracker or send email to the developer
      // for now we will just log the issue, cause in this function we receive the success callback
      // from bkash module - and the payment id should be there in the database. If it is not
      // that means there is some serious issue here - it should be checked immediately.
      console.log(`No payment entity found for payment id: ${paymentId}. This should be checked!`);
      return;
    }

    // console.log(`Payment entity found: ${JSON.stringify(paymentEntity)}`);
    const subscriptionEntity = await this.subscriptionProvider.onPaymentSuccessful(paymentEntity);
    if (!subscriptionEntity) {
      // todo - notify the issue tracker or send email to the developer
      // for now we will just log the issue, cause in this function we receive the success callback
      // from bkash module - and the subscription entity should be there in the database. If it is not
      // that means there is some serious issue here - it should be checked immediately.
      console.log(`No subscription entity found for payment id: ${paymentId}. This should be checked!`);
      return;
    }

    // console.log(`Subscription entity found: ${JSON.stringify(subscriptionEntity)}`);
    const {id, userId, coachId,subscriptionDate, programId, userTotalPaid} = subscriptionEntity

    const programEntity = await this.programProvider.onPaymentSuccessful(subscriptionEntity.programId, paymentEntity.paymentAmount);
    if (!programEntity) {
      // todo - notify the issue tracker or send email to the developer
      // for now we will just log the issue, cause in this function we receive the success callback
      // from bkash module - and the program entity should be there in the database. If it is not
      // that means there is some serious issue here - it should be checked immediately.
      console.log(`No program entity found for payment id: ${paymentId}. This should be checked!`);
      return;
    }

    // console.log(`Program entity found: ${JSON.stringify(programEntity)}`);

    await this.incomeProfileProvider.onPaymentSuccessful(coachId, userTotalPaid)

    // console.log(`Coach income profile entity found: ${JSON.stringify(incomeProfileEntity)}`);

    // console.log('Listeners for program.subscription:', this.eventEmitter.listenerCount('program.subscription'));

    console.log('event emitter for coach program')

    // Emit the event
    this.eventEmitter.emit('program.subscription', {
      userId,
      programId,
      subscriptionDate
    });
    
    await this.accountProvider.addAccount(
      AccountingReference.COACH,
      AccountingReferenceType.SUBSCRIPTION,
      id,
      AccountingTransactionType.CREDIT,
      userTotalPaid)

    // todo - split the amount day wise and save them in the database
    await this.splitAmount(userId, coachId, subscriptionDate, userTotalPaid)
    // our cron job will automatically pay the coach based on the day wise amount

  }

  async onBkashPaymentFailed(paymentId: string): Promise<void> {
    await this.repository.updatePaymentStatusFailed(paymentId);
    // since the payment is failed, we don't have to do anything
  }

  async onBkashPaymentCancelled(paymentId: string): Promise<void> {
    await this.repository.updatePaymentStatusCanceled(paymentId);
    // since the payment is failed, we don't have to do anything
  }

  async splitAmount(userId: string, coachId: string, subscriptionDate: any, totalAmount: number): Promise<any> {    
    const dailyAmount = parseFloat((totalAmount / 30).toFixed(2));    
    let remaining = totalAmount;
    const payoutSchedule = [];
    
    for (let i = 1; i <= 30; i++) {
      let amount = dailyAmount;
      
      if (i === 30) {
        amount = parseFloat((remaining).toFixed(2)); // Last day gets remainder
      } else {
        remaining -= dailyAmount;
      }

      payoutSchedule.push({
        dayNumber: i,
        amount,
        isPaid: false,
        paidAt: null
      });
    }
    
    // console.log('payout schedule',payoutSchedule)

    const paymentEntity = {
      coachId,
      userId,
      subscriptionDate,
      dailyAmount,
      payoutSchedule,
      daysPaid: 0,
      lastPaidDay: 0,
      isCompleted: false,
      totalAmount: totalAmount
    };
    await this.repository.createPayoutPlan(paymentEntity);
  }

  async getPayoutPlansByCompletionStatus(isCompleted: boolean): Promise<CoachPayoutPlanEntity[]> {
    return this.repository.getPayoutPlansByCompletionStatus(isCompleted);
  }

  async setPayoutPlanCompletedStatusById(planId: string, isCompleted: boolean): Promise<void> {
    await this.repository.updatePayoutPlanCompletedStatusById(planId, isCompleted);
  }

  async getLatestPayment(subscriptionId: string): Promise<CoachSubscriptionPaymentEntity | null> {
    return await this.repository.getLatestPaymentBySubscriptionId(subscriptionId);
  }
  
  async updatePlanAfterPayout(
    planId: string,
    update: Partial<CoachPayoutPlanEntity>
  ): Promise<CoachPayoutPlanEntity | null> {
    return this.repository.updatePayoutPlanFields(planId, update);
  }
  

  async getPaymentById(paymentId: string): Promise<CoachSubscriptionPaymentEntity | null> {
    return await this.repository.getPaymentById(paymentId);
  }
}