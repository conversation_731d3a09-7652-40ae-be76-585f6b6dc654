import { EmailClient } from "@azure/communication-email";
import { Injectable } from '@nestjs/common';
import * as sgMail from '@sendgrid/mail';
import { mailerConfig, nodemailerConfig } from 'config/mail';
import * as nodemailer from 'nodemailer';
import { IMailService, MailOptions, Options } from './mail.service.interface';

@Injectable()
export class MailService implements IMailService {
  private async sendThroughSendgrid(data: any): Promise<boolean | null> {
    sgMail.setApiKey(mailerConfig.sendgridApiKey);
    const sent = await sgMail.send(data);
    return sent ? true : false;
  }

  private async sendThroughACS(data: any): Promise<boolean | null> {
    const emailClient = new EmailClient(mailerConfig.acsConnectionString);
    const sent = await emailClient.beginSend(data);
    return sent ? true : false;
  }

  async sendMail(
    email: string,
    subject: string,
    mailBody: string,
  ): Promise<boolean | null> {
    try {
      if (mailerConfig.emailTransportType === 'ACS') {
        const mailOptions = {
          senderAddress: nodemailerConfig.user,
          content: {
            subject,
            html: mailBody
          },
          recipients: {
            to: [{
              address: email
            }]
          }
        };
        return await this.sendThroughACS(mailOptions);
      } 
      else {
        const transporter: nodemailer.Transporter = nodemailer.createTransport(
          nodemailerConfig.options as MailOptions,
        );
        const mailOptions: Options = {
          from: nodemailerConfig.user,
          to: email,
          subject,
          html: mailBody,
        };
        const res = await transporter.sendMail(mailOptions);
        if (!res) return false;
        return true;
      }
    } catch (error) {
      console.log(error.response);
    }
  }
}
