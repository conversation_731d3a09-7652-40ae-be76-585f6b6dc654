import { model, Schema } from 'mongoose';
import { UserDailyActivity } from 'src/entity/activity';

const UserDailyActivitySchema = new Schema<UserDailyActivity>(
  {
    userId: {
      type: String,
      index: true,
    },
    date: {
      type: String,
      index: true,
    },
    profileViewCount: {
      type: Number,
      default: 0,
    },
    profileViewedIds:{
      type: [String],
      default: []
    },
    spotRequestCount: {
      type: Number,
      default: 0,
    },
    activityDuration: {
      type: Number,
      default: 0,
    },
    activityMetRequirement: {
      type: Boolean,
      default: false,
    },
    activitySessionHistory: [{
      startTime: Date,
      endTime: Date,
      minutes: Number,
    }],
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const UserDailyActivityModel = model<UserDailyActivity>(
  'user-daily-activity',
  UserDailyActivitySchema,
);
export { UserDailyActivityModel };
