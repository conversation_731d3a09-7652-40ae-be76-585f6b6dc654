import { forwardRef, Inject, Injectable } from "@nestjs/common";
import { Helper } from "src/helper/helper.interface";
import { deepCasting } from "src/internal/casting/object.casting";
import { throwConflictErrIf, throwNotFoundErr, throwUnauthorizedErrIf } from "src/internal/exception/api.exception.ext";
import { NO_SUBSCRIPTION_FOUND, SUBSCRIPTION_CANCELLED_ALREADY, SUBSCRIPTION_CANCELLED_BY_COACH, USER_DOES_NOT_OWN_THIS_SUBSCRIPTION, USER_IS_NOT_A_COACH } from "../../common/const/coach.const";
import { CoachSubscriptionCancelStatus } from "../../common/entities/coach-program.entity";
import { UserPaymentProviderForUser } from "../../payment/providers/user.provider";
import { CoachProfileRatingProviderForUser } from "../../profile-rating/providers/user.provider";
import { CoachProgramRatingProviderForUser } from "../../program-rating/providers/user.provider";
import { ProgramProviderForCoach } from "../../program/providers/coach.provider";
import { UserInfoProviderForUser } from "../../user-info/providers/user.provider";
import { VerifiedProfileProviderForCoach } from "../../verified-profile/providers/coach.provider";
import { CancelSubscriptionRequestDtoForCoach } from "../dtos/coach-cancel-subscription.dto";
import { GetSubscribersSuccessResponseDtoForCoach, GetSubscriptionResponseDtoForCoach, GetSubscriptionSuccessResponseDtoForCoach } from "../dtos/coach-get-subscription.dto";
import { GetSubscriptionsResponseDtoForCoach, GetSubscriptionsSuccessResponseDtoForCoach } from "../dtos/coach-get.subscriptions.dto";
import { SubscriptionRepositoryForCoach } from "../repositories/coach.repository";

@Injectable()
export class SubscriptionServiceForCoach {

  constructor(
    private readonly repository: SubscriptionRepositoryForCoach,
    private helper: Helper,
    @Inject(forwardRef(() => UserInfoProviderForUser))
    private readonly userInfoProvider: UserInfoProviderForUser,
    @Inject(forwardRef(() => VerifiedProfileProviderForCoach))
    private readonly verifiedProfileProvider: VerifiedProfileProviderForCoach,
    @Inject(forwardRef(() => ProgramProviderForCoach))
    private readonly programProvider: ProgramProviderForCoach,
    @Inject(forwardRef(() => CoachProgramRatingProviderForUser))
    private readonly programRatingProvider: CoachProgramRatingProviderForUser,
    @Inject(forwardRef(() => CoachProfileRatingProviderForUser))
    private readonly profileRatingProvider: CoachProfileRatingProviderForUser,
    @Inject(forwardRef(() => UserPaymentProviderForUser))
    private readonly paymentProvider: UserPaymentProviderForUser
  ) { }

  async getMultipleSubscription(userId: string, coachId: string, limit: number, offset: number): Promise<GetSubscriptionsSuccessResponseDtoForCoach> {
    const isVerified = await this.verifiedProfileProvider.verifyProfileOwnership(userId, coachId);
    throwConflictErrIf(!isVerified, "you are not a coach", USER_IS_NOT_A_COACH);

    const subscriptions = await this.repository.getSubscriptionsByCoachId(coachId, limit, offset);
    const responseDto = subscriptions.map(subscription => deepCasting(GetSubscriptionsResponseDtoForCoach, subscription));

    const userNameAndImages = await this.userInfoProvider.getUserNamesAndProfileImages(subscriptions.map(sub => sub.userId));
    const coachPrograms = await this.programProvider.getMultipleCoachPrograms(subscriptions.map(sub => sub.programId));
    responseDto.forEach((item, index) => {
      item.userName = userNameAndImages[index].userName;
      item.userImage = userNameAndImages[index].profileImage;
      item.programName = coachPrograms.find(program => program.id === item.programId)?.title || "";
      item.oneTimePrice = coachPrograms.find(program => program.id === item.programId)?.oneTimePrice;
      item.dailyPrice = coachPrograms.find(program => program.id === item.programId)?.dailyPrice;
      item.weeklyPrice = coachPrograms.find(program => program.id === item.programId)?.weeklyPrice;
      item.monthlyPrice = coachPrograms.find(program => program.id === item.programId)?.monthlyPrice;
      const now = new Date();
      item.isCompleted = subscriptions[index].expirationDate 
      ? new Date(subscriptions[index].expirationDate) <= now
      : false;
    });

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getSubscribersPaymentHistory(userId: string, coachId: string, programId: string, limit: number, offset: number): Promise<GetSubscriptionsSuccessResponseDtoForCoach> {
    const isVerified = await this.verifiedProfileProvider.verifyProfileOwnership(userId, coachId);
    throwConflictErrIf(!isVerified, "you are not a coach", USER_IS_NOT_A_COACH);

    const subscriptions = await this.repository.getSubscriptionsPaymentHistory(coachId, programId, limit, offset);
    const responseDto = subscriptions.map(subscription => deepCasting(GetSubscriptionsResponseDtoForCoach, subscription));

    const userNameAndImages = await this.userInfoProvider.getUserNamesAndProfileImages(subscriptions.map(sub => sub.userId));
    const coachPrograms = await this.programProvider.getMultipleCoachPrograms(subscriptions.map(sub => sub.programId));
    responseDto.forEach((item, index) => {
      item.userName = userNameAndImages[index].userName;
      item.userImage = userNameAndImages[index].profileImage;
      item.programName = coachPrograms.find(program => program.id === item.programId)?.title || "";
      item.oneTimePrice = coachPrograms.find(program => program.id === item.programId)?.oneTimePrice;
      item.dailyPrice = coachPrograms.find(program => program.id === item.programId)?.dailyPrice;
      item.weeklyPrice = coachPrograms.find(program => program.id === item.programId)?.weeklyPrice;
      item.monthlyPrice = coachPrograms.find(program => program.id === item.programId)?.monthlyPrice;
      const now = new Date();
      item.isCompleted = subscriptions[index].expirationDate 
      ? new Date(subscriptions[index].expirationDate) <= now
      : false;
    });

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getSingleSubscription(userId: string, coachId: string, subscriptionId: string): Promise<GetSubscriptionSuccessResponseDtoForCoach> {
    const isVerified = await this.verifiedProfileProvider.verifyProfileOwnership(userId, coachId);
    throwConflictErrIf(!isVerified, "you are not a coach", USER_IS_NOT_A_COACH);

    const subscription = await this.repository.getSubscriptionById(subscriptionId);
    throwNotFoundErr(!subscription, "Subscription not found", NO_SUBSCRIPTION_FOUND);

    const invalidOwnership = subscription.coachId !== coachId;
    throwUnauthorizedErrIf(invalidOwnership, "You are not the owner of this subscription", USER_DOES_NOT_OWN_THIS_SUBSCRIPTION);

    const responseDto = deepCasting(GetSubscriptionResponseDtoForCoach, subscription);

    const now = new Date();
    responseDto.isCompleted = subscription.expirationDate ? new Date(subscription.expirationDate) <= now : false;

    const userNameAndProfile = await this.userInfoProvider.getUserNameAndProfileImage(subscription.userId);
    responseDto.userName = userNameAndProfile.userName;
    responseDto.userImage = userNameAndProfile.profileImage;

    const programInfo = await this.programProvider.getSingleCoachProgram(subscription.programId);
    const programRatingEntity =
      await this.programRatingProvider.getProgramRatingOfUser(
        subscription.userId,
        subscription.programId,
      );
    const coachRatingEntity =
      await this.profileRatingProvider.getProfileRatingOfUser(
        subscription.userId,
        subscription.coachId,
      );
    responseDto.programName = programInfo.title;
    responseDto.oneTimePrice = programInfo.oneTimePrice;
    responseDto.dailyPrice = programInfo.dailyPrice;
    responseDto.weeklyPrice = programInfo.weeklyPrice;
    responseDto.monthlyPrice = programInfo.monthlyPrice;

    responseDto.coachRatingId = coachRatingEntity?.id ?? null;
    responseDto.programRatingId = programRatingEntity?.id ?? null;

    const paymentTerm = await this.paymentProvider.getPaymentTermOfUser(subscription.userId, subscriptionId)
    responseDto.paymentTerm=paymentTerm


    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getSubscribers(
  userId: string,
  coachId: string,
  programId: string
  ): Promise<GetSubscribersSuccessResponseDtoForCoach
  > {
  const isVerified = await this.verifiedProfileProvider.verifyProfileOwnership(userId, coachId);
  throwConflictErrIf(!isVerified, "you are not a coach", USER_IS_NOT_A_COACH);

  const subscriptions = await this.repository.getSubscriptionsByCoachAndProgram(coachId, programId);
  throwNotFoundErr(!subscriptions || subscriptions.length === 0, "No subscribers found", NO_SUBSCRIPTION_FOUND);

  const responseList = await Promise.all(
    subscriptions.map(async (subscription) => {
      const invalidOwnership = subscription.coachId !== coachId;
      throwUnauthorizedErrIf(invalidOwnership, "You are not the owner of this subscription", USER_DOES_NOT_OWN_THIS_SUBSCRIPTION);

      const responseDto = deepCasting(GetSubscriptionResponseDtoForCoach, subscription);

      // Fetch all required info in parallel for each user
      const [
        userInfo,
        programInfo,
        programRatingEntity,
        coachRatingEntity,
      ] = await Promise.all([
        this.userInfoProvider.getUserNameAndProfileImage(subscription.userId),
        this.programProvider.getSingleCoachProgram(subscription.programId),
        this.programRatingProvider.getProgramRatingOfUser(subscription.userId, subscription.programId),
        this.profileRatingProvider.getProfileRatingOfUser(subscription.userId, subscription.coachId),
      ]);

      responseDto.userName = userInfo.userName;
      responseDto.userImage = userInfo.profileImage;

      responseDto.programName = programInfo.title;
      responseDto.oneTimePrice = programInfo.oneTimePrice;
      responseDto.dailyPrice = programInfo.dailyPrice;
      responseDto.weeklyPrice = programInfo.weeklyPrice;
      responseDto.monthlyPrice = programInfo.monthlyPrice;

      responseDto.coachRatingId = coachRatingEntity?.id ?? null;
      responseDto.programRatingId = programRatingEntity?.id ?? null;

      return responseDto;
    })
  );

  return this.helper.serviceResponse.successResponse(responseList);
}


  async cancelSubscription(userId: string, coachId: string, subscriptionId: string, reqDto: CancelSubscriptionRequestDtoForCoach): Promise<GetSubscriptionSuccessResponseDtoForCoach> {
    const isVerified = await this.verifiedProfileProvider.verifyProfileOwnership(userId, coachId);
    throwConflictErrIf(!isVerified, "you are not a coach", USER_IS_NOT_A_COACH);

    let subscription = await this.repository.getSubscriptionById(subscriptionId);
    throwNotFoundErr(!subscription, "Subscription not found", NO_SUBSCRIPTION_FOUND);

    const invalidOwnership = subscription.coachId !== coachId;
    throwUnauthorizedErrIf(invalidOwnership, "You are not the owner of this subscription", USER_DOES_NOT_OWN_THIS_SUBSCRIPTION);

    // const isAlreadyCanceled = subscription.cancelStatus !== null;
    // throwConflictErrIf(isAlreadyCanceled, "Subscription is already canceled", SUBSCRIPTION_CANCELLED_ALREADY);

    const cancelByUser = subscription.cancelStatus === CoachSubscriptionCancelStatus.CanceledByUser;
    throwConflictErrIf(cancelByUser, 'Subscription already canceled by user', SUBSCRIPTION_CANCELLED_ALREADY);
        
    const cancelByCoach = subscription.cancelStatus === CoachSubscriptionCancelStatus.CanceledByCoach
    throwConflictErrIf(cancelByCoach,'Subscription already canceled by coach', SUBSCRIPTION_CANCELLED_BY_COACH);

    subscription = await this.repository.cancelSubscriptionTriggeredByCoach(subscriptionId, reqDto.reason);
    const responseDto = deepCasting(GetSubscriptionResponseDtoForCoach, subscription);

    const userNameAndProfile = await this.userInfoProvider.getUserNameAndProfileImage(subscription.userId);
    responseDto.userName = userNameAndProfile.userName;
    responseDto.userImage = userNameAndProfile.profileImage;

    const programInfo = await this.programProvider.getSingleCoachProgram(subscription.programId);
    responseDto.programName = programInfo.title;
    responseDto.oneTimePrice = programInfo.oneTimePrice;
    responseDto.dailyPrice = programInfo.dailyPrice;
    responseDto.weeklyPrice = programInfo.weeklyPrice;
    responseDto.monthlyPrice = programInfo.monthlyPrice;

    return this.helper.serviceResponse.successResponse(responseDto);
  }

}