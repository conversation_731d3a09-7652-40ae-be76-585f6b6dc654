import { Modu<PERSON> } from '@nestjs/common';
import { NotificationHelperService } from '../notification/services/helper.notification.service';
import { PreferenceModule } from '../preference/preference.rest.module';
import { TaskModule } from '../task/task.module';
import { PostRepository } from './repository';
import { PostHelperRepository } from './repository/post.helper.repository';
import { PostController } from './rest';
import { PostService } from './services';

@Module({
  imports: [PreferenceModule, TaskModule],
  controllers: [PostController],
  providers: [
    PostService,
    PostRepository,
    PostHelperRepository,
    NotificationHelperService,
  ],
})
export class PostModule {}
