import { Injectable } from "@nestjs/common";
import { CoachIncomeProfileEntityModel } from "../../common/db/coach-profile.model";
import { CoachReWithdrawEntityModel } from "../../common/db/coach-withdraw.model";
import { CoachWithdrawEntity, CoachWithdrawPaymentStatus } from "../../common/entities/coach-withdraw.entity";

@Injectable()
export class CoachWithdrawableRepositoryForInternal { 

  async updateWithdrawPaymentStatusSuccessful(
    requestId: string,
  ): Promise<CoachWithdrawEntity | null> {
    return await CoachReWithdrawEntityModel.findOneAndUpdate(
			{ id: requestId },
			{ paymentStatus: CoachWithdrawPaymentStatus.Successfull },
			{ new: true },
    ).lean();
  }

  async updateWithdrawPaymentStatusFailed(
    requestId: string,
  ): Promise<CoachWithdrawEntity | null> {
    return await CoachReWithdrawEntityModel.findOneAndUpdate(
      { id: requestId },
      { paymentStatus: CoachWithdrawPaymentStatus.Failed },
      { new: true },
    ).lean();
  }

	async updateCoachWithdrawableAmount(coachId: string, amount: number) {
		return await CoachIncomeProfileEntityModel.findOneAndUpdate(
			{ coachId },
			{
				$inc: {
					withdrawableAmount: -amount,
					totalWithdrawn: amount,
				},
			},
			{ new: true }
		).exec();
	}

}