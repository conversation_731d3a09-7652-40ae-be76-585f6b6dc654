import { Injectable } from '@nestjs/common';
import { IAzureBlobStorageService } from './azureBlobService/azureBlobStorage.service.interface';
import { IFcmService } from './fcmService/fcm.service.interface';
import { Helper } from './helper.interface';
import { IMailService } from './mailService/mail.service.interface';
import { IS3FileUploadService } from './s3Service/s3.service.interface';
import { IServiceResponse } from './serviceResponse/service.response.interface';
import { ISmsServive } from './smsService/sms.service.interface';
@Injectable()
export class HelperService implements Helper {
  constructor(
    public serviceResponse: IServiceResponse,
    public mailService: IMailService,
    public fcmService: IFcmService,
    public s3FileUploadService: IS3FileUploadService,
    public smsService: ISmsServive,
    public azureBlobStorageService: IAzureBlobStorageService,
  ) {}
}
