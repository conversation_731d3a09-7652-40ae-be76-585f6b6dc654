import { Injectable } from "@nestjs/common";
import { sharedConfig } from "config/shared";
import { throwBadReqErrIf, throwNotFoundErr } from "src/internal/exception/api.exception.ext";
import { INCOME_PROFILE_NOT_FOUND, WITHDRAW_AMOUNT_BELOW_MINIMUM, WITHDRAW_AMOUNT_EXCEEDS_BALANCE } from "../../common/const/coach.const";
import { CoachIncomeProfileEntity } from "../../common/entities/coach-profile.entity";
import { IncomeProfileRepositoryForCoach } from "../repositories/coach.repository";

@Injectable()
export class IncomeProfileProviderForCoach {

  constructor(private readonly repository: IncomeProfileRepositoryForCoach) { }

  /**
   * As the name says this function will return the Income profile of the coach.
   * However there could be corner cases where the coach has not yet created or
   * the invokered passed an invalid coach id. In such cases this function will
   * return null. However if everything works fine then it will return the Income
   * profile of the coach. But whatever the case is, the invoker should handle
   * the null case.
   * 
   * @param coachId the corresponding coach id for which the profile is needed
   * @returns full coach Income profile for the corresponding coach id
   */
  async getIncomeProfile(coachId: string): Promise<CoachIncomeProfileEntity | null> {
    return await this.repository.getIncomeProfile(coachId);
  }

  async updateIncomeProfile(coachId: string, withdrawableAmount:number): Promise<CoachIncomeProfileEntity | null> {
    return await this.repository.updateIncomeProfile(coachId,withdrawableAmount);
  }

  async withdrawAmountIfPossible(coachId: string, amount: number): Promise<CoachIncomeProfileEntity> {
    const profile = await this.getIncomeProfile(coachId);
    throwNotFoundErr(!profile, 'No Income Profile found with the ID', INCOME_PROFILE_NOT_FOUND);
    
    if (profile.withdrawableAmount < sharedConfig.coachMinimumWithdrawAmount) {
    throwBadReqErrIf(true, 'You need at least 1000 to withdraw.', WITHDRAW_AMOUNT_BELOW_MINIMUM);
  }

    if (amount > profile.withdrawableAmount) {
      throwBadReqErrIf(true, 'You cannot withdraw more than your available balance.', WITHDRAW_AMOUNT_EXCEEDS_BALANCE);
    }
    return profile;
  }
  
  async restoreWithdrawableAmount(coachId: string, amount: number): Promise<CoachIncomeProfileEntity> {
    const profile = await this.getIncomeProfile(coachId);

    throwNotFoundErr(!profile, 'No Income Profile found with the ID', INCOME_PROFILE_NOT_FOUND);

    const updatedProfile = await this.repository.restoreWithdraw(coachId, amount);
    return updatedProfile;
  }
}