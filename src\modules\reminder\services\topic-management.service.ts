import { Injectable } from '@nestjs/common';
import { FcmService } from 'src/helper/fcmService';
import { NotificationHelperService } from 'src/modules/notification/services/helper.notification.service';

@Injectable()
export class TopicManagementService {
  constructor(
    private readonly fcmService: FcmService,
    private readonly notificationHelperService: NotificationHelperService,
  ) {}

  /**
   * Subscribe user tokens to the all-reminders topic
   * This should be called when users register or login
   */
  async subscribeToReminders(tokens: string | string[]): Promise<boolean> {
    try {
      await this.fcmService.subscribeNotificationTopic(tokens, 'all-reminders');
      const tokenCount = Array.isArray(tokens) ? tokens.length : 1;
      console.log(`✅ [TOPIC-SUBSCRIPTION] Successfully subscribed ${tokenCount} token(s) to 'all-reminders' topic`);
      return true;
    } catch (error) {
      console.error('❌ [TOPIC-SUBSCRIPTION] Failed to subscribe to reminders topic:', error.message);
      return false;
    }
  }

  /**
   * Unsubscribe user tokens from the all-reminders topic
   * This should be called when users logout or delete account
   */
  async unsubscribeFromReminders(tokens: string | string[]): Promise<boolean> {
    try {
      await this.fcmService.unsubscribeNotificationTopic(tokens, 'all-reminders');
      const tokenCount = Array.isArray(tokens) ? tokens.length : 1;
      console.log(`✅ [TOPIC-UNSUBSCRIPTION] Successfully unsubscribed ${tokenCount} token(s) from 'all-reminders' topic`);
      return true;
    } catch (error) {
      console.error('❌ [TOPIC-UNSUBSCRIPTION] Failed to unsubscribe from reminders topic:', error.message);
      return false;
    }
  }

  /**
   * Bulk subscribe all active users to reminders topic
   * This can be used for initial setup or migration
   */
  async bulkSubscribeActiveUsers(): Promise<{ success: boolean; subscribedCount: number; totalUsers: number }> {
    try {
      console.log('🔄 [BULK-SUBSCRIPTION] Starting bulk subscription to reminders topic...');

      // Get all active users with FCM tokens
      const activeUsers = await this.notificationHelperService.getAllActiveUsers();
      const tokens = activeUsers
        .map(user => user.fcmToken)
        .filter(token => token && token.trim() !== '');

      if (tokens.length === 0) {
        console.log('⚠️ [BULK-SUBSCRIPTION] No active users with FCM tokens found');
        return { success: true, subscribedCount: 0, totalUsers: activeUsers.length };
      }

      // Subscribe in batches to avoid overwhelming FCM
      const batchSize = 1000; // FCM allows up to 1000 tokens per batch
      let subscribedCount = 0;

      for (let i = 0; i < tokens.length; i += batchSize) {
        const batch = tokens.slice(i, i + batchSize);
        const success = await this.subscribeToReminders(batch);
        if (success) {
          subscribedCount += batch.length;
        }

        // Add a small delay between batches to avoid rate limiting
        if (i + batchSize < tokens.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      console.log(`✅ [BULK-SUBSCRIPTION] Completed: ${subscribedCount}/${tokens.length} tokens subscribed to 'all-reminders' topic`);
      return {
        success: subscribedCount === tokens.length,
        subscribedCount,
        totalUsers: activeUsers.length
      };
    } catch (error) {
      console.error('❌ [BULK-SUBSCRIPTION] Failed to bulk subscribe users to reminders topic:', error.message);
      return { success: false, subscribedCount: 0, totalUsers: 0 };
    }
  }

  /**
   * Subscribe a single user token to reminders topic
   * Convenience method for single user subscription
   */
  async subscribeUserToReminders(fcmToken: string): Promise<boolean> {
    if (!fcmToken || fcmToken.trim() === '') {
      console.warn('⚠️ [TOPIC-SUBSCRIPTION] Empty FCM token provided');
      return false;
    }
    return this.subscribeToReminders(fcmToken);
  }

  /**
   * Unsubscribe a single user token from reminders topic
   * Convenience method for single user unsubscription
   */
  async unsubscribeUserFromReminders(fcmToken: string): Promise<boolean> {
    if (!fcmToken || fcmToken.trim() === '') {
      console.warn('⚠️ [TOPIC-UNSUBSCRIPTION] Empty FCM token provided');
      return false;
    }
    return this.unsubscribeFromReminders(fcmToken);
  }

  /**
   * Get statistics about active users and their subscription status
   */
  async getSubscriptionStats(): Promise<{ totalActiveUsers: number; usersWithTokens: number }> {
    try {
      const activeUsers = await this.notificationHelperService.getAllActiveUsers();
      const usersWithTokens = activeUsers.filter(user => user.fcmToken && user.fcmToken.trim() !== '').length;

      return {
        totalActiveUsers: activeUsers.length,
        usersWithTokens,
      };
    } catch (error) {
      console.error('❌ [SUBSCRIPTION-STATS] Failed to get subscription stats:', error.message);
      return { totalActiveUsers: 0, usersWithTokens: 0 };
    }
  }

  /**
   * Health check method to verify topic messaging is working
   */
  async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; message: string }> {
    try {
      // Try to get user stats as a basic health check
      const stats = await this.getSubscriptionStats();

      if (stats.totalActiveUsers >= 0) {
        return {
          status: 'healthy',
          message: `Topic management service is healthy. ${stats.usersWithTokens} users with FCM tokens out of ${stats.totalActiveUsers} active users.`
        };
      } else {
        return {
          status: 'unhealthy',
          message: 'Unable to retrieve user statistics'
        };
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        message: `Health check failed: ${error.message}`
      };
    }
  }
}
