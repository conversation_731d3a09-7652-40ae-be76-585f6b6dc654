import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsObject, IsString } from 'class-validator';
import { ServiceSuccessResponse } from 'src/helper/serviceResponse/service.response.interface';

export class GetWithdrawInfoResponseDtoForAdmin {
  @Expose()
  @ApiProperty({ required: true, description: 'Payment ID' })
  @IsNotEmpty()
  @IsString()
  id: string;

  @Expose()
  @ApiProperty({
    required: true,
    type: String,
    description: 'The mobile number of the coach',
  })
  @IsString()
  @IsNotEmpty()
  mobileNumber: string;

  @Expose()
  @ApiProperty({
    required: true,
    type: Number,
    description: 'Money to be withdrawn by Coach',
  })
  // @IsNumber()
  @IsNotEmpty()
  moneyWithdraw: number;

  @Expose()
  @ApiProperty({ required: false, description: 'Coach ID' })
  @IsNotEmpty()
  @IsString()
  coachId: string;

  @Expose()
  @ApiProperty({
    required: true,
    type: String,
    description: 'The status of the withdraw request',
  })
  reviewStatus: string;

  @Expose()
  @ApiProperty({
    required: false,
    type: String,
    description: 'The comment given by the reviewer',
  })
  reviewComment?: string;
  
  @Expose()
  @ApiProperty({
    required: false,
    type: String,
    description: 'The Coach Name ',
  })
  coachName?: string;

  @Expose()
  @ApiProperty({
    required: false,
    type: Number,
    description: 'The Coach Share',
  })
  coachShare: number;

  @Expose()
  @ApiProperty({
    required: false,
    type: Number,
    description: 'The Fitsomnia Share',
  })
  fitsomniaShare: number;
}

export class GetWithdrawInfoSuccessResponseDtoForAdmin
  implements ServiceSuccessResponse
{
  @Expose()
  @ApiProperty({
    required: true,
    type: GetWithdrawInfoResponseDtoForAdmin,
    description: 'The response data',
  })
  @IsObject()
  data: GetWithdrawInfoResponseDtoForAdmin;
}
