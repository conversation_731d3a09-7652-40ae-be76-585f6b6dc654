import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler';
import { PrometheusModule } from '@willsoto/nestjs-prometheus';
import { redisCacheConfig } from 'config/cache';
import { ThrottlerStorageRedisService } from 'nestjs-throttler-storage-redis';
import { HelperModule } from './helper/helper.module';
import { ResolveRestModule } from './internal/rest/rest.module.resolver';
import { ResolveQueueModule } from './queue-system/queue.module';

@Module({
  imports: [
    PrometheusModule.registerAsync({
      useFactory: () => ({
        defaultMetrics: {
          enabled: true,
        },
      }),
    }),
    HelperModule,
    ...ResolveRestModule(),
    ...ResolveQueueModule(),
    ScheduleModule.forRoot(),
    ThrottlerModule.forRoot({
      ttl: redisCacheConfig.throttler_ttl,
      limit: redisCacheConfig.throttler_limit,
      storage: new ThrottlerStorageRedisService(redisCacheConfig.redis_url),
    }),
    EventEmitterModule.forRoot({
      // Global configuration options
      wildcard: true,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 40,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),
  ],
  providers: [
    {
      provide: 'APP_GUARD',
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule {}
