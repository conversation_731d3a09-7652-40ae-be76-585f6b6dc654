import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TopicManagementService } from 'src/modules/reminder/services/topic-management.service';
import { ReminderService } from 'src/modules/reminder/services/reminder.service';
import { DailyReminderSenderQueue } from 'src/queue-system/worker/sender/reminder/daily-reminder';
import { FcmServiceHelper } from 'src/helper/fcmService';
import { NotificationHelperService } from 'src/modules/notification/services/helper.notification.service';
import { UserAuthService } from 'src/modules/user-auth/services';

// Mock Firebase Admin to avoid requiring actual Firebase credentials
jest.mock('firebase-admin', () => ({
  messaging: jest.fn(() => ({
    sendToTopic: jest.fn(),
    subscribeToTopic: jest.fn(),
    unsubscribeFromTopic: jest.fn(),
  })),
}));

describe('Topic Messaging Integration (e2e)', () => {
  let app: INestApplication;
  let topicManagementService: TopicManagementService;
  let reminderService: ReminderService;
  let fcmServiceHelper: FcmServiceHelper;
  let notificationHelperService: NotificationHelperService;
  let userAuthService: UserAuthService;

  const mockUsers = [
    { id: 'user1', fcmToken: 'token1', email: '<EMAIL>', isActive: true },
    { id: 'user2', fcmToken: 'token2', email: '<EMAIL>', isActive: true },
    { id: 'user3', fcmToken: 'token3', email: '<EMAIL>', isActive: true },
  ];

  beforeAll(async () => {
    // Create a minimal test module with mocked dependencies
    const moduleFixture: TestingModule = await Test.createTestingModule({
      providers: [
        TopicManagementService,
        ReminderService,
        FcmServiceHelper,
        {
          provide: NotificationHelperService,
          useValue: {
            getAllActiveUsers: jest.fn().mockResolvedValue(mockUsers),
          },
        },
        {
          provide: UserAuthService,
          useValue: {
            verifyFcmToken: jest.fn(),
            logout: jest.fn(),
          },
        },
        // Add other required dependencies as mocks
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    topicManagementService = moduleFixture.get<TopicManagementService>(TopicManagementService);
    reminderService = moduleFixture.get<ReminderService>(ReminderService);
    fcmServiceHelper = moduleFixture.get<FcmServiceHelper>(FcmServiceHelper);
    notificationHelperService = moduleFixture.get<NotificationHelperService>(NotificationHelperService);
    userAuthService = moduleFixture.get<UserAuthService>(UserAuthService);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('End-to-End Topic Messaging Flow', () => {
    it('should complete the full reminder broadcasting workflow', async () => {
      // Step 1: Subscribe users to reminder topics
      console.log('🔔 Step 1: Subscribing users to reminder topics...');
      
      // Mock FCM subscription success
      jest.spyOn(fcmServiceHelper, 'subscribeNotificationTopic').mockResolvedValue(true);
      
      const subscriptionResult = await topicManagementService.bulkSubscribeAllUsersToReminders();
      
      expect(subscriptionResult.success).toBe(true);
      expect(subscriptionResult.subscribedCount).toBe(3);
      expect(subscriptionResult.totalUsers).toBe(3);
      
      console.log('✅ Step 1 completed: Users subscribed to topics');

      // Step 2: Create and queue a reminder notification
      console.log('📤 Step 2: Creating and queuing reminder notification...');
      
      // Mock FCM topic messaging success
      jest.spyOn(fcmServiceHelper, 'sendToTopic').mockResolvedValue(true);
      
      const reminderPayload = {
        title: 'Daily Workout Reminder',
        body: 'Time for your daily workout! Stay consistent with your fitness goals.',
        task: 'topic-sender' as const,
        topic: 'all-reminders',
        documentId: 'reminder-123',
        data: { 
          type: 'reminder', 
          id: 'reminder-123', 
          category: 'workout',
          timestamp: new Date().toISOString()
        },
        isHighPriority: true,
      };
      
      // Simulate the queue worker processing the reminder
      await DailyReminderSenderQueue.sender(reminderPayload);
      
      expect(fcmServiceHelper.sendToTopic).toHaveBeenCalledWith({
        title: 'Daily Workout Reminder',
        body: 'Time for your daily workout! Stay consistent with your fitness goals.',
        documentId: 'reminder-123',
        data: { 
          type: 'reminder', 
          id: 'reminder-123', 
          category: 'workout',
          timestamp: expect.any(String)
        },
        topic: 'all-reminders',
        isHighPriority: true,
        ttl: 3600,
      });
      
      console.log('✅ Step 2 completed: Reminder notification sent to topic');

      // Step 3: Verify subscription statistics
      console.log('📊 Step 3: Verifying subscription statistics...');
      
      const stats = await topicManagementService.getSubscriptionStats();
      
      expect(stats.totalActiveUsers).toBe(3);
      expect(stats.usersWithTokens).toBe(3);
      
      console.log('✅ Step 3 completed: Subscription statistics verified');

      // Step 4: Test health check
      console.log('🏥 Step 4: Performing health check...');
      
      const healthCheck = await topicManagementService.healthCheck();
      
      expect(healthCheck.status).toBe('healthy');
      expect(healthCheck.message).toContain('3 users with FCM tokens out of 3 active users');
      
      console.log('✅ Step 4 completed: Health check passed');

      console.log('🎉 End-to-end topic messaging flow completed successfully!');
    });

    it('should handle user authentication topic subscription flow', async () => {
      console.log('🔐 Testing user authentication topic subscription flow...');
      
      // Mock successful subscription
      jest.spyOn(topicManagementService, 'subscribeUserToReminders').mockResolvedValue(true);
      jest.spyOn(topicManagementService, 'unsubscribeUserFromReminders').mockResolvedValue(true);
      
      // Step 1: User updates FCM token (simulating login/signup)
      const newFcmToken = 'new-user-token-456';
      
      // This would normally be called during user authentication
      const subscriptionSuccess = await topicManagementService.subscribeUserToReminders(newFcmToken);
      
      expect(subscriptionSuccess).toBe(true);
      expect(topicManagementService.subscribeUserToReminders).toHaveBeenCalledWith(newFcmToken);
      
      console.log('✅ User successfully subscribed to topics during authentication');
      
      // Step 2: User logs out (simulating logout)
      const unsubscriptionSuccess = await topicManagementService.unsubscribeUserFromReminders(newFcmToken);
      
      expect(unsubscriptionSuccess).toBe(true);
      expect(topicManagementService.unsubscribeUserFromReminders).toHaveBeenCalledWith(newFcmToken);
      
      console.log('✅ User successfully unsubscribed from topics during logout');
      
      console.log('🎉 User authentication topic subscription flow completed successfully!');
    });

    it('should handle error scenarios gracefully', async () => {
      console.log('⚠️ Testing error handling scenarios...');
      
      // Test FCM service failure
      jest.spyOn(fcmServiceHelper, 'sendToTopic').mockRejectedValue(new Error('FCM service unavailable'));
      
      const failedPayload = {
        title: 'Test Reminder',
        body: 'This should fail gracefully',
        task: 'topic-sender' as const,
        topic: 'all-reminders',
        documentId: 'test-reminder',
        data: { type: 'test' },
      };
      
      // Should not throw an error
      await expect(DailyReminderSenderQueue.sender(failedPayload)).resolves.not.toThrow();
      
      console.log('✅ FCM service failure handled gracefully');
      
      // Test subscription failure
      jest.spyOn(fcmServiceHelper, 'subscribeNotificationTopic').mockResolvedValue(false);
      
      const subscriptionResult = await topicManagementService.subscribeUserToReminders('test-token');
      
      expect(subscriptionResult).toBe(false);
      
      console.log('✅ Subscription failure handled gracefully');
      
      // Test database failure
      jest.spyOn(notificationHelperService, 'getAllActiveUsers').mockRejectedValue(new Error('Database error'));
      
      const bulkResult = await topicManagementService.bulkSubscribeAllUsersToReminders();
      
      expect(bulkResult.success).toBe(false);
      expect(bulkResult.subscribedCount).toBe(0);
      expect(bulkResult.totalUsers).toBe(0);
      
      console.log('✅ Database failure handled gracefully');
      
      console.log('🎉 Error handling scenarios completed successfully!');
    });

    it('should validate notification message structure for mobile devices', async () => {
      console.log('📱 Testing mobile notification message structure...');
      
      // Mock FCM to capture the message structure
      const mockSendToTopic = jest.spyOn(fcmServiceHelper, 'sendToTopic').mockResolvedValue(true);
      
      const mobileOptimizedPayload = {
        title: 'Workout Reminder',
        body: 'Your 30-minute HIIT session starts in 10 minutes!',
        task: 'topic-sender' as const,
        topic: 'all-reminders',
        documentId: 'workout-reminder-789',
        data: { 
          type: 'workout_reminder',
          workoutId: 'hiit-30min',
          startTime: '2024-01-15T14:00:00Z',
          duration: '30'
        },
        isHighPriority: true,
      };
      
      await DailyReminderSenderQueue.sender(mobileOptimizedPayload);
      
      // Verify the message was sent with proper mobile configuration
      expect(mockSendToTopic).toHaveBeenCalledWith({
        title: 'Workout Reminder',
        body: 'Your 30-minute HIIT session starts in 10 minutes!',
        documentId: 'workout-reminder-789',
        data: { 
          type: 'workout_reminder',
          workoutId: 'hiit-30min',
          startTime: '2024-01-15T14:00:00Z',
          duration: '30'
        },
        topic: 'all-reminders',
        isHighPriority: true,
        ttl: 3600,
      });
      
      console.log('✅ Mobile notification message structure validated');
      
      console.log('🎉 Mobile notification validation completed successfully!');
    });
  });
});
