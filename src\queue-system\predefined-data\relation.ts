import { NotificationQueueName, QueuePayloadType } from '.';

export const QueueRelation = {
  STORY_VIEW: {
    generator: {
      type: QueuePayloadType.STORY_VIEW_GENERATOR,
      name: NotificationQueueName.STORY_VIEW_GENERATOR_QUEUE,
    },
    sender: {
      type: QueuePayloadType.STORY_VIEW_SENDER,
      name: NotificationQueueName.STORY_VIEW_SENDER_QUEUE,
    },
  },
  POST_COMMENT: {
    generator: {
      type: QueuePayloadType.POST_COMMENT_GENERATOR,
      name: NotificationQueueName.POST_COMMENT_GENERATOR_QUEUE,
    },
    sender: {
      type: QueuePayloadType.POST_COMMENT_SENDER,
      name: NotificationQueueName.POST_COMMENT_SENDER_QUEUE,
    },
  },
  POST_REACTION: {
    generator: {
      type: QueuePayloadType.POST_REACTION_GENERATOR,
      name: NotificationQueueName.POST_REACTION_GENERATOR_QUEUE,
    },
    sender: {
      type: QueuePayloadType.POST_REACTION_SENDER,
      name: NotificationQueueName.POST_REACTION_SENDER_QUEUE,
    },
  },
  SPOT_REQUEST_ACCEPT: {
    generator: {
      type: QueuePayloadType.SPOT_REQUEST_ACCEPT_GENERATOR,
      name: NotificationQueueName.SPOT_REQUEST_ACCEPT_GENERATOR_QUEUE,
    },
    sender: {
      type: QueuePayloadType.SPOT_REQUEST_ACCEPT_SENDER,
      name: NotificationQueueName.SPOT_REQUEST_ACCEPT_SENDER_QUEUE,
    },
  },
  SPOT_REQUEST: {
    generator: {
      type: QueuePayloadType.SPOT_REQUEST_GENERATOR,
      name: NotificationQueueName.SPOT_REQUEST_GENERATOR_QUEUE,
    },
    sender: {
      type: QueuePayloadType.SPOT_REQUEST_SENDER,
      name: NotificationQueueName.SPOT_REQUEST_SENDER_QUEUE,
    },
  },
  DIET_ENDING: {
    generator: {
      type: QueuePayloadType.DIET_ENDING_GENERATOR,
      name: NotificationQueueName.DIET_ENDING_GENERATOR_QUEUE,
    },
    sender: {
      type: QueuePayloadType.DIET_ENDING_SENDER,
      name: NotificationQueueName.DIET_ENDING_SENDER_QUEUE,
    },
  },
  CHALLENGE_ENDING: {
    generator: {
      type: QueuePayloadType.CHALLENGE_ENDING_GENERATOR,
      name: NotificationQueueName.CHALLENGE_ENDING_GENERATOR_QUEUE,
    },
    sender: {
      type: QueuePayloadType.CHALLENGE_ENDING_SENDER,
      name: NotificationQueueName.CHALLENGE_ENDING_SENDER_QUEUE,
    },
  },
  CHALLENGE_STATUS: {
    generator: {
      type: QueuePayloadType.CHALLENGE_STATUS_GENERATOR,
      name: NotificationQueueName.CHALLENGE_STATUS_GENERATOR_QUEUE,
    },
    sender: {
      type: QueuePayloadType.CHALLENGE_STATUS_SENDER,
      name: NotificationQueueName.CHALLENGE_STATUS_SENDER_QUEUE,
    },
  },
  ORDER_STATUS: {
    generator: {
      type: QueuePayloadType.ORDER_STATUS_GENERATOR,
      name: NotificationQueueName.ORDER_STATUS_GENERATOR_QUEUE,
    },
    sender: {
      type: QueuePayloadType.ORDER_STATUS_SENDER,
      name: NotificationQueueName.ORDER_STATUS_SENDER_QUEUE,
    },
  },
  ONE_TO_ONE_CHAT: {
    generator: {
      type: QueuePayloadType.ONE_TO_ONE_CHAT_GENERATOR,
      name: NotificationQueueName.ONE_TO_ONE_CHAT_GENERATOR_QUEUE,
    },
    sender: {
      type: QueuePayloadType.ONE_TO_ONE_CHAT_SENDER,
      name: NotificationQueueName.ONE_TO_ONE_CHAT_SENDER_QUEUE,
    },
  },
  GROUP_CHAT: {
    generator: {
      type: QueuePayloadType.GROUP_CHAT_GENERATOR,
      name: NotificationQueueName.GROUP_CHAT_GENERATOR_QUEUE,
    },
    sender: {
      type: QueuePayloadType.GROUP_CHAT_SENDER,
      name: NotificationQueueName.GROUP_CHAT_SENDER_QUEUE,
    },
  },
  DAILY_REMINDER: {
    generator: {
      type: QueuePayloadType.DAILY_REMINDER_GENERATOR,
      name: NotificationQueueName.DAILY_REMINDER_GENERATOR_QUEUE,
    },
    sender: {
      type: QueuePayloadType.DAILY_REMINDER_SENDER,
      name: NotificationQueueName.DAILY_REMINDER_SENDER_QUEUE,
    },
  },
};
