import { Module } from '@nestjs/common';
import { NotificationHelperService } from '../notification/services/helper.notification.service';
import { UserPointsModule } from '../user-points/user-points.module';
import { UserRepository } from '../user/repositories';
import { BaseChallengeRepository } from './repositories';
import { HelperChallengeRepository } from './repositories/helper.challenge.repository';
import { UserChallengeRepository } from './repositories/user.challenge.repository';
import { AdminChallengeController } from './rest/admin.challenge.controller';
import { UserChallengeController } from './rest/user.challenge.controller';
import { AdminChallengeService } from './services/admin.challenge.service';
import { ChallengeApprovalService } from './services/challenge-approval.service';
import { UserChallengeService } from './services/user.challenge.service';

@Module({
  imports: [UserPointsModule],
  controllers: [UserChallengeController, AdminChallengeController],
  providers: [
    AdminChallengeService,
    UserChallengeService,
    HelperChallengeRepository,
    BaseChallengeRepository,
    UserChallengeRepository,
    UserRepository,
    NotificationHelperService,
    ChallengeApprovalService
  ],
})
export class ChallengeModule {}
