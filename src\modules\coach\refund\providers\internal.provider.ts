import { forwardRef, Inject, Injectable } from "@nestjs/common";
import { shallowCasting } from "src/internal/casting/object.casting";
import { AccountingProvider } from "src/modules/accounting/account/providers/account.provider";
import { AccountingReference, AccountingReferenceType, AccountingTransactionType } from "src/modules/accounting/common/entities/account.entity";
import { NotificationType } from "../../common/const/notfication.const";
import { CoachRefundEntity, CoachRefundReviewStatus } from "../../common/entities/coach-refund.entity";
import { IncomeProfileProviderForInternal } from "../../income-profile/providers/internal.provider";
import { EmailProvider } from "../../notification/providers/email.provider";
import { CoachPaymentProviderForInternal } from "../../payment/providers/internal.provider";
import { CoachProgramProviderForInternal } from "../../program/providers/internal.provider";
import { SubscriptionProviderForAdmin } from "../../subscription/providers/admin.provider";
import { UserInfoProviderForUser } from "../../user-info/providers/user.provider";
import { RefundRepositoryForAdmin } from "../repositories/admin.repository";

@Injectable()
export class CoachRefundProviderForInternal {

  constructor(
		@Inject(forwardRef(() => CoachPaymentProviderForInternal))
		private readonly paymentProvider: CoachPaymentProviderForInternal,
		@Inject(forwardRef(() => SubscriptionProviderForAdmin))
		private readonly subscriptionProvider: SubscriptionProviderForAdmin,
		@Inject(forwardRef(() => AccountingProvider))
		private readonly accountProvider: AccountingProvider,
		@Inject(forwardRef(() => RefundRepositoryForAdmin))
		private readonly repository: RefundRepositoryForAdmin,
		@Inject(forwardRef(() => EmailProvider))
		private readonly emailProvider: EmailProvider,
		@Inject(forwardRef(() => UserInfoProviderForUser))
		private readonly userInfoProvider: UserInfoProviderForUser,
		@Inject(forwardRef(() => IncomeProfileProviderForInternal))
		private readonly incomeProfileProvider: IncomeProfileProviderForInternal,
		@Inject(forwardRef(() => CoachProgramProviderForInternal))
		private readonly programProvider: CoachProgramProviderForInternal,
  ) {}

  /**
   * Updates the refund status of a subscription to SUCCESSFUL after a successful refund from bKash
   * @param paymentId The id of the payment to refund
   */
  async onBkashRefundSuccessful(paymentId: string): Promise<void> {
    console.log(`Refund successful operation started for payment id: ${paymentId}`);

    // Get the payment entity
    const paymentEntity = await this.paymentProvider.getPaymentById(paymentId)

    // Update the refund status of the subscription
    const updatedRefundInfo = shallowCasting(CoachRefundEntity, { reviewStatus: CoachRefundReviewStatus.SUCCESS });
    const newRefundInfo = await this.repository.updateRefundInfoBySubscriptionId(paymentEntity.subscriptionId, updatedRefundInfo)
    console.log(`Subscription id: ${paymentEntity.subscriptionId} refund status changed to successful`);

    // Set the refund status of the subscription to successful
    await this.subscriptionProvider.setRefundStatusToSuccessful(newRefundInfo.subscriptionId);

    // Update the income profile of the coach
    await this.incomeProfileProvider.onRefundSuccessful(newRefundInfo.coachId, paymentEntity.paymentAmount);

    // Update the program of the subscription
    await this.programProvider.onRefundSuccessful(newRefundInfo.programId, paymentEntity.paymentAmount);

    // Send an email notification to the user
    const userName = await this.userInfoProvider.getUserName(newRefundInfo.userId)
    if (userName && newRefundInfo.contactEmail?.trim()) {
      console.log(`Sending email notification to the user: ${userName} with email: ${newRefundInfo.contactEmail}`);
      await this.emailProvider.sendEmailNotification(NotificationType.SUCCESS_REFUND, {
        name: userName,
        email: newRefundInfo.contactEmail,
      });
    }

    // Debit the amount from the account
    console.log(`Debiting the amount from the account: ${paymentEntity.paymentAmount}`);
    await this.accountProvider.addAccount(
      AccountingReference.COACH,
      AccountingReferenceType.REFUND,
      paymentEntity.id,
      AccountingTransactionType.DEBIT,
      paymentEntity.paymentAmount
    )
  }

  async onBkashRefundFailed(paymentId: string): Promise<void> {
    const paymentEntity = await this.paymentProvider.getPaymentById(paymentId)
    const updatedRefundInfo = shallowCasting(CoachRefundEntity, { reviewStatus: CoachRefundReviewStatus.FAILED });
    const newRefundInfo = await this.repository.updateRefundInfoBySubscriptionId(paymentEntity.subscriptionId, updatedRefundInfo)
    await this.subscriptionProvider.setRefundStatusToFailed(newRefundInfo.subscriptionId);
    const userName = await this.userInfoProvider.getUserName(newRefundInfo.userId)
    if (userName && newRefundInfo.contactEmail?.trim()) {
      await this.emailProvider.sendEmailNotification(NotificationType.FAILED_REFUND, {
        name: userName,
        email: newRefundInfo.contactEmail,
      });
    }
  }
}