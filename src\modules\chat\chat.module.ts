import { RedisModule } from '@nestjs-modules/ioredis';
import { Module, forwardRef } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { redisCacheConfig } from 'config/cache';
import { RealtimeService } from 'src/modules/chat/realtime/realtime.service';
import { RedisDBService } from 'src/modules/chat/realtime/redis/redis.db.service';
import { SocketGateway } from 'src/modules/chat/realtime/socket.gateway';
import { SocketHelperService } from 'src/modules/chat/realtime/socket.helper';
import { UserActivityStreakModule } from 'src/modules/user-activity-streak/user-activity-streak.module';
import { NotificationHelperService } from '../notification/services/helper.notification.service';
import { ChatHelperRepository, OneToOneChatRepository } from './repositories';
import { ChatGroupInfoRepository } from './repositories/chatGroupInfo.repository';
import { GroupChatRepository } from './repositories/groupChat.repository';
import { GroupChatController, OneToOneChatController } from './rest';
import {
  ChatHelperService,
  GroupChatService,
  OneToOneChatService,
} from './services';
import { ChatGroupInfoService } from './services/chatGroupInfo.service';

@Module({
  imports: [
    RedisModule.forRoot({
      config: {
        url: redisCacheConfig.redis_url,
      },
    }),
    forwardRef(() => UserActivityStreakModule),
  ],
  controllers: [OneToOneChatController, GroupChatController],
  providers: [
    SocketHelperService,
    RealtimeService,
    OneToOneChatService,
    ChatHelperService,
    OneToOneChatRepository,
    ChatHelperRepository,
    GroupChatRepository,
    GroupChatService,
    ChatGroupInfoRepository,
    ChatGroupInfoService,
    SocketGateway,
    JwtService,
    RedisDBService,
    NotificationHelperService
  ],
  exports: [RedisDBService],
})
export class ChatModule {}
