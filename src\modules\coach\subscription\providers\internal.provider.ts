import { forwardRef, Inject, Injectable } from "@nestjs/common";
import { CoachSubscriptionPaymentEntity } from "../../common/entities/coach-payment.entity";
import { CoachSubscriptionEntity } from "../../common/entities/coach-program.entity";
import { ProgramProviderForUser } from "../../program/providers/user.provider";
import { VerifiedProfileProviderForUser } from "../../verified-profile/providers/user.provider";
import { SubscriptionRepositoryForInternal } from "../repositories/internal.repository";

@Injectable()
export class SubscriptionProviderForInternal {

  constructor(
    private readonly repository: SubscriptionRepositoryForInternal,
    @Inject(forwardRef(() => VerifiedProfileProviderForUser))
    private readonly verifiedProfileProvider: VerifiedProfileProviderForUser,
    @Inject(forwardRef(() => ProgramProviderForUser))
    private readonly programProvider: ProgramProviderForUser,
  ) { }

  async deactivateExpiredSubscriptions(): Promise<number> {
    return await this.repository.deactivateExpiredSubscriptions();
  }

  async markSubscriptionsAsPaymentDue(): Promise<number> {
    return await this.repository.markSubscriptionsAsPaymentDue();
  }

  async onPaymentSuccessful(paymentEntity: CoachSubscriptionPaymentEntity): Promise<CoachSubscriptionEntity> {
    const payment = await this.repository.onPaymentSuccessful(paymentEntity);
    if (payment) {
      const programProvider = await this.programProvider.incrementTotalSubscriptionCountByOne(payment.programId);
      const profileProvider = await this.verifiedProfileProvider.incrementTotalSubscriptionCountByOne(payment.coachId);
    }
    return payment;
  }

}