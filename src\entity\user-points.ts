import { IUserPointsSources, IUserPointsTransTypeEnum } from "models/user-points/user-points.interface";

export class UserPoints {
  id?: string;
  userId: string;
  points: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export class UserPointsTransHistory {
  id?: string;
  userId: string;
  type: IUserPointsTransTypeEnum;
  pointSourceType?: IUserPointsSources;
  pointSourceId?: string;
  pointSourceName?: string;
  points: number;
  createdAt?: Date;
  updatedAt?: Date;
}

