import { Injectable } from "@nestjs/common";
import { CoachWithdrawableRepositoryForInternal } from "../repositories/internal.repository";

@Injectable()
export class CoachWithdrawProviderForInternal { 
	constructor(
		private readonly repository: CoachWithdrawableRepositoryForInternal,
	) {}
	
	async onWithdrawSuccess(requestId: string): Promise<void> {
		const updatedWithdraw = await this.repository.updateWithdrawPaymentStatusSuccessful(requestId);
		await this.repository.updateCoachWithdrawableAmount(updatedWithdraw.coachId, updatedWithdraw.moneyWithdraw);
	}

	async onWithdrawFailed(requestId: string): Promise<void> {
		await this.repository.updateWithdrawPaymentStatusFailed(requestId);
	}
}