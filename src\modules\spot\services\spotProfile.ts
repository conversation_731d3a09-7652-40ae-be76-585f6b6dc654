import { HttpStatus, Injectable } from '@nestjs/common';
import { clubConfig } from 'config/club';
import { sharedConfig } from 'config/shared';
import {
  ClubMemberStatus,
  CreateSpotProfileErrorMessages,
  CreateSpotProfileRequestBody,
  CreateSpotProfileSuccessResponse,
  DeleteSpotProfileErrorMessages,
  DeleteSpotProfileSuccessMessages,
  DeleteSpotProfileSuccessResponse,
  FindNearestUserListErrorMessages,
  FindNearestUserListQuery,
  FindNearestUserListSuccessResponse,
  GenderEnum,
  GetSpotProfileErrorMessages,
  GetSpotProfileSuccessResponse,
  LeaveAndJoinClubErrorMessages,
  PackageFeatureEnum,
  SpotProfileImage,
  UpdateSpotProfileErrorMessages,
  UpdateSpotProfileRequestBody,
  UpdateSpotProfileSuccessResponse,
  UpdateUserLocationErrorMessages,
} from 'models';
import {
  SpotNotErrorMessages,
  SpotNotSuccessResponse,
} from 'models/spot/spotNot';
import { Helper } from 'src/helper/helper.interface';
import { APIException } from 'src/internal/exception/api.exception';
import { ClubRepository } from 'src/modules/club/repositories';
import { UserDailyActivityService } from 'src/modules/package/services/user.dailyActivity.service';
import { TaskProgressService } from 'src/modules/task/task-progress.service';
import { UserRepository } from 'src/modules/user/repositories';
import { RedisDBService } from '../../chat/realtime/redis/redis.db.service';
import { SpotProfileRepository } from '../repositories/spotProfile';
import { SpotProfileAddressHelperService } from './spotProfile.helper';

@Injectable()
export class SpotProfileService {
  constructor(
    private spotProfileRepo: SpotProfileRepository,
    private userRepo: UserRepository,
    private clubRepo: ClubRepository,
    private helper: Helper,
    private readonly userDailyActivityService: UserDailyActivityService,
    private spotProfileAddressHelperService: SpotProfileAddressHelperService,
    private readonly redisDBService: RedisDBService,
    private readonly taskProgressService: TaskProgressService,
  ) {}

  async nearestUserList(
    userId: string,
    condition: FindNearestUserListQuery,
  ): Promise<FindNearestUserListSuccessResponse> {
    const nearestUserList = await this.spotProfileRepo.findNearestUserList(
      userId,
      condition,
    );

    // console.log('nearest user list', nearestUserList)

    const updatedUserList = nearestUserList.map(async (user) => {
      const latLng = {
        lat: user.location.coordinates[1], // Latitude is at index 1
        lng: user.location.coordinates[0], // Longitude is at index 0
      };

      // console.log('user',user)

      // Find nearest district for each user
      const userAddress =
        this.spotProfileAddressHelperService.findNearestDistrict(latLng);

      const userInfo = await this.redisDBService.getUserInfo(user.userId);
      const isOnline = userInfo ? userInfo.isOnline : false;
      // console.log('isOnline',isOnline)

      return {
        ...user,
        address: userAddress,
        isOnline,
      };
    });

    const finalUserList = await Promise.all(updatedUserList);

    if (!nearestUserList)
      throw new APIException(
        FindNearestUserListErrorMessages.NEAREST_USER_NOT_FOUND,
        'NEAREST_USER_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );

    return this.helper.serviceResponse.successResponse(finalUserList);
  }

  async nearestUserClubList(
    userId: string,
    condition: FindNearestUserListQuery,
  ): Promise<FindNearestUserListSuccessResponse> {
    const nearestUserList = await this.spotProfileRepo.findNearestUsersClub(
      userId,
      condition,
    );
    if (!nearestUserList)
      throw new APIException(
        FindNearestUserListErrorMessages.NEAREST_USER_NOT_FOUND,
        'NEAREST_USER_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );

    return this.helper.serviceResponse.successResponse(nearestUserList);
  }

  async createSpotProfile(
    userId: string,
    data: CreateSpotProfileRequestBody,
  ): Promise<CreateSpotProfileSuccessResponse> {
    const userInfo = await this.userRepo.findUser({ id: userId });

    if (!userInfo) {
      throw new APIException(
        CreateSpotProfileErrorMessages.USER_NOT_FOUND,
        'USER_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    let age = null;

    if (userInfo?.dateOfBirth) {
      const today = new Date();
      const birthDate = new Date(userInfo?.dateOfBirth);
      age = today.getFullYear() - birthDate.getFullYear();
    }

    const profileExists = await this.spotProfileRepo.getSpotProfileByUserId(
      userId,
    );

    const spotProfile = {
      ...data,
      location: userInfo?.location,
      age: age,
      userId: userId,
      name: userInfo?.name,
      interest: data?.interest,
      gender: data?.gender.toLowerCase() as GenderEnum,
      isDeleted: false,
    };

    let newSpotProfile = null;

    let club = null;

    if (spotProfile?.clubId) {
      club = await this.clubRepo.getClub({ id: spotProfile?.clubId });
      if (!club) {
        throw new APIException(
          LeaveAndJoinClubErrorMessages.NO_CLUB_EXIST,
          'NO_CLUB_EXIST',
          HttpStatus.NOT_FOUND,
        );
      }
    }

    if (profileExists) {
      if (profileExists?.isDeleted == true) {
        newSpotProfile = await this.spotProfileRepo.updateSpotProfileByUserId(
          userId,
          spotProfile,
        );
      } else {
        throw new APIException(
          CreateSpotProfileErrorMessages.PROFILE_ALREADY_EXISTED,
          'PROFILE_ALREADY_EXISTED',
          HttpStatus.BAD_REQUEST,
        );
      }
    } else {
      newSpotProfile = await this.spotProfileRepo.createSpotProfile(
        spotProfile,
      );
      await this.taskProgressService.trackSpotProfileCreation(userId);
    }

    if (newSpotProfile?.clubId) {
      const doesExistMember = await this.clubRepo.getClubMember({
        userId: userId,
      });

      if (doesExistMember) {
        await this.clubRepo.updateClubMember(userId, newSpotProfile?.clubId);
      } else {
        const ClubMember = {
          userId,
          userName: newSpotProfile?.name,
          clubId: newSpotProfile?.clubId,
          status: ClubMemberStatus.ACTIVE,
          expireDate: new Date().setDate(
            new Date().getDate() + clubConfig.clubMemberExpiredDate,
          ),
        };
        await this.clubRepo.joinClub(ClubMember);
      }
    }

    // console.log('newSpotProfile',newSpotProfile)

    return this.helper.serviceResponse.successResponse(newSpotProfile);
  }

  async findSpotProfileByUserId(
    viewerId: string,
    userId: string,
  ): Promise<GetSpotProfileSuccessResponse> {
    // checking subscription
    if (viewerId !== userId) {
      const handlePackage = await this.userDailyActivityService.verifyPackage(
        viewerId,
        PackageFeatureEnum.PROFILE_VIEW,
        userId,
      );
    }
    const profile = await this.spotProfileRepo.getSpotProfileByUserId(userId);

    if (!profile || profile?.isDeleted == true) {
      throw new APIException(
        GetSpotProfileErrorMessages.PROFILE_NOT_FOUND,
        'PROFILE_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    if (profile.location) {
      const latLng = {
        lat: profile.location.coordinates[1],
        lng: profile.location.coordinates[0],
      };

      // collect user address

      const userAddress =
        this.spotProfileAddressHelperService.findNearestDistrict(latLng);

      (profile.address as any) = userAddress;
    }

    if (profile) {
      //  collect user online stattus
      const userInfo = await this.redisDBService.getUserInfo(userId);
      // console.log('user info', userInfo.isOnline)
      const isOnline = userInfo ? userInfo?.isOnline : false;
      (profile.isOline as any) = isOnline;
    }

    return this.helper.serviceResponse.successResponse(profile);
  }

  async updateSpotProfile(
    userId: string,
    data: UpdateSpotProfileRequestBody,
  ): Promise<UpdateSpotProfileSuccessResponse> {
    const profile = await this.spotProfileRepo.getSpotProfileByUserId(userId);

    if (!profile) {
      throw new APIException(
        UpdateSpotProfileErrorMessages.PROFILE_NOT_FOUND,
        'PROFILE_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    if (data?.clubId) {
      const club = await this.clubRepo.getClub({ id: data?.clubId });
      if (!club) {
        throw new APIException(
          LeaveAndJoinClubErrorMessages.NO_CLUB_EXIST,
          'NO_CLUB_EXIST',
          HttpStatus.NOT_FOUND,
        );
      }
    }

    if (
      data?.location &&
      (!data.location.coordinates[0] || !data.location.coordinates[1])
    ) {
      throw new APIException(
        UpdateUserLocationErrorMessages.INVALID_LOCATION_POINTS,
        'INVALID_LOCATION_POINTS',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (data?.gender) {
      data.gender = data.gender.toLowerCase();
    }
    const updatedSpotProfile =
      await this.spotProfileRepo.updateSpotProfileByUserId(userId, data);

    if (updatedSpotProfile?.clubId) {
      const doesExistMember = await this.clubRepo.getClubMember({
        userId: userId,
      });

      if (doesExistMember) {
        await this.clubRepo.updateClubMember(
          userId,
          updatedSpotProfile?.clubId,
        );
      } else {
        const ClubMember = {
          userId,
          userName: updatedSpotProfile?.name,
          clubId: updatedSpotProfile?.clubId,
          status: ClubMemberStatus.ACTIVE,
          expireDate: new Date().setDate(
            new Date().getDate() + clubConfig.clubMemberExpiredDate,
          ),
        };
        await this.clubRepo.joinClub(ClubMember);
      }
    }

    return this.helper.serviceResponse.successResponse(updatedSpotProfile);
  }

  async deleteSpotProfile(
    userId: string,
  ): Promise<DeleteSpotProfileSuccessResponse> {
    const profile = await this.spotProfileRepo.getSpotProfileByUserId(userId);

    if (!profile) {
      throw new APIException(
        DeleteSpotProfileErrorMessages.SPOT_PROFILE_NOT_FOUND,
        'SPOT_PROFILE_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const spotProfile = {
      ...profile,
      isDeleted: true,
    };

    const deleteProfile = await this.spotProfileRepo.updateSpotProfileByUserId(
      userId,
      spotProfile,
    );

    if (!deleteProfile) {
      throw new APIException(
        DeleteSpotProfileErrorMessages.CAN_NOT_DELELE_SPOT_PROFILE,
        'CAN_NOT_DELELE_SPOT_PROFILE',
        HttpStatus.BAD_REQUEST,
      );
    }

    return this.helper.serviceResponse.successResponse({
      message:
        DeleteSpotProfileSuccessMessages.SPOT_PROFILE_DELETED_SUCCESSFULLY,
    });
  }

  async addImageToSpotProfile(
    userId: string,
    url: string,
  ): Promise<UpdateSpotProfileSuccessResponse> {
    const profile = await this.spotProfileRepo.getSpotProfileByUserId(userId);

    if (!profile || profile?.isDeleted == true) {
      throw new APIException(
        UpdateSpotProfileErrorMessages.PROFILE_NOT_FOUND,
        'PROFILE_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const updatedSpotProfile = await this.spotProfileRepo.addSpotProfileImage(
      userId,
      url,
    );

    return this.helper.serviceResponse.successResponse(updatedSpotProfile);
  }

  async removeImageFromSpotProfile(
    userId: string,
    imageId: string,
  ): Promise<UpdateSpotProfileSuccessResponse> {
    const profile = await this.spotProfileRepo.getSpotProfileByUserId(userId);

    if (!profile || profile?.isDeleted == true) {
      throw new APIException(
        UpdateSpotProfileErrorMessages.PROFILE_NOT_FOUND,
        'PROFILE_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const imageExists = await this.spotProfileRepo.findSpotProfileImage(
      userId,
      imageId,
    );

    if (!imageExists) {
      throw new APIException(
        UpdateSpotProfileErrorMessages.IMAGE_NOT_FOUND,
        'IMAGE_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const updatedSpotProfile =
      await this.spotProfileRepo.deleteSpotProfileImage(userId, imageId);

    return this.helper.serviceResponse.successResponse(updatedSpotProfile);
  }

  async updateSpotProfileImage(
    userId: string,
    imageId: string,
    image: SpotProfileImage,
  ): Promise<UpdateSpotProfileSuccessResponse> {
    const profile = await this.spotProfileRepo.getSpotProfileByUserId(userId);

    if (!profile || profile?.isDeleted == true) {
      throw new APIException(
        UpdateSpotProfileErrorMessages.PROFILE_NOT_FOUND,
        'PROFILE_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const imageExists = await this.spotProfileRepo.findSpotProfileImage(
      userId,
      imageId,
    );

    if (!imageExists) {
      throw new APIException(
        UpdateSpotProfileErrorMessages.IMAGE_NOT_FOUND,
        'IMAGE_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const updatedImage: SpotProfileImage = {
      id: imageId,
      url: image.url,
    };

    const updatedSpotProfile =
      await this.spotProfileRepo.updateSpotProfileImage(userId, updatedImage);

    return this.helper.serviceResponse.successResponse(updatedSpotProfile);
  }

  async spotNotUser(
    userId: string,
    notUserId: string, // userId of the user whom the logged in user intends to spotNot(spotNot==don't suggest)
  ): Promise<SpotNotSuccessResponse> {
    const loggedInprofile = await this.spotProfileRepo.getSpotProfileByUserId(
      userId,
    );

    // if (!loggedInprofile || loggedInprofile?.isDeleted == true) {
    //   throw new APIException(
    //     SpotNotErrorMessages.LOGGED_IN_SPOT_PROFILE_NOT_EXIST,
    //     'LOGGED_IN_SPOT_PROFILE_NOT_EXIST',
    //     HttpStatus.NOT_FOUND,
    //   );
    // }

    const notSpotprofile = await this.spotProfileRepo.getSpotProfileByUserId(
      notUserId,
    );
    if (!notSpotprofile) {
      throw new APIException(
        SpotNotErrorMessages.SPOT_PROFILE_NOT_EXIST,
        'SPOT_PROFILE_NOT_EXIST',
        HttpStatus.NOT_FOUND,
      );
    }

    if (loggedInprofile) {
      // filter out all item excepts the non exipired and save them
      const filteredItems = loggedInprofile?.spotNot?.filter(
        (e) => e.userId !== notUserId && e.expireDate >= new Date(),
      );

      const expireDate = new Date(
        Date.now() + sharedConfig.spotNotExipresAfterInMs,
      );
      const spotNot = await this.spotProfileRepo.spotNotUser(
        { userId },
        {
          spotNot: [{ userId: notUserId, expireDate }, ...filteredItems],
        },
      );

      if (!spotNot) {
        throw new APIException(
          SpotNotErrorMessages.CAN_NOT_SPOT_NOT,
          'CAN_NOT_SPOT_NOT',
          HttpStatus.BAD_REQUEST,
        );
      }
    }
    const spotnotpayload = {
      id: userId,
      requesterId: userId,
      recipientId: notUserId,
    };

    // return this.helper.serviceResponse.successResponse({
    //   message: SpotNotSuccessMessages.SPOT_NOT_SUCCESSFULLY,
    // });
    return this.helper.serviceResponse.successResponse(spotnotpayload);
  }
}
