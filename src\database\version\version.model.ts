import { model, Schema } from 'mongoose';
import { Version } from 'src/entity/version';

const VersionSchema = new Schema<Version>(
  {
    id: {
      type: String,
      unique: true,
    },
    version: {
      type: String,
      required: true,
    },
    forceUpdate: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const VersionModel = model<Version>('versions', VersionSchema);
export { VersionModel };
