import { forwardRef, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { sharedConfig } from 'config/shared';
import { IUpdatePointsList, IUserLeadersboard, IUserPoints } from 'models';
import { UserPointsTransHistory } from 'src/entity/user-points';
import { APIException } from 'src/internal/exception/api.exception';
import { PointRedemptionProvider } from 'src/modules/point-redemption/providers/point-redemption.provider';
import { successResponse } from 'src/utils/response';
import { IServiceResponse } from 'src/utils/response/service.response.interface';
import { UserPointsRepository } from '../repositories';

@Injectable()
export class UserPointsService {
  constructor(
    private readonly userPointsRepository: UserPointsRepository,

    @Inject(forwardRef(() => PointRedemptionProvider))
    private readonly pointRedemptionProvider: PointRedemptionProvider,
  ) {}

  async myPoints(userId: string): Promise<IServiceResponse<IUserPoints>> {
    const points = await this.userPointsRepository.getUserPointsWithRanking(
      userId,
    );
    if (!points) {
      throw new APIException(
        'You have no points',
        'NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const conversionRateConfig = await this.pointRedemptionProvider.getActiveConfig();
    if (conversionRateConfig) {
      points.conversionRate = conversionRateConfig.conversionRate;
      points.amountToWithdraw =
        points.points * conversionRateConfig.conversionRate;
    }
    return successResponse(null, points);
  }

  async updatePoints(
    userId: string,
    data: IUpdatePointsList,
  ): Promise<IUserPoints> {
    return await this.userPointsRepository.updatePoints(userId, data);
  }

  async updateReferrerAndReferralPoints(
    referrerId: string,
    referralId: string,
    referrerPoints: number,
    referralPoints: number,
    pointSourceType: any,
    pointSourceId: string,
    pointSourceName: string,
  ): Promise<void> {
    await this.userPointsRepository.updateReferrerAndReferralPoints(
      referrerId,
      referralId,
      referrerPoints,
      referralPoints,
      pointSourceType,
      pointSourceId,
      pointSourceName,
    );
  }

  // internal use
  async getPointsByUserId(userId: string): Promise<IUserPoints> {
    return await this.userPointsRepository.getPointsByUserId(userId);
  }

  async leaderBoard(
    offset: number,
    limit: number,
  ): Promise<IServiceResponse<IUserLeadersboard[]>> {
    limit =
      limit > sharedConfig.defaultMaxLImit ? sharedConfig.defaultLimit : limit;

    const leaderBoard = await this.userPointsRepository.leaderBoard(
      offset,
      limit,
    );
    return successResponse(null, leaderBoard ? leaderBoard : []);
  }
  async userPointsHistory(
    userId: string,
    offset: number,
    limit: number,
    filter = 'all',
  ): Promise<IServiceResponse<UserPointsTransHistory[]>> {
    limit = limit > sharedConfig.defaultMaxLImit ? sharedConfig.defaultLimit : limit;

    const pointHistory = await this.userPointsRepository.getUserPointsHistory(
      userId,
      offset,
      limit,
      filter,
    );
    return successResponse(null, pointHistory ? pointHistory : []);
  }
}
