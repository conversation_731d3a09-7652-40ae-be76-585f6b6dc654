import { HttpStatus, Injectable } from '@nestjs/common';
import { sharedConfig } from 'config/shared';
import {
  ICreateStory,
  ICreateStoryReq,
  ICreateStoryRes,
  IDeleteStoryRes,
  IFetchStoryRes,
  IGetStoryRes,
  IStoryViewerList,
  NotificationCategory,
  NotificationModule,
  NotificationType,
  StoryErrorEnum,
} from 'models';
import { Helper } from 'src/helper/helper.interface';
import { APIException } from 'src/internal/exception/api.exception';
import { NotificationHelperService } from 'src/modules/notification/services/helper.notification.service';
import { UserRepository } from 'src/modules/user/repositories';
import { successResponse } from 'src/utils/response';
import {
  IServiceResponse,
  IServiceSuccessResponse,
} from 'src/utils/response/service.response.interface';
import { StoryRepository } from '../repository';
import { StoryHelperRepository } from '../repository/story.helper.repository';

@Injectable()
export class StoryService {
  constructor(
    private readonly storyRepository: StoryRepository,
    private readonly userRepository: UserRepository,
    private readonly storyHelperRepository: StoryHelperRepository,
    private readonly notificationHelperService: NotificationHelperService,
    private readonly helperService: Helper,
  ) {}

  /**
   * Create new story
   * @param {string} userId
   * @param {ICreateStoryReq} body
   * @return {*}  {Promise<IServiceResponse<ICreateStoryRes>>}
   * @memberof StoryService
   */
  async createStory(
    userId: string,
    body: ICreateStoryReq,
  ): Promise<IServiceResponse<ICreateStoryRes>> {
    const { file } = body;
    if (!file) {
      throw new APIException(
        StoryErrorEnum.NO_FILE_IS_FOUND,
        'NO_FILE_IS_FOUND',
        HttpStatus.BAD_REQUEST,
      );
    }

    const fileType = body.file.mimetype.split('/')[0];
    const ext = file.originalname.substring(
      file.originalname.lastIndexOf('.'),
      file.originalname.length,
    );
    if (fileType !== 'image' && fileType !== 'video') {
      throw new APIException(
        StoryErrorEnum.INVALID_FILE_TYPE,
        'INVALID_FILE_TYPE',
        HttpStatus.BAD_REQUEST,
      );
    }

    const key = await this.helperService.azureBlobStorageService.generateFileKey(
      userId,
      ext,
      'story',
    );
    const s3ObjectUrl = await this.helperService.azureBlobStorageService.uploadToAzureBlob(
      file,
      key,
    );
    if (!s3ObjectUrl) {
      throw new APIException(
        StoryErrorEnum.ERROR_IN_UPLOADING_STORY,
        'ERROR_IN_UPLOADING_STORY',
        HttpStatus.CONFLICT,
      );
    }

    // save data to db
    const createStoryPayload: ICreateStory = {
      userId: userId,
      type: fileType,
      url: s3ObjectUrl,
      expireAt: Date.now() + sharedConfig.storyExpirationTime,
    };
    const newStory = await this.storyRepository.createStory(createStoryPayload);
    if (newStory) {
      newStory.url =
        await this.helperService.azureBlobStorageService.generatePreSignedUrl(
          newStory.url,
          'original/story',
        );
      return successResponse(null, newStory);
    }
    throw new APIException(
      StoryErrorEnum.ERROR_IN_CREATING_NEW_STORY,
      'ERROR_IN_CREATING_NEW_STORY',
      HttpStatus.CONFLICT,
    );
  }

  /**
   * Get all stories of a logged in user
   *
   * @param {string} userId
   * @return {*}  {Promise<IServiceResponse<IGetStoryRes[]>>}
   * @memberof StoryService
   */
  async myStory(userId: string): Promise<IServiceResponse<IGetStoryRes[]>> {
    const storyList = await this.storyRepository.myStory(userId);
    if (storyList.length > 0) {
      const mapped = await Promise.all(
        storyList.map(async (e) => {
          e.url =
            await this.helperService.azureBlobStorageService.generatePreSignedUrl(
              e.url,
              'original/story',
            );
          return e;
        }),
      );
      return successResponse(null, mapped);
    }
    return successResponse(null, []);
  }

  /**
   * Delete story
   *
   * @param {string} userId
   * @param {string} storyId
   * @return {*}  {Promise<IServiceResponse<IDeleteStoryRes>>}
   * @memberof StoryService
   */
  async deleteMyStoryById(
    userId: string,
    storyId: string,
  ): Promise<IServiceResponse<IDeleteStoryRes>> {
    const isDeleted = await this.storyRepository.deleteMyStoryById(
      userId,
      storyId,
    );
    if (isDeleted) {
      return successResponse(null, {
        message: 'Story has been deleted successfully',
      });
    }
    throw new APIException(
      StoryErrorEnum.STORY_CAN_NOT_BE_DELETED,
      'STORY_CAN_NOT_BE_DELETED',
      HttpStatus.CONFLICT,
    );
  }

  /**
   * View a specific story and update view count
   *
   * @param {string} uploaderId
   * @param {string} storyId
   * @param {string} viewerId
   * @return {*}  {Promise<IServiceResponse<IGetStoryRes>>}
   * @memberof StoryService
   */
  async getSingleStory(
    uploaderId: string,
    storyId: string,
    viewerId: string,
  ): Promise<IServiceResponse<IGetStoryRes>> {
    const storyData = await this.storyRepository.findOneByStoryId(
      storyId,
      uploaderId,
    );
    if (!storyData) {
      throw new APIException(
        StoryErrorEnum.INVALID_STORY,
        'INVALID_STORY',
        HttpStatus.BAD_REQUEST,
      );
    }
    if (uploaderId === viewerId) {
      storyData.url =
        await this.helperService.azureBlobStorageService.generatePreSignedUrl(
          storyData.url,
          'original/story',
        );
      return successResponse(null, storyData);
    }

    const isAllowedToView =
      await this.storyHelperRepository.isUserExistInMyBuddies(
        uploaderId,
        viewerId,
      );
    if (!isAllowedToView) {
      throw new APIException(
        StoryErrorEnum.INVALID_REQUEST,
        'INVALID_REQUEST',
        HttpStatus.CONFLICT,
      );
    }

    // increment view count and return updated story info
    const updatedStory = await this.storyRepository.getSingleStoryAndUpdateInfo(
      uploaderId,
      storyId,
      viewerId,
    );
    if (!updatedStory) {
      throw new APIException(
        StoryErrorEnum.ERROR_IN_STORY_VIEW,
        'ERROR_IN_STORY_VIEW',
        HttpStatus.CONFLICT,
      );
    }
    updatedStory.url =
      await this.helperService.azureBlobStorageService.generatePreSignedUrl(
        updatedStory.url,
        'original/story',
      );

    if (storyData?.totalView < updatedStory?.totalView) {
      const [storyUploader, storyViewer] = await Promise.all([
        await this.userRepository.findUser({
          id: uploaderId,
        }),
        await this.userRepository.findUser({
          id: viewerId,
        }),
      ]);

      // Notification Payload
      const payload = {
        recipient: storyUploader,
        createdBy: storyViewer,
        title: 'Your story got a view!',
        content: `${storyViewer?.name} viewed your story.`,
        module: NotificationModule.STORY,
        type: NotificationType.STORY_VIEW,
        category: NotificationCategory.STORY_VIEW,
        documentId: storyId,
      };

      await this.notificationHelperService.sendNotifications(payload);
    }

    return successResponse(null, updatedStory);
  }

  /**
   * Get the user list who viewed the story
   *
   * @param {string} userId
   * @param {string} storyId
   * @return {*}  {Promise<IServiceSuccessResponse<IStoryViewerList>>}
   * @memberof StoryService
   */
  async getViewerList(
    userId: string,
    storyId: string,
  ): Promise<IServiceSuccessResponse<IStoryViewerList>> {
    const storyViewers = await this.storyRepository.getViewerList(
      userId,
      storyId,
    );
    if (!storyViewers) {
      throw new APIException(
        StoryErrorEnum.ERROR_IN_GETTING_STORY_VIEWERS,
        'ERROR_IN_GETTING_STORY_VIEWERS',
        HttpStatus.CONFLICT,
      );
    }
    return storyViewers.length
      ? successResponse(null, storyViewers[0])
      : successResponse(null, null);
  }

  /**
   * Get the stories of connected fit buddies
   *
   * @param {string} viewerId
   * @param {*} [skip=sharedConfig.fetchStoriesDefaultSkip]
   * @param {*} [limit=sharedConfig.fetchStoriesDefaultLimit]
   * @return {*}  {Promise<IServiceResponse<IFetchStoryRes[]>>}
   * @memberof StoryService
   */
  async fetchStories(
    viewerId: string,
    offset = sharedConfig.fetchStoriesDefaultOffset,
    limit = sharedConfig.fetchStoriesDefaultLimit,
  ): Promise<IServiceResponse<IFetchStoryRes[]>> {
    if (limit > sharedConfig.fetchStoriesMaxLimit) {
      limit = sharedConfig.fetchStoriesDefaultLimit;
    }
    const myBuddies = await this.storyHelperRepository.getMyFitBuddysIds(
      viewerId,
      // offset,
      // limit,
    );

    if (!myBuddies) {
      throw new APIException(
        StoryErrorEnum.ERROR_IN_GETTING_FIT_BUDDIES,
        'ERROR_IN_GETTING_FIT_BUDDIES',
        HttpStatus.CONFLICT,
      );
    }
    const userStories = myBuddies.length
      ? await this.storyRepository.fetchStories(myBuddies, offset, limit)
      : [];

    // mapping to generate presigned url for every story
    const mappedResult: IFetchStoryRes[] = await Promise.all(
      userStories.map(async (e) => {
        const stories = await Promise.all(
          e.stories.map(async (storyInfo) => {
            storyInfo.url =
              await this.helperService.azureBlobStorageService.generatePreSignedUrl(
                storyInfo.url,
                'original/story',
              );
            return storyInfo;
          }),
        );
        return { ...e, stories };
      }),
    );

    return successResponse(null, mappedResult);
  }
}
