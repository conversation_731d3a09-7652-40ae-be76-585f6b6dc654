export enum ActivityTypeEnum {
  SEDENTARY = 'SEDENTARY',
  LIGHTLY_ACTIVE = 'LIGHTLY_ACTIVE',
  MODERATELY_ACTIVE = 'MODERATELY_ACTIVE',
  VERY_ACTIVE = 'VERY_ACTIVE',
  EXTRA_ACTIVE = 'EXTRA_ACTIVE',
}

export class Activity {
  id: string;
  name: string;
  activityType: ActivityTypeEnum;
  description: string;
  value: number;
}

export class UserDailyActivity {
  userId: string;
  date: string;
  profileViewCount: number;
  profileViewedIds: string[];
  spotRequestCount: number;
  activityDuration: number;
  activityMetRequirement: boolean; // Did they meet minimum minutes?
  activitySessionHistory: {       // Track daily activity
    startTime: Date;
    endTime: Date;
    minutes: number;             // Minutes active that day
  }[];
  createdAt?: Date;
  updatedAt?: Date;
}
