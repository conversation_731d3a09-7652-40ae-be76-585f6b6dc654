import { IAzureBlobStorageService } from './azureBlobService/azureBlobStorage.service.interface';
import { IFcmService } from './fcmService/fcm.service.interface';
import { IMailService } from './mailService/mail.service.interface';
import { IS3FileUploadService } from './s3Service/s3.service.interface';
import { IServiceResponse } from './serviceResponse/service.response.interface';
import { ISmsServive } from './smsService/sms.service.interface';

export abstract class Helper {
  serviceResponse: IServiceResponse;
  mailService: IMailService;
  fcmService: IFcmService;
  s3FileUploadService: IS3FileUploadService;
  azureBlobStorageService: IAzureBlobStorageService;
  smsService: ISmsServive;
}
