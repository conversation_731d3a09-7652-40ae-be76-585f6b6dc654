import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiProperty,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { sharedConfig } from 'config/shared';
import { IUserPointsTransTypeEnum } from 'models';
import { RolesGuard } from 'src/authentication/guards/auth.guard';
import { User as UserInfo } from 'src/decorators/auth.decorator';
import { User } from 'src/entity/user';
import { UserPointsService } from '../services';
import { GetUserPointsSuccessResponseDtoForUser } from './dtos/get-user-points';
import { GetUserPointsHistorySuccessResponseDtoForUser } from './dtos/get-user-points-history';
import { GetUserLeaderboardSuccessResponseDtoForUser } from './dtos/get-user-points-leaderboards';

class UpdatePoints {
  @ApiProperty({ enum: IUserPointsTransTypeEnum })
  type: IUserPointsTransTypeEnum;

  @ApiProperty()
  points: number;
}

@ApiTags('Points API - User')
@ApiBearerAuth()
@UseGuards(new RolesGuard(['user']))
@Controller('')
export class UserPointsController {
  constructor(private readonly userPointsService: UserPointsService) {}

  @ApiResponse({ type: GetUserPointsSuccessResponseDtoForUser })
  @ApiOperation({ summary: 'Get User Points ' })
  @Get('user/points')
  async getPoints(@UserInfo() user: User) {
    return await this.userPointsService.myPoints(user.id);
  }

  @Post('update-points')
  async addPoints(@Body() body: UpdatePoints, @UserInfo() user: User) {
    return await this.userPointsService.updatePoints(user.id, body);
  }

  @ApiResponse({ type: GetUserLeaderboardSuccessResponseDtoForUser })
  @ApiOperation({ summary: 'Get leaderboard of all users' })
  @ApiQuery({ name: 'offset', type: Number, example: 0, required: false })
  @ApiQuery({ name: 'limit', type: Number, example: 10, required: false })
  @Get('user/leaderboard')
  async leaderBoard(
    @Query('offset') offset = sharedConfig.defaultSkip,
    @Query('limit') limit = sharedConfig.defaultLimit,
  ) {
    return await this.userPointsService.leaderBoard(offset, limit);
  }

  @ApiResponse({ type: GetUserPointsHistorySuccessResponseDtoForUser })
  @ApiOperation({ summary: 'Get points history of users' })
  @ApiQuery({ name: 'offset', type: Number, example: 0, required: false })
  @ApiQuery({ name: 'limit', type: Number, example: 10, required: false })
  @ApiQuery({
    name: 'filter',
    type: String,
    enum: ['all', 'monthly'],
    example: 'all',
    required: false,
    description: 'Filter by all points history or only current month',
  })
  @Get('user/points-history')
  async pointsHistory(
    @UserInfo() user: User,
    @Query('offset') offset = sharedConfig.defaultSkip,
    @Query('limit') limit = sharedConfig.defaultLimit,
    @Query('filter') filter = 'all', // Default to 'all'
  ) {
    return await this.userPointsService.userPointsHistory(
      user.id,
      offset,
      limit,
      filter,
    );
  }
}
