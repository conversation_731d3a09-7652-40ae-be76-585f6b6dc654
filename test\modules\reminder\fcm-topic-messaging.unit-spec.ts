import { Test, TestingModule } from '@nestjs/testing';
import { FcmService } from 'src/helper/fcmService';
import { TopicFcmRequest } from 'src/helper/fcmService/fcm.service.interface';
import * as admin from 'firebase-admin';

// Mock Firebase Admin
jest.mock('firebase-admin', () => ({
  messaging: jest.fn(() => ({
    sendToTopic: jest.fn(),
    subscribeToTopic: jest.fn(),
    unsubscribeFromTopic: jest.fn(),
  })),
}));

describe('FcmService - Topic Messaging', () => {
  let service: FcmService;
  let mockMessaging: jest.Mocked<admin.messaging.Messaging>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FcmService],
    }).compile();

    service = module.get<FcmService>(FcmService);
    mockMessaging = admin.messaging() as jest.Mocked<admin.messaging.Messaging>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('sendToTopic', () => {
    const mockTopicRequest: TopicFcmRequest = {
      title: 'Daily Reminder',
      body: 'Time for your workout!',
      topic: 'all-reminders',
      documentId: 'reminder-123',
      data: { type: 'reminder', id: 'reminder-123' },
      isHighPriority: true,
      ttl: 3600,
    };

    it('should successfully send notification to topic with proper configuration', async () => {
      const mockResponse = 'msg-123';
      mockMessaging.send.mockResolvedValue(mockResponse);

      const result = await service.sendToTopic(mockTopicRequest);

      expect(result).toBe('msg-123');
      expect(mockMessaging.send).toHaveBeenCalledWith(
        expect.objectContaining({
          notification: {
            title: 'Daily Reminder',
            body: 'Time for your workout!',
          },
          data: expect.objectContaining({
            type: 'reminder',
            id: 'reminder-123',
            documentId: 'reminder-123',
          }),
          android: expect.objectContaining({
            notification: expect.objectContaining({
              icon: 'ic_notification',
              color: '#FF6B35',
              sound: 'default',
              channelId: 'reminders',
              priority: 'high',
            }),
            ttl: 3600000, // TTL in milliseconds
          }),
          apns: expect.objectContaining({
            payload: expect.objectContaining({
              aps: expect.objectContaining({
                alert: {
                  title: 'Daily Reminder',
                  body: 'Time for your workout!',
                },
                sound: 'default',
                badge: 1,
              }),
            }),
            headers: expect.objectContaining({
              'apns-priority': '10',
              'apns-expiration': expect.any(String),
            }),
          }),
          topic: 'all-reminders',
        })
      );
    });

    it('should handle missing optional parameters gracefully', async () => {
      const minimalRequest: TopicFcmRequest = {
        title: 'Simple Reminder',
        body: 'Basic message',
        topic: 'all-reminders',
        documentId: 'reminder-456',
        data: { type: 'reminder' },
      };

      const mockResponse = 'msg-456';
      mockMessaging.send.mockResolvedValue(mockResponse);

      const result = await service.sendToTopic(minimalRequest);

      expect(result).toBe('msg-456');
      expect(mockMessaging.send).toHaveBeenCalledWith(
        expect.objectContaining({
          notification: {
            title: 'Simple Reminder',
            body: 'Basic message',
          },
          data: expect.objectContaining({
            title: 'Simple Reminder',
            body: 'Basic message',
          }),
          topic: 'all-reminders',
        })
      );
    });

    it('should handle FCM errors gracefully', async () => {
      const fcmError = new Error('FCM quota exceeded');
      fcmError.name = 'messaging/quota-exceeded';
      mockMessaging.sendToTopic.mockRejectedValue(fcmError);

      const result = await service.sendToTopic(mockTopicRequest);

      expect(result).toBe(false);
      expect(mockMessaging.sendToTopic).toHaveBeenCalledWith(
        'all-reminders',
        expect.any(Object)
      );
    });

    it('should set correct Android notification priority for high priority messages', async () => {
      const highPriorityRequest = { ...mockTopicRequest, isHighPriority: true };
      const mockResponse = { messageId: 789 }; // messageId should be number
      mockMessaging.sendToTopic.mockResolvedValue(mockResponse);

      await service.sendToTopic(highPriorityRequest);

      expect(mockMessaging.sendToTopic).toHaveBeenCalledWith(
        'all-reminders',
        expect.objectContaining({
          android: expect.objectContaining({
            notification: expect.objectContaining({
              priority: 'high',
            }),
          }),
        })
      );
    });

    it('should set correct Android notification priority for normal priority messages', async () => {
      const normalPriorityRequest = { ...mockTopicRequest, isHighPriority: false };
      const mockResponse = { messageId: 'msg-101' };
      mockMessaging.sendToTopic.mockResolvedValue(mockResponse);

      await service.sendToTopic(normalPriorityRequest);

      expect(mockMessaging.sendToTopic).toHaveBeenCalledWith(
        'all-reminders',
        expect.objectContaining({
          android: expect.objectContaining({
            notification: expect.objectContaining({
              priority: 'normal',
            }),
          }),
        })
      );
    });

    it('should include custom data in the message payload', async () => {
      const requestWithCustomData = {
        ...mockTopicRequest,
        data: { 
          type: 'reminder',
          id: 'reminder-123',
          customField: 'customValue',
          userId: 'user-456'
        },
      };
      const mockResponse = { messageId: 'msg-custom' };
      mockMessaging.sendToTopic.mockResolvedValue(mockResponse);

      await service.sendToTopic(requestWithCustomData);

      expect(mockMessaging.sendToTopic).toHaveBeenCalledWith(
        'all-reminders',
        expect.objectContaining({
          data: expect.objectContaining({
            type: 'reminder',
            id: 'reminder-123',
            customField: 'customValue',
            userId: 'user-456',
            documentId: 'reminder-123',
          }),
        })
      );
    });
  });

  describe('subscribeNotificationTopic', () => {
    it('should successfully subscribe token to topic', async () => {
      const mockResponse = { successCount: 1, failureCount: 0, errors: [] };
      mockMessaging.subscribeToTopic.mockResolvedValue(mockResponse);

      const result = await service.subscribeNotificationTopic('token-123', 'all-reminders');

      expect(result).toBe(true);
      expect(mockMessaging.subscribeToTopic).toHaveBeenCalledWith(
        ['token-123'],
        'all-reminders'
      );
    });

    it('should handle subscription failures', async () => {
      const mockResponse = { successCount: 0, failureCount: 1, errors: [{ error: 'Invalid token' }] };
      mockMessaging.subscribeToTopic.mockResolvedValue(mockResponse);

      const result = await service.subscribeNotificationTopic('invalid-token', 'all-reminders');

      expect(result).toBe(false);
      expect(mockMessaging.subscribeToTopic).toHaveBeenCalledWith(
        ['invalid-token'],
        'all-reminders'
      );
    });

    it('should handle FCM service errors', async () => {
      const fcmError = new Error('FCM service unavailable');
      mockMessaging.subscribeToTopic.mockRejectedValue(fcmError);

      const result = await service.subscribeNotificationTopic('token-123', 'all-reminders');

      expect(result).toBe(false);
      expect(mockMessaging.subscribeToTopic).toHaveBeenCalledWith(
        ['token-123'],
        'all-reminders'
      );
    });
  });

  describe('unsubscribeNotificationTopic', () => {
    it('should successfully unsubscribe token from topic', async () => {
      const mockResponse = { successCount: 1, failureCount: 0, errors: [] };
      mockMessaging.unsubscribeFromTopic.mockResolvedValue(mockResponse);

      const result = await service.unsubscribeNotificationTopic('token-123', 'all-reminders');

      expect(result).toBe(true);
      expect(mockMessaging.unsubscribeFromTopic).toHaveBeenCalledWith(
        ['token-123'],
        'all-reminders'
      );
    });

    it('should handle unsubscription failures', async () => {
      const mockResponse = { successCount: 0, failureCount: 1, errors: [{ error: 'Invalid token' }] };
      mockMessaging.unsubscribeFromTopic.mockResolvedValue(mockResponse);

      const result = await service.unsubscribeNotificationTopic('invalid-token', 'all-reminders');

      expect(result).toBe(false);
      expect(mockMessaging.unsubscribeFromTopic).toHaveBeenCalledWith(
        ['invalid-token'],
        'all-reminders'
      );
    });

    it('should handle FCM service errors', async () => {
      const fcmError = new Error('FCM service unavailable');
      mockMessaging.unsubscribeFromTopic.mockRejectedValue(fcmError);

      const result = await service.unsubscribeNotificationTopic('token-123', 'all-reminders');

      expect(result).toBe(false);
      expect(mockMessaging.unsubscribeFromTopic).toHaveBeenCalledWith(
        ['token-123'],
        'all-reminders'
      );
    });
  });
});
