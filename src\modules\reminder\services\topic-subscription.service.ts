import { Injectable } from '@nestjs/common';
import { FcmService } from 'src/helper/fcmService';

export interface TopicSubscriptionResult {
  success: boolean;
  message: string;
  failedTokens?: string[];
}

@Injectable()
export class TopicSubscriptionService {
  private readonly REMINDER_TOPIC = 'all-reminders';
  
  constructor(private readonly fcmService: FcmService) {}

  /**
   * Subscribe a user's FCM token to the all-reminders topic
   */
  async subscribeToReminders(fcmToken: string): Promise<TopicSubscriptionResult> {
    try {
      if (!fcmToken || fcmToken.trim() === '') {
        return {
          success: false,
          message: 'Invalid FCM token provided'
        };
      }

      await this.fcmService.subscribeNotificationTopic(fcmToken, this.REMINDER_TOPIC);
      
      return {
        success: true,
        message: `Successfully subscribed to ${this.REMINDER_TOPIC} topic`
      };
    } catch (error) {
      console.error('Failed to subscribe to reminders topic:', error.message);
      return {
        success: false,
        message: `Failed to subscribe: ${error.message}`
      };
    }
  }

  /**
   * Subscribe multiple FCM tokens to the all-reminders topic
   */
  async subscribeMultipleToReminders(fcmTokens: string[]): Promise<TopicSubscriptionResult> {
    try {
      if (!fcmTokens || fcmTokens.length === 0) {
        return {
          success: false,
          message: 'No FCM tokens provided'
        };
      }

      // Filter out invalid tokens
      const validTokens = fcmTokens.filter(token => token && token.trim() !== '');
      
      if (validTokens.length === 0) {
        return {
          success: false,
          message: 'No valid FCM tokens provided'
        };
      }

      await this.fcmService.subscribeNotificationTopic(validTokens, this.REMINDER_TOPIC);
      
      return {
        success: true,
        message: `Successfully subscribed ${validTokens.length} tokens to ${this.REMINDER_TOPIC} topic`
      };
    } catch (error) {
      console.error('Failed to subscribe multiple tokens to reminders topic:', error.message);
      return {
        success: false,
        message: `Failed to subscribe: ${error.message}`,
        failedTokens: fcmTokens
      };
    }
  }

  /**
   * Unsubscribe a user's FCM token from the all-reminders topic
   */
  async unsubscribeFromReminders(fcmToken: string): Promise<TopicSubscriptionResult> {
    try {
      if (!fcmToken || fcmToken.trim() === '') {
        return {
          success: false,
          message: 'Invalid FCM token provided'
        };
      }

      await this.fcmService.unsubscribeNotificationTopic(fcmToken, this.REMINDER_TOPIC);
      
      return {
        success: true,
        message: `Successfully unsubscribed from ${this.REMINDER_TOPIC} topic`
      };
    } catch (error) {
      console.error('Failed to unsubscribe from reminders topic:', error.message);
      return {
        success: false,
        message: `Failed to unsubscribe: ${error.message}`
      };
    }
  }

  /**
   * Unsubscribe multiple FCM tokens from the all-reminders topic
   */
  async unsubscribeMultipleFromReminders(fcmTokens: string[]): Promise<TopicSubscriptionResult> {
    try {
      if (!fcmTokens || fcmTokens.length === 0) {
        return {
          success: false,
          message: 'No FCM tokens provided'
        };
      }

      // Filter out invalid tokens
      const validTokens = fcmTokens.filter(token => token && token.trim() !== '');
      
      if (validTokens.length === 0) {
        return {
          success: false,
          message: 'No valid FCM tokens provided'
        };
      }

      await this.fcmService.unsubscribeNotificationTopic(validTokens, this.REMINDER_TOPIC);
      
      return {
        success: true,
        message: `Successfully unsubscribed ${validTokens.length} tokens from ${this.REMINDER_TOPIC} topic`
      };
    } catch (error) {
      console.error('Failed to unsubscribe multiple tokens from reminders topic:', error.message);
      return {
        success: false,
        message: `Failed to unsubscribe: ${error.message}`,
        failedTokens: fcmTokens
      };
    }
  }

  /**
   * Get the reminder topic name
   */
  getReminderTopicName(): string {
    return this.REMINDER_TOPIC;
  }

  /**
   * Batch subscribe users when they enable reminder notifications
   * This should be called when users opt-in to reminder notifications
   */
  async batchSubscribeNewUsers(fcmTokens: string[]): Promise<TopicSubscriptionResult> {
    if (fcmTokens.length === 0) {
      return {
        success: true,
        message: 'No tokens to subscribe'
      };
    }

    // Process in batches of 1000 (FCM limit)
    const batchSize = 1000;
    const batches = [];
    
    for (let i = 0; i < fcmTokens.length; i += batchSize) {
      batches.push(fcmTokens.slice(i, i + batchSize));
    }

    let totalSuccess = 0;
    const failedTokens: string[] = [];

    for (const batch of batches) {
      try {
        const result = await this.subscribeMultipleToReminders(batch);
        if (result.success) {
          totalSuccess += batch.length;
        } else {
          failedTokens.push(...batch);
        }
      } catch (error) {
        console.error('Batch subscription failed:', error.message);
        failedTokens.push(...batch);
      }
    }

    return {
      success: failedTokens.length === 0,
      message: `Subscribed ${totalSuccess} out of ${fcmTokens.length} tokens`,
      failedTokens: failedTokens.length > 0 ? failedTokens : undefined
    };
  }
}
