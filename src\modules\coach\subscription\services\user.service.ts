import { forwardRef, Inject, Injectable } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { Helper } from "src/helper/helper.interface";
import { deepCasting } from "src/internal/casting/object.casting";
import { throwConflictErrIf, throwNotFoundErr, throwUnauthorizedErrIf } from "src/internal/exception/api.exception.ext";
import { COACH_PROGRAM_NOT_FOUND, NO_SUBSCRIPTION_FOUND, SUBSCRIPTION_CANCELLED_ALREADY, SUBSCRIPTION_CANCELLED_BY_COACH, THIS_COACH_DOES_NOT_OWN_THIS_PROGRAM, USER_ALREADY_HAS_ACTIVE_SUBSCRIPTION, USER_DOES_NOT_OWN_THIS_SUBSCRIPTION } from "../../common/const/coach.const";
import { CoachSubscriptionCancelStatus, CoachSubscriptionEntity, SubscriptionStatus } from "../../common/entities/coach-program.entity";
import { UserPaymentProviderForUser } from "../../payment/providers/user.provider";
import { CoachProgramRatingProviderForUser } from "../../program-rating/providers/user.provider";
import { ProgramProviderForUser } from "../../program/providers/user.provider";
import { UserInfoProviderForUser } from "../../user-info/providers/user.provider";
import { VerifiedProfileProviderForUser } from "../../verified-profile/providers/user.provider";
import { CancelSubscriptionRequestDtoForUser } from "../dtos/user-cancel-subscription.dto";
import { GetSubscriptionResponseDtoForUser, GetSubscriptionSuccessResponseDtoForUser } from "../dtos/user-get-subscription.dto";
import { GetSubscriptionsResponseDtoForUser, GetSubscriptionsSuccessResponseDtoForUser } from "../dtos/user-get-subscriptions.dto";
import { SubscriptionHistoryResponseDtoForUser, SubscriptionHistorySuccessResponseDtoForUser } from "../dtos/user-subscription-history.dto";
import { SubscriptionRepositoryForUser } from "../repositories/user.repository";
import { TimeUtils } from "../utils/time.utils";

@Injectable()
export class SubscriptionServiceForUser {

  constructor(
    private readonly repository: SubscriptionRepositoryForUser,
    private helper: Helper,
    @Inject(forwardRef(() => VerifiedProfileProviderForUser))
    private readonly verifiedProfileProvider: VerifiedProfileProviderForUser,
    @Inject(forwardRef(() => ProgramProviderForUser))
    private readonly programProvider: ProgramProviderForUser,
    @Inject(forwardRef(() => UserInfoProviderForUser))
    private readonly userInfoProvider: UserInfoProviderForUser,
    @Inject(forwardRef(() => CoachProgramRatingProviderForUser))
    private readonly programRatingProvider: CoachProgramRatingProviderForUser,
    private eventEmitter: EventEmitter2,
    @Inject(forwardRef(() => UserPaymentProviderForUser))
    private readonly paymentProvider: UserPaymentProviderForUser
  ) { }

  async getSingleSubscriptionDetails(userId: string, subscriptionId: string): Promise<GetSubscriptionSuccessResponseDtoForUser> {
    const subscription = await this.repository.getSubscriptionById(subscriptionId);
    throwNotFoundErr(!subscription, 'No subscription found', NO_SUBSCRIPTION_FOUND);

    const invalidOwnership = subscription.userId !== userId;
    throwUnauthorizedErrIf(invalidOwnership, 'Invalid ownership', USER_DOES_NOT_OWN_THIS_SUBSCRIPTION);

    const responseDto = deepCasting(GetSubscriptionResponseDtoForUser, subscription);

    const now = new Date();
    responseDto.isCompleted = subscription.expirationDate ? new Date(subscription.expirationDate) <= now : false;

    const programDetails = await this.programProvider.getSingleCoachProgram(subscription.programId);
    responseDto.programName = programDetails.title;
    responseDto.programDurationCount = programDetails.durationCount;
    responseDto.programDurationTerm = programDetails.durationTerm;
    responseDto.oneTimePrice = programDetails.oneTimePrice;
    responseDto.dailyPrice = programDetails.dailyPrice;
    responseDto.weeklyPrice = programDetails.weeklyPrice;
    responseDto.monthlyPrice = programDetails.monthlyPrice;
    


    const coachProfile = await this.verifiedProfileProvider.getSingleCoachProfile(subscription.coachId);
    responseDto.coachName = coachProfile.legalName;
    responseDto.coachUserId = coachProfile.userId;
    responseDto.coachImage = coachProfile.media;

    const ratingEntity = await this.programRatingProvider.getProgramRatingOfUser(userId, subscription.programId);
    responseDto.programRating = ratingEntity?.rating;
    responseDto.programRatingComment = ratingEntity?.comment;
    responseDto.programRatingId = ratingEntity?.id;

    const paymentTerm = await this.paymentProvider.getPaymentTermOfUser(userId, subscriptionId)
    responseDto.paymentTerm=paymentTerm
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getMultipleSubscriptionDetails(userId: string, offset: number, limit: number): Promise<GetSubscriptionsSuccessResponseDtoForUser> {
    const subscriptions = await this.repository.getSubscriptionsByUserId(userId, offset, limit);
    const programInfos = await this.programProvider.getMultipleCoachPrograms(subscriptions.map((subscription) => subscription.programId));
    const coachProfiles = await this.verifiedProfileProvider.getMultipleCoachProfiles(subscriptions.map((subscription) => subscription.coachId));
    
    const responseDtos = subscriptions.map((subscription) => deepCasting(GetSubscriptionsResponseDtoForUser, subscription));
    

    for (let index = 0; index < subscriptions.length; index++) {
      const subscription = subscriptions[index];
      const dto = responseDtos[index];
      const now = new Date();
    
      dto.isCompleted = subscription.expirationDate 
        ? new Date(subscription.expirationDate) <= now
        : false;
    
      const programInfo = programInfos.find(program => program.id === dto.programId);
      dto.programName = programInfo.title;
      dto.programDurationCount = programInfo.durationCount;
      dto.programDurationTerm = programInfo.durationTerm;
      dto.oneTimePrice = programInfo.oneTimePrice;
      dto.dailyPrice = programInfo.dailyPrice;
      dto.weeklyPrice = programInfo.weeklyPrice;
      dto.monthlyPrice = programInfo.monthlyPrice;

      // for (const profile of coachProfiles) {
      //   console.log('Checking profile id:', profile?.id, 'against coach id:', dto.coachId);
      // }
      
    
      const profileInfo = coachProfiles?.find(profile => profile?.id === dto.coachId);
      if (profileInfo) {
        dto.coachName = profileInfo.legalName;
        dto.coachImage = profileInfo.media;
        dto.coachUserId = profileInfo.userId;
      }
    
      const paymentTerm = await this.paymentProvider.getPaymentTermOfUser(userId, subscription.id);
      dto.paymentTerm = paymentTerm;

    }
    

   

    return this.helper.serviceResponse.successResponse(responseDtos);
  }

  async getLifetimeSubscriptionHistory(userId: string): Promise<SubscriptionHistorySuccessResponseDtoForUser> {
    const responseDto = new SubscriptionHistoryResponseDtoForUser();
    responseDto.totalInProgress = await this.repository.countOfInprogressSubscriptions(userId);
    responseDto.totalCompleted = await this.repository.countOfClosedSubscriptions(userId);
    responseDto.totalSubscriptions = responseDto.totalInProgress + responseDto.totalCompleted;
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async cancelSubscriptionTriggeredByUser(userId: string, subscriptionId: string, reqDto: CancelSubscriptionRequestDtoForUser): Promise<GetSubscriptionSuccessResponseDtoForUser> {
    let subscription = await this.repository.getSubscriptionById(subscriptionId);
    throwNotFoundErr(!subscription, 'No subscription found', NO_SUBSCRIPTION_FOUND);


    const invalidOwnership = subscription.userId !== userId;
    throwUnauthorizedErrIf(invalidOwnership, 'Invalid ownership', USER_DOES_NOT_OWN_THIS_SUBSCRIPTION);

    // const isAlreadyCanceled = subscription.cancelStatus !== null;
    // throwConflictErrIf(isAlreadyCanceled, "Subscription is already canceled", SUBSCRIPTION_CANCELLED_ALREADY);

    const cancelByUser = subscription.cancelStatus === CoachSubscriptionCancelStatus.CanceledByUser;
    throwConflictErrIf(cancelByUser, 'Subscription already canceled by user', SUBSCRIPTION_CANCELLED_ALREADY);
    
    const cancelByCoach = subscription.cancelStatus === CoachSubscriptionCancelStatus.CanceledByCoach
    throwConflictErrIf(cancelByCoach,'Subscription already canceled by coach', SUBSCRIPTION_CANCELLED_BY_COACH);

  

    subscription = await this.repository.cancelSubscriptionTriggeredByUser(subscriptionId, reqDto.reason);
    const responseDto = deepCasting(GetSubscriptionResponseDtoForUser, subscription);

    const programDetails = await this.programProvider.getSingleCoachProgram(subscription.programId);
    responseDto.programName = programDetails.title;
    responseDto.programDurationCount = programDetails.durationCount;
    responseDto.programDurationTerm = programDetails.durationTerm;

    const coachProfile =
      await this.verifiedProfileProvider.getSingleCoachProfile(
        subscription.coachId,
      );
    responseDto.coachName = coachProfile.legalName;
    responseDto.coachImage = coachProfile.media;
    responseDto.coachUserId = coachProfile.userId;

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async subscribeToCoachProgram(
    userId: string,
    coachId: string,
    programId: string,
  ): Promise<GetSubscriptionSuccessResponseDtoForUser> {
    const coachProgram = await this.programProvider.getSingleCoachProgram(
      programId,
    );
    throwNotFoundErr(
      !coachProgram,
      'Coach program not found',
      COACH_PROGRAM_NOT_FOUND,
    );

    const isCoachIdValid = coachProgram.coachId === coachId;
    throwConflictErrIf(
      !isCoachIdValid,
      'This coach does not own this program',
      THIS_COACH_DOES_NOT_OWN_THIS_PROGRAM,
    );

    const existingSubscription = await this.repository.findSubscriptionByUserAndProgram(userId, programId);


    if (existingSubscription) {
      if (
        // existingSubscription.status === SubscriptionStatus.SUCCESSFUL
        existingSubscription.isFirstPaymentDone
        
      ) {
        throwConflictErrIf(
          // existingSubscription.status === SubscriptionStatus.SUCCESSFUL,
          existingSubscription.isFirstPaymentDone,
          'User already has an active subscription',
          USER_ALREADY_HAS_ACTIVE_SUBSCRIPTION,
        );
      }
  
      if (
        // existingSubscription.status === SubscriptionStatus.INITIATED
        !existingSubscription.isFirstPaymentDone
      ) {
        const responseDto = deepCasting(GetSubscriptionResponseDtoForUser, existingSubscription);
        responseDto.programName = coachProgram.title;
        responseDto.programDurationCount = coachProgram.durationCount;
        responseDto.programDurationTerm = coachProgram.durationTerm;
  
        const coachProfile = await this.verifiedProfileProvider.getSingleCoachProfile(existingSubscription.coachId);
        responseDto.coachName = coachProfile?.legalName ?? '';
        responseDto.coachImage = coachProfile?.media ?? [];

        // console.log('event emitter for coach program')
        // this.eventEmitter.emit('program.subscription', {
        //   userId,
        //   programId,
          
        // });
  
        return this.helper.serviceResponse.successResponse(responseDto);
      }
    }
   

    const currentDate = new Date();
    let subscriptionEntity = new CoachSubscriptionEntity();
    subscriptionEntity.userId = userId;
    subscriptionEntity.coachId = coachId;
    subscriptionEntity.programId = programId;
    subscriptionEntity.isActive = true;
    subscriptionEntity.status = SubscriptionStatus.INITIATED;
    subscriptionEntity.isPaymentNeeded = true;
    subscriptionEntity.isFirstPaymentDone = false;
    subscriptionEntity.userTotalPaid = 0;
    subscriptionEntity.coachAlreadyGot = 0;
    subscriptionEntity.subscriptionDate = currentDate;
    subscriptionEntity.nextPaymentDate = currentDate;
    subscriptionEntity.expirationDate = TimeUtils.calculateExpirationDate(
      currentDate,
      coachProgram.durationCount,
      coachProgram.durationTerm,
    );

    subscriptionEntity = await this.repository.createNewSubscription(
      subscriptionEntity,
    );
    const responseDto = deepCasting(
      GetSubscriptionResponseDtoForUser,
      subscriptionEntity,
    );

    responseDto.programName = coachProgram.title;
    responseDto.programDurationCount = coachProgram.durationCount;
    responseDto.programDurationTerm = coachProgram.durationTerm;

    const coachProfile = await this.verifiedProfileProvider.getSingleCoachProfile(subscriptionEntity.coachId);
    responseDto.coachName = coachProfile?.legalName ?? '';
    responseDto.coachImage = coachProfile?.media ?? [];


    // await this.programProvider.incrementTotalSubscriptionCountByOne(programId);
    // await this.verifiedProfileProvider.incrementTotalSubscriptionCountByOne(coachId);

    return this.helper.serviceResponse.successResponse(responseDto);
  }

}