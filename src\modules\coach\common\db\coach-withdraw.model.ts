import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import {
  CoachWithdrawEntity,
  CoachWithdrawMobileNumberEntity,
  CoachWithdrawPaymentStatus,
  CoachWithdrawReviewStatus,
} from '../entities/coach-withdraw.entity';

const CoachWithdrawEntitySchema = new Schema<CoachWithdrawEntity>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
      required: true,
    },
    coachId: {
      type: String,
      required: true,
    },
    mobileNumber: {
      type: String,
      required: true,
    },
    moneyWithdraw: {
      type: Number,
      required: true,
    },
    bkashTrxId: {
      type: String,
      required: false,
    },
    reviewStatus: {
      type: String,
      enum: CoachWithdrawReviewStatus,
      required: true,
    },
    paymentStatus: {
      type: String,
      enum: CoachWithdrawPaymentStatus,
      required: true,
    },
    reviewComment: {
      type: String,
      required: false,
      default: '',
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const CoachReWithdrawEntityModel = model<CoachWithdrawEntity>(
  'coach_withdraw_entity',
  CoachWithdrawEntitySchema,
);
export { CoachReWithdrawEntityModel };

const CoachWithdrawMobileNumber = new Schema<CoachWithdrawMobileNumberEntity>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    coachId: {
      type: String,
      required: true,
    },
    mobileNumber: {
      type: String,
      required: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const CoachReWithdrawMobileNumberEntityModel =
  model<CoachWithdrawMobileNumberEntity>(
    'coach_withdraw_mobile_entity',
    CoachWithdrawMobileNumber,
  );
export { CoachReWithdrawMobileNumberEntityModel };

