import { HttpStatus, Injectable } from '@nestjs/common';
import { UpdateVersionRequest, Version } from 'src/entity/version';
import { Helper } from 'src/helper/helper.interface';
import { APIException } from 'src/internal/exception/api.exception';
import { VersionRepository } from '../repositories/version.repository';

@Injectable()
export class VersionService {
  constructor(private versionRepo: VersionRepository, private helper: Helper) {}

  async getLatestVersion() {
    const version = await this.versionRepo.getLatestVersion();
    if (!version) {
      throw new APIException(
        'No version found',
        'NO_VERSION_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }
    return this.helper.serviceResponse.successResponse(version);
  }

  async createVersion(data: Version) {
    const existingVersion = await this.versionRepo.getVersion({
      version: data.version,
    });
     
    if (existingVersion) {
      throw new APIException(
        'Version already exists',
        'VERSION_ALREADY_EXISTS',
        HttpStatus.BAD_REQUEST,
      );
    }

    const version = await this.versionRepo.createVersion(data);
    if (!version) {
      throw new APIException(
        'Failed to create version',
        'FAILED_TO_CREATE_VERSION',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    return this.helper.serviceResponse.successResponse(version);
  }

  async updateVersion(versionId: string, data: UpdateVersionRequest) {
    if (data.version) {
      const existingVersion = await this.versionRepo.getVersion({
        version: data.version,
        id: { $ne: versionId },
      });
      
      if (existingVersion) {
        throw new APIException(
          'Version already exists',
          'VERSION_ALREADY_EXISTS',
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    const updatedVersion = await this.versionRepo.updateVersion(
      { id: versionId },
      data,
    );
    
    if (!updatedVersion) {
      throw new APIException(
        'Failed to update version',
        'FAILED_TO_UPDATE_VERSION',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    
    return this.helper.serviceResponse.successResponse(updatedVersion);
  }

  async deleteVersion(versionId: string) {
    const deleted = await this.versionRepo.deleteVersion({ id: versionId });
    
    if (!deleted) {
      throw new APIException(
        'Failed to delete version',
        'FAILED_TO_DELETE_VERSION',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    
    return this.helper.serviceResponse.successResponse({
      message: 'Version deleted successfully',
    });
  }
}
